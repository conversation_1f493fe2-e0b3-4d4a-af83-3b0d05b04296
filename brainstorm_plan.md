# Brainstorm Plan: Ana<PERSON><PERSON>timen Comprehensive untuk Reviews GoFood Merchant

## Analisis File yang Ada

### 1. Preprocessing.ipynb
- **Tujuan**: Preprocessing data ulasan dari file Excel
- **Proses**: 
  - Cleaning text (case folding, remove URL, hashtag, dll)
  - Normalisasi kata (singkatan ke bentuk lengkap)
  - Remove stopwords
  - Stemming menggunakan Sastrawi
- **Output**: File Excel dengan kolom 'ulasan_stemmed'

### 2. Olah_data1.ipynb  
- **Tujuan**: Machine Learning untuk klasifikasi sentimen
- **Proses**:
  - Labeling berdasarkan rating (1-2: Negatif, 4-5: Positif, 3: diabaikan)
  - TF-IDF vectorization dengan tuning
  - Model: Random Forest dan XGBoost
  - Hyperparameter tuning
  - Evaluasi model
- **Output**: Model terlatih dan evaluasi performa

### 3. reviews_gofood_Merchant.xlsx
- **Data**: Reviews pengguna aplikasi GoFood Merchant dari Google Play Store

## Rencana Notebook Baru yang Terstruktur

### Struktur Notebook Comprehensive:

#### BAGIAN 1: EKSPLORASI DATA AWAL (EDA)
1. **Import Libraries & Load Data**
2. **Analisis Deskriptif Dataset**
   - Jumlah review, distribusi rating
   - Analisis temporal (jika ada timestamp)
   - Statistik panjang teks
3. **Visualisasi Data**
   - Distribusi rating
   - Word cloud untuk setiap kategori sentimen
   - Analisis frekuensi kata

#### BAGIAN 2: PREPROCESSING TEKS YANG DISEMPURNAKAN
1. **Text Cleaning**
   - Case folding
   - Remove noise (URL, mention, hashtag)
   - Handle emoji dan special characters
2. **Normalisasi & Standardisasi**
   - Kamus normalisasi yang diperluas
   - Koreksi typo sederhana
3. **Tokenisasi & Filtering**
   - Remove stopwords (custom Indonesian)
   - Stemming dengan Sastrawi
   - Filter kata berdasarkan panjang minimum

#### BAGIAN 3: ANALISIS SENTIMEN MENDALAM
1. **Labeling Strategy**
   - Berbasis rating dengan justifikasi
   - Analisis distribusi label
2. **Feature Engineering**
   - TF-IDF dengan parameter optimal
   - N-gram analysis
   - Feature selection

#### BAGIAN 4: MACHINE LEARNING MODELING
1. **Model Comparison**
   - Random Forest (dengan tuning)
   - XGBoost (dengan tuning)
   - Tambahan: Naive Bayes, SVM
2. **Model Evaluation**
   - Cross-validation
   - Confusion matrix
   - Classification report
   - ROC curve analysis

#### BAGIAN 5: ANALISIS HASIL & INSIGHTS
1. **Feature Importance Analysis**
2. **Error Analysis**
3. **Business Insights**
4. **Rekomendasi Actionable**

#### BAGIAN 6: DEPLOYMENT PREPARATION
1. **Model Serialization**
2. **Prediction Function**
3. **Sample Predictions**

## Perbaikan dari Notebook Asli

### Preprocessing.ipynb → Improvements:
- Tambah EDA sebelum preprocessing
- Kamus normalisasi yang lebih comprehensive
- Validasi hasil preprocessing
- Visualisasi setiap tahap preprocessing

### Olah_data1.ipynb → Improvements:
- Tambah model comparison yang lebih sistematis
- Cross-validation yang proper
- Feature importance analysis
- Business insights yang actionable
- Error analysis yang mendalam

## Output yang Diharapkan

1. **Notebook Utama**: `Analisis_Sentimen_GoFood_Comprehensive.ipynb`
2. **Log Perubahan**: `changelog.md`
3. **Laporan Pengembangan**: `laporan_pengembangan.md`
4. **Model Files**: Serialized models untuk deployment
5. **Data Files**: Processed data untuk future use

## Timeline Eksekusi

1. **Setup & EDA** (30 menit)
2. **Preprocessing Enhanced** (45 menit)  
3. **Modeling & Evaluation** (60 menit)
4. **Analysis & Insights** (30 menit)
5. **Documentation** (15 menit)

Total estimasi: ~3 jam untuk notebook comprehensive
