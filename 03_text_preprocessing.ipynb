{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔤 03. Advanced Text Preprocessing for Indonesian\n", "\n", "**Tujuan**: Melakukan preprocessing teks bahasa Indonesia yang komprehensif dengan fokus pada:\n", "\n", "**Key Features**:\n", "1. 🧹 **Advanced Text Cleaning** - Pembersihan teks mendalam\n", "2. 🔄 **Slang Normalization** - Normalisasi bahasa gaul Indonesia\n", "3. ✂️ **Stemming & Lemmatization** - Menggunakan Sastrawi\n", "4. 🚫 **Stopword Removal** - Penghapusan kata tidak penting\n", "5. 🔍 **Quality Validation** - Validasi kualitas preprocessing\n", "6. 📊 **Before/After Analysis** - <PERSON><PERSON><PERSON>\n", "\n", "**Input**: `gofood_ulasan_preprocessed.xlsx`  \n", "**Output**: Enhanced preprocessed text dengan multiple stages\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "import string\n", "import pickle\n", "import json\n", "from collections import Counter\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "from datetime import datetime\n", "\n", "# Indonesian NLP libraries\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from Sastrawi.Stemmer.StemmerFactory import StemmerFactory\n", "from Sastrawi.StopWordRemover.StopWordRemoverFactory import StopWordRemoverFactory\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "\n", "# Download required NLTK data\n", "try:\n", "    nltk.data.find('corpora/stopwords')\n", "except LookupError:\n", "    nltk.download('stopwords')\n", "\n", "try:\n", "    nltk.data.find('tokenizers/punkt')\n", "except LookupError:\n", "    nltk.download('punkt')\n", "\n", "print(\"🔤 Text preprocessing libraries imported successfully!\")\n", "print(f\"🕐 Preprocessing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Load Data & Lexicon Resources"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "try:\n", "    df = pd.read_excel('gofood_ulasan_preprocessed.xlsx')\n", "    print(f\"✅ Dataset loaded: {len(df)} reviews\")\n", "    print(f\"📋 Columns: {list(df.columns)}\")\n", "    \n", "    # Identify text column\n", "    text_columns = [col for col in df.columns if 'ulasan' in col.lower()]\n", "    if text_columns:\n", "        main_text_col = text_columns[0]  # Use first text column\n", "        print(f\"📝 Using text column: {main_text_col}\")\n", "    else:\n", "        main_text_col = df.columns[0]\n", "        print(f\"⚠️ No 'ulasan' column found, using: {main_text_col}\")\n", "        \n", "except FileNotFoundError:\n", "    print(\"❌ Dataset file not found. Creating sample data...\")\n", "    sample_data = {\n", "        'ulasan': [\n", "            \"<PERSON><PERSON><PERSON> enak bgt dan cepat sampai, driver ramah sekali!\",\n", "            \"<PERSON><PERSON><PERSON><PERSON> buruk, makanan dingin dan ga enak sama sekali\",\n", "            \"<PERSON><PERSON><PERSON> sih, tapi agak lama nunggunya. Overall oke lah\",\n", "            \"Mantap jiwa! Gofood emang the best, recommended bgt\",\n", "            \"Ap<PERSON><PERSON> sering error, susah bgt mau order makanan\"\n", "        ],\n", "        'nilai': [5, 1, 3, 5, 2]\n", "    }\n", "    df = pd.DataFrame(sample_data)\n", "    main_text_col = 'ulasan'\n", "    print(f\"📝 Sample dataset created with {len(df)} reviews\")\n", "\n", "# Load lexicon resources\n", "try:\n", "    with open('lexicons/indonesian_lexicon_package.pkl', 'rb') as f:\n", "        lexicon_package = pickle.load(f)\n", "    \n", "    slang_dict = lexicon_package['slang_dictionary']\n", "    sentiment_dict = lexicon_package['sentiment_lexicon']\n", "    \n", "    print(f\"✅ Lexicon package loaded:\")\n", "    print(f\"  • Slang dictionary: {len(slang_dict)} entries\")\n", "    print(f\"  • Sentiment lexicon: {len(sentiment_dict)} entries\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"⚠️ Lexicon package not found. Using basic dictionaries...\")\n", "    # Basic slang dictionary\n", "    slang_dict = {\n", "        'bgt': 'banget', 'ga': 'tidak', 'gak': 'tidak', 'yg': 'yang',\n", "        'dg': 'dengan', 'utk': 'untuk', 'hrs': 'harus', 'jd': 'jadi',\n", "        'tp': 'tapi', 'sm': 'sama', 'dr': 'dari', 'ke': 'ke'\n", "    }\n", "    sentiment_dict = {}\n", "    print(f\"📝 Basic slang dictionary created with {len(slang_dict)} entries\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Initialize Indonesian NLP Tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize Sastrawi stemmer\n", "factory = StemmerFactory()\n", "stemmer = factory.create_stemmer()\n", "print(\"✅ Sastrawi stemmer initialized\")\n", "\n", "# Initialize Sastrawi stopword remover\n", "stopword_factory = StopWordRemoverFactory()\n", "sastrawi_stopwords = stopword_factory.get_stop_words()\n", "\n", "# Get NLTK Indonesian stopwords\n", "try:\n", "    nltk_stopwords = set(stopwords.words('indonesian'))\n", "except:\n", "    nltk_stopwords = set()\n", "\n", "# Combine stopwords from multiple sources\n", "combined_stopwords = set(sastrawi_stopwords) | nltk_stopwords\n", "\n", "# Add custom stopwords for food delivery domain\n", "custom_stopwords = {\n", "    'gofood', 'grabfood', 'shopeefood', 'aplikasi', 'app', 'order', 'pesan',\n", "    'makanan', 'minuman', 'driver', 'kurir', 'antar', 'kirim', 'delivery'\n", "}\n", "\n", "# Final stopwords set\n", "final_stopwords = combined_stopwords | custom_stopwords\n", "\n", "print(f\"📚 Stopwords initialized:\")\n", "print(f\"  • Sastrawi stopwords: {len(sastrawi_stopwords)}\")\n", "print(f\"  • NLTK stopwords: {len(nltk_stopwords)}\")\n", "print(f\"  • Custom stopwords: {len(custom_stopwords)}\")\n", "print(f\"  • Total unique stopwords: {len(final_stopwords)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧹 Advanced Text Cleaning Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def advanced_text_cleaning(text):\n", "    \"\"\"\n", "    Comprehensive text cleaning for Indonesian text\n", "    \n", "    Args:\n", "        text (str): Input text\n", "    \n", "    Returns:\n", "        str: Cleaned text\n", "    \"\"\"\n", "    if not text or pd.isna(text):\n", "        return \"\"\n", "    \n", "    text = str(text)\n", "    \n", "    # 1. Convert to lowercase\n", "    text = text.lower()\n", "    \n", "    # 2. Remove URLs\n", "    text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text, flags=re.MULTILINE)\n", "    \n", "    # 3. Remove email addresses\n", "    text = re.sub(r'\\S+@\\S+', '', text)\n", "    \n", "    # 4. Remove mentions (@username)\n", "    text = re.sub(r'@[A-Za-z0-9_]+', '', text)\n", "    \n", "    # 5. Remove hashtags (#hashtag)\n", "    text = re.sub(r'#\\w+', '', text)\n", "    \n", "    # 6. Remove phone numbers\n", "    text = re.sub(r'\\b\\d{4,}\\b', '', text)\n", "    \n", "    # 7. Remove excessive punctuation\n", "    text = re.sub(r'[!]{2,}', '!', text)\n", "    text = re.sub(r'[?]{2,}', '?', text)\n", "    text = re.sub(r'[.]{2,}', '.', text)\n", "    \n", "    # 8. Remove special characters but keep Indonesian characters\n", "    text = re.sub(r'[^a-zA-Z\\s]', ' ', text)\n", "    \n", "    # 9. Remove extra whitespace\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    # 10. Remove single characters (except 'a' and 'i')\n", "    words = text.split()\n", "    words = [word for word in words if len(word) > 1 or word in ['a', 'i']]\n", "    text = ' '.join(words)\n", "    \n", "    return text\n", "\n", "def normalize_slang(text, slang_dictionary):\n", "    \"\"\"\n", "    Normalize Indonesian slang words\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        slang_dictionary (dict): Slang to formal word mapping\n", "    \n", "    Returns:\n", "        str: Normalized text\n", "    \"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    words = text.split()\n", "    normalized_words = []\n", "    \n", "    for word in words:\n", "        # Check if word exists in slang dictionary\n", "        if word in slang_dictionary:\n", "            normalized_words.append(slang_dictionary[word])\n", "        else:\n", "            normalized_words.append(word)\n", "    \n", "    return ' '.join(normalized_words)\n", "\n", "def remove_stopwords(text, stopwords_set):\n", "    \"\"\"\n", "    Remove stopwords from text\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        stopwords_set (set): Set of stopwords\n", "    \n", "    Returns:\n", "        str: Text without stopwords\n", "    \"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    words = text.split()\n", "    filtered_words = [word for word in words if word not in stopwords_set]\n", "    \n", "    return ' '.join(filtered_words)\n", "\n", "def stem_text(text, stemmer):\n", "    \"\"\"\n", "    Apply stemming to Indonesian text\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        stemmer: Sastrawi stemmer object\n", "    \n", "    Returns:\n", "        str: <PERSON><PERSON><PERSON> text\n", "    \"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    return stemmer.stem(text)\n", "\n", "print(\"🔧 Text preprocessing functions defined:\")\n", "print(\"  ✅ advanced_text_cleaning()\")\n", "print(\"  ✅ normalize_slang()\")\n", "print(\"  ✅ remove_stopwords()\")\n", "print(\"  ✅ stem_text()\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Apply Preprocessing Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a copy of the original data for comparison\n", "df_processed = df.copy()\n", "\n", "print(\"🔄 APPLYING PREPROCESSING PIPELINE\")\n", "print(\"=\" * 50)\n", "\n", "# Step 1: Advanced text cleaning\n", "print(\"\\n1️⃣ Advanced text cleaning...\")\n", "df_processed['text_cleaned'] = df_processed[main_text_col].apply(advanced_text_cleaning)\n", "print(\"   ✅ Text cleaning completed\")\n", "\n", "# Step 2: Slang normalization\n", "print(\"\\n2️⃣ Slang normalization...\")\n", "df_processed['text_normalized'] = df_processed['text_cleaned'].apply(\n", "    lambda x: normalize_slang(x, slang_dict)\n", ")\n", "print(\"   ✅ Slang normalization completed\")\n", "\n", "# Step 3: Stopword removal\n", "print(\"\\n3️⃣ Stopword removal...\")\n", "df_processed['text_no_stopwords'] = df_processed['text_normalized'].apply(\n", "    lambda x: remove_stopwords(x, final_stopwords)\n", ")\n", "print(\"   ✅ Stopword removal completed\")\n", "\n", "# Step 4: <PERSON><PERSON><PERSON>\n", "print(\"\\n4️⃣ Stemming...\")\n", "df_processed['text_stemmed'] = df_processed['text_no_stopwords'].apply(\n", "    lambda x: stem_text(x, stemmer)\n", ")\n", "print(\"   ✅ Stemming completed\")\n", "\n", "print(f\"\\n🎉 PREPROCESSING PIPELINE COMPLETED!\")\n", "print(f\"📊 Processed {len(df_processed)} reviews through 4 stages\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Before/After Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze the impact of each preprocessing step\n", "print(\"📊 BEFORE/AFTER PREPROCESSING ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Sample comparison\n", "sample_indices = range(min(5, len(df_processed)))\n", "\n", "print(\"\\n🔍 SAMPLE TRANSFORMATIONS:\")\n", "print(\"-\" * 60)\n", "\n", "for i in sample_indices:\n", "    print(f\"\\n📝 Sample {i+1}:\")\n", "    print(f\"Original    : {df_processed[main_text_col].iloc[i]}\")\n", "    print(f\"Cleaned     : {df_processed['text_cleaned'].iloc[i]}\")\n", "    print(f\"Normalized  : {df_processed['text_normalized'].iloc[i]}\")\n", "    print(f\"No Stopwords: {df_processed['text_no_stopwords'].iloc[i]}\")\n", "    print(f\"Stemmed     : {df_processed['text_stemmed'].iloc[i]}\")\n", "    print(\"-\" * 60)\n", "\n", "# Statistical analysis\n", "stages = ['original', 'cleaned', 'normalized', 'no_stopwords', 'stemmed']\n", "columns = [main_text_col, 'text_cleaned', 'text_normalized', 'text_no_stopwords', 'text_stemmed']\n", "\n", "stats_data = []\n", "\n", "for stage, col in zip(stages, columns):\n", "    texts = df_processed[col].astype(str)\n", "    \n", "    # Calculate statistics\n", "    char_lengths = texts.apply(len)\n", "    word_counts = texts.apply(lambda x: len(x.split()))\n", "    \n", "    # Vocabulary analysis\n", "    all_words = ' '.join(texts).split()\n", "    unique_words = len(set(all_words))\n", "    total_words = len(all_words)\n", "    \n", "    stats_data.append({\n", "        'Stage': stage,\n", "        'Avg_Char_Length': char_lengths.mean(),\n", "        'Avg_Word_Count': word_counts.mean(),\n", "        'Total_Words': total_words,\n", "        'Unique_Words': unique_words,\n", "        'Vocabulary_Richness': unique_words / total_words if total_words > 0 else 0\n", "    })\n", "\n", "stats_df = pd.DataFrame(stats_data)\n", "\n", "print(\"\\n📈 PREPROCESSING IMPACT STATISTICS:\")\n", "print(stats_df.round(3))\n", "\n", "# Calculate reduction percentages\n", "original_chars = stats_df.iloc[0]['Avg_Char_Length']\n", "final_chars = stats_df.iloc[-1]['Avg_Char_Length']\n", "char_reduction = ((original_chars - final_chars) / original_chars) * 100\n", "\n", "original_words = stats_df.iloc[0]['Avg_Word_Count']\n", "final_words = stats_df.iloc[-1]['Avg_Word_Count']\n", "word_reduction = ((original_words - final_words) / original_words) * 100\n", "\n", "original_vocab = stats_df.iloc[0]['Unique_Words']\n", "final_vocab = stats_df.iloc[-1]['Unique_Words']\n", "vocab_reduction = ((original_vocab - final_vocab) / original_vocab) * 100\n", "\n", "print(f\"\\n📉 REDUCTION SUMMARY:\")\n", "print(f\"  • Average character length: {char_reduction:.1f}% reduction\")\n", "print(f\"  • Average word count: {word_reduction:.1f}% reduction\")\n", "print(f\"  • Vocabulary size: {vocab_reduction:.1f}% reduction\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Visualization of Preprocessing Impact"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualization\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Average Character Length by Stage', 'Average Word Count by Stage',\n", "                   'Vocabulary Size by Stage', 'Vocabulary Richness by Stage'),\n", "    specs=[[{'type': 'bar'}, {'type': 'bar'}],\n", "           [{'type': 'bar'}, {'type': 'scatter'}]]\n", ")\n", "\n", "# Character length\n", "fig.add_trace(\n", "    go.Bar(x=stats_df['Stage'], y=stats_df['Avg_Char_Length'], \n", "           name='Char Length', marker_color='lightblue'),\n", "    row=1, col=1\n", ")\n", "\n", "# Word count\n", "fig.add_trace(\n", "    go.Bar(x=stats_df['Stage'], y=stats_df['Avg_Word_Count'], \n", "           name='Word Count', marker_color='lightgreen'),\n", "    row=1, col=2\n", ")\n", "\n", "# Vocabulary size\n", "fig.add_trace(\n", "    go.Bar(x=stats_df['Stage'], y=stats_df['Unique_Words'], \n", "           name='Vocabulary', marker_color='lightcoral'),\n", "    row=2, col=1\n", ")\n", "\n", "# Vocabulary richness\n", "fig.add_trace(\n", "    go.<PERSON><PERSON>(x=stats_df['Stage'], y=stats_df['Vocabulary_Richness'], \n", "               mode='lines+markers', name='Richness', \n", "               line=dict(color='purple', width=3)),\n", "    row=2, col=2\n", ")\n", "\n", "fig.update_layout(height=800, showlegend=False, \n", "                 title_text=\"📊 Text Preprocessing Impact Analysis\")\n", "fig.show()\n", "\n", "# Word frequency analysis\n", "print(\"\\n🔤 TOP WORDS ANALYSIS:\")\n", "print(\"=\" * 50)\n", "\n", "# Original text top words\n", "original_words = ' '.join(df_processed[main_text_col].astype(str)).lower().split()\n", "original_freq = Counter(original_words)\n", "\n", "# Final processed text top words\n", "final_words = ' '.join(df_processed['text_stemmed'].astype(str)).split()\n", "final_freq = Counter(final_words)\n", "\n", "print(\"\\n📝 TOP 15 WORDS - ORIGINAL TEXT:\")\n", "for word, count in original_freq.most_common(15):\n", "    if word.strip() and len(word) > 1:\n", "        print(f\"  {word:<15} : {count:>3} times\")\n", "\n", "print(\"\\n🔧 TOP 15 WORDS - PROCESSED TEXT:\")\n", "for word, count in final_freq.most_common(15):\n", "    if word.strip() and len(word) > 1:\n", "        print(f\"  {word:<15} : {count:>3} times\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Quality Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quality validation of preprocessing results\n", "print(\"🔍 PREPROCESSING QUALITY VALIDATION\")\n", "print(\"=\" * 50)\n", "\n", "# Check for empty texts after preprocessing\n", "empty_texts = df_processed['text_stemmed'].apply(lambda x: str(x).strip() == '').sum()\n", "empty_percentage = (empty_texts / len(df_processed)) * 100\n", "\n", "print(f\"\\n📊 QUALITY METRICS:\")\n", "print(f\"  • Empty texts after preprocessing: {empty_texts} ({empty_percentage:.1f}%)\")\n", "print(f\"  • Valid texts: {len(df_processed) - empty_texts} ({100-empty_percentage:.1f}%)\")\n", "\n", "# Check text length distribution\n", "text_lengths = df_processed['text_stemmed'].apply(lambda x: len(str(x).split()))\n", "print(f\"\\n📏 TEXT LENGTH DISTRIBUTION (words):\")\n", "print(f\"  • Mean: {text_lengths.mean():.1f}\")\n", "print(f\"  • Median: {text_lengths.median():.1f}\")\n", "print(f\"  • Min: {text_lengths.min()}\")\n", "print(f\"  • Max: {text_lengths.max()}\")\n", "print(f\"  • Std: {text_lengths.std():.1f}\")\n", "\n", "# Check for very short texts (potential quality issues)\n", "very_short = (text_lengths <= 2).sum()\n", "short_percentage = (very_short / len(df_processed)) * 100\n", "print(f\"\\n⚠️ POTENTIAL QUALITY ISSUES:\")\n", "print(f\"  • Very short texts (≤2 words): {very_short} ({short_percentage:.1f}%)\")\n", "\n", "# Show examples of very short texts\n", "if very_short > 0:\n", "    short_examples = df_processed[text_lengths <= 2]\n", "    print(f\"\\n📝 Examples of very short texts:\")\n", "    for i, (idx, row) in enumerate(short_examples.head(3).iterrows()):\n", "        print(f\"  {i+1}. Original: '{row[main_text_col]}'\")\n", "        print(f\"     Processed: '{row['text_stemmed']}'\")\n", "\n", "# Vocabulary richness check\n", "all_processed_words = ' '.join(df_processed['text_stemmed'].astype(str)).split()\n", "unique_processed_words = set(all_processed_words)\n", "vocab_richness = len(unique_processed_words) / len(all_processed_words) if all_processed_words else 0\n", "\n", "print(f\"\\n📚 VOCABULARY ANALYSIS:\")\n", "print(f\"  • Total processed words: {len(all_processed_words):,}\")\n", "print(f\"  • Unique words: {len(unique_processed_words):,}\")\n", "print(f\"  • Vocabulary richness: {vocab_richness:.3f}\")\n", "\n", "# Quality score calculation\n", "quality_score = 100 - empty_percentage - short_percentage\n", "print(f\"\\n🎯 OVERALL QUALITY SCORE: {quality_score:.1f}/100\")\n", "\n", "if quality_score >= 90:\n", "    print(\"   ✅ Excellent quality!\")\n", "elif quality_score >= 80:\n", "    print(\"   ✅ Good quality\")\n", "elif quality_score >= 70:\n", "    print(\"   ⚠️ Acceptable quality\")\n", "else:\n", "    print(\"   ❌ Quality issues detected\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save Preprocessed Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare final dataset with all preprocessing stages\n", "print(\"💾 SAVING PREPROCESSED DATA\")\n", "print(\"=\" * 50)\n", "\n", "# Create final dataset with selected columns\n", "final_columns = [main_text_col, 'text_cleaned', 'text_normalized', \n", "                'text_no_stopwords', 'text_stemmed']\n", "\n", "# Add rating column if exists\n", "if 'nilai' in df_processed.columns:\n", "    final_columns.append('nilai')\n", "\n", "# Add any other important columns\n", "other_cols = [col for col in df_processed.columns \n", "              if col not in final_columns and col != main_text_col]\n", "final_columns.extend(other_cols)\n", "\n", "# Create final dataframe\n", "df_final = df_processed[final_columns].copy()\n", "\n", "# Rename columns for clarity\n", "column_mapping = {\n", "    main_text_col: 'original_text',\n", "    'text_cleaned': 'stage1_cleaned',\n", "    'text_normalized': 'stage2_normalized', \n", "    'text_no_stopwords': 'stage3_no_stopwords',\n", "    'text_stemmed': 'final_processed_text'\n", "}\n", "\n", "df_final = df_final.rename(columns=column_mapping)\n", "\n", "# Save to Excel\n", "output_file = 'data/processed/gofood_advanced_preprocessed.xlsx'\n", "try:\n", "    df_final.to_excel(output_file, index=False, engine='openpyxl')\n", "    print(f\"✅ Data saved to: {output_file}\")\n", "except:\n", "    # Fallback to current directory\n", "    output_file = 'gofood_advanced_preprocessed.xlsx'\n", "    df_final.to_excel(output_file, index=False, engine='openpyxl')\n", "    print(f\"✅ Data saved to: {output_file}\")\n", "\n", "# Save preprocessing statistics\n", "stats_output = 'data/processed/preprocessing_stats.xlsx'\n", "try:\n", "    stats_df.to_excel(stats_output, index=False, engine='openpyxl')\n", "    print(f\"✅ Statistics saved to: {stats_output}\")\n", "except:\n", "    stats_output = 'preprocessing_stats.xlsx'\n", "    stats_df.to_excel(stats_output, index=False, engine='openpyxl')\n", "    print(f\"✅ Statistics saved to: {stats_output}\")\n", "\n", "# Save preprocessing functions as utility module\n", "preprocessing_code = '''\n", "import pandas as pd\n", "import re\n", "from Sastrawi.Stemmer.StemmerFactory import StemmerFactory\n", "from Sastrawi.StopWordRemover.StopWordRemoverFactory import StopWordRemoverFactory\n", "import nltk\n", "from nltk.corpus import stopwords\n", "\n", "def advanced_text_cleaning(text):\n", "    \"\"\"Comprehensive text cleaning for Indonesian text\"\"\"\n", "    if not text or pd.isna(text):\n", "        return \"\"\n", "    \n", "    text = str(text).lower()\n", "    text = re.sub(r'http\\\\S+|www\\\\S+|https\\\\S+', '', text, flags=re.MULTILINE)\n", "    text = re.sub(r'\\\\S+@\\\\S+', '', text)\n", "    text = re.sub(r'@[A-Za-z0-9_]+', '', text)\n", "    text = re.sub(r'#\\\\w+', '', text)\n", "    text = re.sub(r'\\\\b\\\\d{4,}\\\\b', '', text)\n", "    text = re.sub(r'[!]{2,}', '!', text)\n", "    text = re.sub(r'[?]{2,}', '?', text)\n", "    text = re.sub(r'[.]{2,}', '.', text)\n", "    text = re.sub(r'[^a-zA-Z\\\\s]', ' ', text)\n", "    text = re.sub(r'\\\\s+', ' ', text).strip()\n", "    \n", "    words = text.split()\n", "    words = [word for word in words if len(word) > 1 or word in ['a', 'i']]\n", "    return ' '.join(words)\n", "\n", "def normalize_slang(text, slang_dictionary):\n", "    \"\"\"Normalize Indonesian slang words\"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    words = text.split()\n", "    normalized_words = [slang_dictionary.get(word, word) for word in words]\n", "    return ' '.join(normalized_words)\n", "\n", "def remove_stopwords(text, stopwords_set):\n", "    \"\"\"Remove stopwords from text\"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    words = text.split()\n", "    filtered_words = [word for word in words if word not in stopwords_set]\n", "    return ' '.join(filtered_words)\n", "\n", "def stem_text(text, stemmer):\n", "    \"\"\"Apply stemming to Indonesian text\"\"\"\n", "    if not text:\n", "        return \"\"\n", "    return stemmer.stem(text)\n", "\n", "def preprocess_indonesian_text(text, slang_dict=None, custom_stopwords=None):\n", "    \"\"\"Complete preprocessing pipeline for Indonesian text\"\"\"\n", "    # Initialize tools\n", "    factory = StemmerFactory()\n", "    stemmer = factory.create_stemmer()\n", "    \n", "    stopword_factory = StopWordRemoverFactory()\n", "    sastrawi_stopwords = stopword_factory.get_stop_words()\n", "    \n", "    try:\n", "        nltk_stopwords = set(stopwords.words('indonesian'))\n", "    except:\n", "        nltk_stopwords = set()\n", "    \n", "    final_stopwords = set(sastrawi_stopwords) | nltk_stopwords\n", "    if custom_stopwords:\n", "        final_stopwords |= set(custom_stopwords)\n", "    \n", "    # Apply preprocessing pipeline\n", "    text = advanced_text_cleaning(text)\n", "    if slang_dict:\n", "        text = normalize_slang(text, slang_dict)\n", "    text = remove_stopwords(text, final_stopwords)\n", "    text = stem_text(text, stemmer)\n", "    \n", "    return text\n", "'''\n", "\n", "with open('utils/indonesian_preprocessing.py', 'w', encoding='utf-8') as f:\n", "    f.write(preprocessing_code)\n", "\n", "print(f\"\\n🔧 Preprocessing utilities saved to: utils/indonesian_preprocessing.py\")\n", "\n", "print(f\"\\n📊 FINAL DATASET SUMMARY:\")\n", "print(f\"  • Total records: {len(df_final)}\")\n", "print(f\"  • Columns: {len(df_final.columns)}\")\n", "print(f\"  • Column names: {list(df_final.columns)}\")\n", "print(f\"  • File size: {df_final.memory_usage(deep=True).sum() / 1024**2:.1f} MB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 TEXT PREPROCESSING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n🔄 PREPROCESSING PIPELINE COMPLETED:\")\n", "print(f\"  1️⃣ Advanced text cleaning\")\n", "print(f\"  2️⃣ Indonesian slang normalization\")\n", "print(f\"  3️⃣ Stopword removal (multi-source)\")\n", "print(f\"  4️⃣ Sastrawi stemming\")\n", "\n", "print(f\"\\n📊 PROCESSING RESULTS:\")\n", "print(f\"  • Records processed: {len(df_final)}\")\n", "print(f\"  • Average text reduction: {word_reduction:.1f}% (words)\")\n", "print(f\"  • Vocabulary reduction: {vocab_reduction:.1f}%\")\n", "print(f\"  • Quality score: {quality_score:.1f}/100\")\n", "\n", "print(f\"\\n💾 FILES GENERATED:\")\n", "print(f\"  📁 Main output: {output_file}\")\n", "print(f\"  📊 Statistics: {stats_output}\")\n", "print(f\"  🔧 Utilities: utils/indonesian_preprocessing.py\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. Feature extraction with TF-IDF (04_feature_extraction.ipynb)\")\n", "print(f\"  2. Score-based sentiment labeling (05_score_based_labeling.ipynb)\")\n", "print(f\"  3. Lexicon-based sentiment labeling (06_lexicon_based_labeling.ipynb)\")\n", "print(f\"  4. Model training and evaluation\")\n", "\n", "print(f\"\\n✅ TEXT PREPROCESSING COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for feature extraction phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}