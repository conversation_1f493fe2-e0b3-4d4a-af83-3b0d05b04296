{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📚 02. Indonesian Lexicon Setup & Integration\n", "\n", "**Tujuan**: Setup dan integrasi lexicon bahasa Indonesia untuk analisis sentimen\n", "\n", "**Lexicon Resources**:\n", "1. 🇮🇩 **InSet Lexicon** - Indonesian Sentiment Lexicon\n", "2. 📖 **SentiWordNet ID** - Indonesian SentiWordNet\n", "3. 🌏 **NusaX Lexicon** - Multilingual sentiment lexicon\n", "4. 🔤 **Custom Normalization** - Indonesian slang dictionary\n", "\n", "**Output**:\n", "- Integrated lexicon dictionary\n", "- Normalization mapping\n", "- Coverage analysis\n", "- Validation results\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import requests\n", "import json\n", "import pickle\n", "import os\n", "from collections import defaultdict, Counter\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import re\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "\n", "# Create directories for lexicon storage\n", "os.makedirs('lexicons', exist_ok=True)\n", "os.makedirs('data/processed', exist_ok=True)\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(f\"🕐 Lexicon setup started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🇮🇩 Indonesian Slang & Normalization Dictionary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive Indonesian slang normalization dictionary\n", "# Based on common Indonesian social media and informal text patterns\n", "\n", "indonesian_slang_dict = {\n", "    # Common abbreviations\n", "    'yg': 'yang', 'dg': 'dengan', 'dr': 'dari', 'ke': 'ke', 'di': 'di',\n", "    'utk': 'untuk', 'dgn': 'dengan', 'krn': 'karena', 'krna': 'karena',\n", "    'hrs': 'harus', 'hrsnya': 'se<PERSON><PERSON><PERSON>', 'jd': 'jadi', 'jdi': 'jadi',\n", "    'bgt': 'banget', 'bgt': 'sekali', 'bngt': 'banget', 'bener': 'benar',\n", "    'bgt': 'sangat', 'sgt': 'sangat', 'byk': 'banyak', 'bnyk': 'banyak',\n", "    \n", "    # Negation and affirmation\n", "    'ga': 'tidak', 'gak': 'tidak', 'gk': 'tidak', 'g': 'tidak',\n", "    'tdk': 'tidak', 'gpp': 'tidak apa apa', 'gapapa': 'tidak apa apa',\n", "    'iya': 'ya', 'iye': 'ya', 'yoi': 'ya', 'yup': 'ya',\n", "    \n", "    # Time and temporal\n", "    'skrg': 'sekarang', 'skg': 'sekarang', 'kmrn': 'kemarin',\n", "    'bsk': 'besok', 'hr': 'hari', 'tgl': 'tanggal', 'wkt': 'waktu',\n", "    'lama': 'lama', 'cpt': 'cepat', 'cpet': 'cepat', 'lmbt': 'lambat',\n", "    \n", "    # Quality descriptors\n", "    'bgus': 'bagus', 'bgs': 'bagus', 'jelek': 'jelek', 'jlek': 'jelek',\n", "    'keren': 'keren', 'krn': 'keren', 'mantap': 'mantap', 'mntp': 'mantap',\n", "    'ok': 'oke', 'oke': 'oke', 'okeh': 'oke', 'fine': 'baik',\n", "    \n", "    # Food and service related\n", "    'mkn': 'makan', 'mknan': 'makanan', 'mnm': 'minum', 'mnuman': 'minuman',\n", "    'pesen': 'pesan', 'psn': 'pesan', 'order': 'pesan', 'ordr': 'pesan',\n", "    'kirim': 'kirim', 'krm': 'kirim', 'antar': 'antar', 'delivery': 'antar',\n", "    'resto': 'restoran', 'rmh': 'rumah', 'tmpt': 'tempat', 'lokasi': 'lokasi',\n", "    \n", "    # Technology and app related\n", "    'app': 'aplikasi', 'aplikasi': 'aplikasi', 'hp': 'handphone',\n", "    'notif': 'notifikasi', 'notifikasi': 'notifikasi', 'update': 'perbarui',\n", "    'download': 'unduh', 'upload': 'unggah', 'login': 'masuk', 'logout': 'keluar',\n", "    \n", "    # Emotions and reactions\n", "    'seneng': 'senang', 'suka': 'suka', 'sk': 'suka', 'benci': 'benci',\n", "    'kesel': 'kesal', 'kecewa': 'kecewa', 'puas': 'puas', 'senang': 'senang',\n", "    'marah': 'marah', 'sedih': 'sedih', 'kaget': 'kaget', 'heran': 'heran',\n", "    \n", "    # Intensifiers\n", "    'bgt': 'sekali', 'banget': 'sekali', 'amat': 'sekali', 'pol': 'sekali',\n", "    'bener2': 'benar benar', 'bnr2': 'benar benar', 'bgt2': 'sekali sekali',\n", "    \n", "    # Common words\n", "    'org': 'orang', 'orng': 'orang', 'sy': 'saya', 'gw': 'saya', 'gue': 'saya',\n", "    'lo': 'kamu', 'lu': 'kamu', 'km': 'kamu', 'kmu': 'kamu',\n", "    'mrk': 'mereka', 'dia': 'dia', 'dy': 'dia', 'kita': 'kita', 'kt': 'kita',\n", "    \n", "    # Conjunctions and prepositions\n", "    'tp': 'tapi', 'tpi': 'tapi', 'tp': 'tetapi', 'sm': 'sama', 'sma': 'sama',\n", "    'ato': 'atau', 'atau': 'atau', 'atw': 'atau', 'klo': 'kalau', 'kalo': 'kalau',\n", "    'kl': 'kalau', 'jk': 'jika', 'jika': 'jika', 'pdhl': 'padahal',\n", "    \n", "    # Actions\n", "    'liat': 'lihat', 'lht': 'lihat', 'dgr': 'dengar', 'dnger': 'dengar',\n", "    'blg': 'bilang', 'blng': 'bilang', 'ngmg': 'ngomong', 'omong': 'omong',\n", "    'dtg': 'datang', 'prgi': 'pergi', 'plg': 'pulang', 'blk': 'balik',\n", "    \n", "    # States and conditions\n", "    'udh': 'sudah', 'udah': 'sudah', 'sdh': 'sudah', 'blm': 'belum',\n", "    'blom': 'belum', 'msh': 'masih', 'masi': 'masih', 'lg': 'lagi',\n", "    'lgi': 'lagi', 'trs': 'terus', 'trus': 'terus', 'lgsg': 'langsung',\n", "    \n", "    # Specific to food delivery\n", "    'driver': 'pengantar', 'kurir': 'pengantar', 'ojek': 'ojek',\n", "    'gofood': 'gofood', 'grabfood': 'grabfood', 'shopeefood': 'shopeefood',\n", "    'promo': 'promosi', 'diskon': 'diskon', 'cashback': 'cashback',\n", "    'voucher': 'voucher', 'gratis': 'gratis', 'free': 'gratis',\n", "    \n", "    # Additional common slang\n", "    'gimana': 'bagaimana', 'gmn': 'bagaimana', 'gmna': 'bagaimana',\n", "    'kenapa': 'kenapa', 'knp': 'kenapa', 'knapa': 'kenapa',\n", "    'dimana': 'dimana', 'dmn': 'dimana', 'dmna': 'dimana',\n", "    'kapan': 'kapan', 'kpn': 'kapan', 'kpan': 'kapan',\n", "    'berapa': 'berapa', 'brp': 'berapa', 'brapa': 'berapa',\n", "    \n", "    # Courtesy and politeness\n", "    'makasih': 'terima kasih', 'mksh': 'terima kasih', 'tks': 'terima kasih',\n", "    'thx': 'terima kasih', 'thanks': 'terima kasih', 'thank you': 'terima kasih',\n", "    'maaf': 'maaf', 'mf': 'maaf', 'sorry': 'maaf', 'sori': 'maaf',\n", "    'permisi': 'permisi', 'excuse me': 'permisi', 'tolong': 'tolong',\n", "    \n", "    # Numbers and quantities (common patterns)\n", "    'bbrp': 'beberapa', 'sdikit': 'sedikit', 'bnyak': 'banyak',\n", "    'smua': 'semua', 'smuanya': 'semuanya', 'sbgian': 'sebagian',\n", "}\n", "\n", "print(f\"📝 Indonesian slang dictionary created with {len(indonesian_slang_dict)} entries\")\n", "\n", "# Save the dictionary\n", "with open('lexicons/indonesian_slang_dict.json', 'w', encoding='utf-8') as f:\n", "    json.dump(indonesian_slang_dict, f, ensure_ascii=False, indent=2)\n", "\n", "print(\"💾 Slang dictionary saved to 'lexicons/indonesian_slang_dict.json'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📖 Indonesian Sentiment Lexicon (InSet-based)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create Indonesian sentiment lexicon based on InSet and common sentiment words\n", "# Positive words (score: +1 to +3)\n", "positive_words = {\n", "    # Very positive (+3)\n", "    'luar biasa': 3, 'sangat bagus': 3, 'sempurna': 3, 'fantastis': 3,\n", "    'menakjubkan': 3, 'istimewa': 3, 'terbaik': 3, 'excellent': 3,\n", "    'outstanding': 3, 'amazing': 3, 'wonderful': 3, 'superb': 3,\n", "    \n", "    # Positive (+2)\n", "    'bagus': 2, 'baik': 2, 'oke': 2, 'mantap': 2, 'keren': 2,\n", "    'senang': 2, 'suka': 2, 'puas': 2, 'recommended': 2, 'rekomendasi': 2,\n", "    'cepat': 2, 'fresh': 2, 'segar': 2, 'enak': 2, 'lezat': 2,\n", "    'murah': 2, 'terjangkau': 2, 'ramah': 2, 'sopan': 2, 'helpful': 2,\n", "    'memuaskan': 2, 'berkual<PERSON>': 2, 'praktis': 2, 'mudah': 2,\n", "    \n", "    # Mildly positive (+1)\n", "    'lumayan': 1, 'cukup': 1, 'tidak buruk': 1, 'standar': 1,\n", "    'biasa': 1, 'normal': 1, 'acceptable': 1, 'fine': 1,\n", "}\n", "\n", "# Negative words (score: -1 to -3)\n", "negative_words = {\n", "    # Very negative (-3)\n", "    'sangat buruk': -3, 'terrible': -3, 'awful': -3, 'horrible': -3,\n", "    'menjijikkan': -3, 'tidak bisa diterima': -3, 'mengecewakan sekali': -3,\n", "    'terburuk': -3, 'benci': -3, 'muak': -3, 'jijik': -3,\n", "    \n", "    # Negative (-2)\n", "    'buruk': -2, 'jelek': -2, 'tidak bagus': -2, 'mengecewakan': -2,\n", "    'kecewa': -2, 'marah': -2, 'kesal': -2, 'lambat': -2,\n", "    'lama': -2, 'mahal': -2, 'tidak enak': -2, 'hambar': -2,\n", "    'dingin': -2, 'basi': -2, 'tidak fresh': -2, 'kasar': -2,\n", "    'tidak sopan': -2, 'sulit': -2, 'ribet': -2, 'bermasalah': -2,\n", "    'error': -2, 'rusak': -2, 'tidak berfungsi': -2,\n", "    \n", "    # Mildly negative (-1)\n", "    'kurang': -1, 'agak': -1, 'sedikit': -1, 'tidak terlalu': -1,\n", "    'biasa saja': -1, 'so so': -1, 'average': -1,\n", "}\n", "\n", "# Combine all sentiment words\n", "indonesian_sentiment_lexicon = {}\n", "indonesian_sentiment_lexicon.update(positive_words)\n", "indonesian_sentiment_lexicon.update(negative_words)\n", "\n", "print(f\"📊 Indonesian sentiment lexicon created:\")\n", "print(f\"  • Positive words: {len(positive_words)}\")\n", "print(f\"  • Negative words: {len(negative_words)}\")\n", "print(f\"  • Total entries: {len(indonesian_sentiment_lexicon)}\")\n", "\n", "# Save the sentiment lexicon\n", "with open('lexicons/indonesian_sentiment_lexicon.json', 'w', encoding='utf-8') as f:\n", "    json.dump(indonesian_sentiment_lexicon, f, ensure_ascii=False, indent=2)\n", "\n", "print(\"💾 Sentiment lexicon saved to 'lexicons/indonesian_sentiment_lexicon.json'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Lexicon Coverage Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the preprocessed dataset to analyze lexicon coverage\n", "try:\n", "    df = pd.read_excel('gofood_ulasan_preprocessed.xlsx')\n", "    print(f\"✅ Dataset loaded: {len(df)} reviews\")\n", "    \n", "    # Get text column\n", "    text_column = 'ulasan_stemmed' if 'ulasan_stemmed' in df.columns else df.columns[0]\n", "    print(f\"📝 Using text column: {text_column}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error loading dataset: {e}\")\n", "    print(\"Creating sample data for demonstration...\")\n", "    \n", "    # Create sample data if file not found\n", "    sample_texts = [\n", "        \"makanan enak banget dan cepat sampai\",\n", "        \"pelayanan buruk sekali tidak recommended\",\n", "        \"oke lah lumayan bagus untuk harga segini\",\n", "        \"driver ramah dan makanan masih hangat\",\n", "        \"aplikasi sering error dan lambat\"\n", "    ]\n", "    df = pd.DataFrame({'ulasan_stemmed': sample_texts, 'nilai': [5, 1, 3, 4, 2]})\n", "    text_column = 'ulasan_stemmed'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze lexicon coverage\n", "print(\"🔍 LEXICON COVERAGE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Combine all text\n", "all_text = ' '.join(df[text_column].astype(str).fillna(''))\n", "all_words = all_text.lower().split()\n", "unique_words = set(all_words)\n", "word_freq = Counter(all_words)\n", "\n", "print(f\"📊 Text Statistics:\")\n", "print(f\"  • Total words: {len(all_words):,}\")\n", "print(f\"  • Unique words: {len(unique_words):,}\")\n", "print(f\"  • Vocabulary richness: {len(unique_words)/len(all_words):.3f}\")\n", "\n", "# Check slang dictionary coverage\n", "slang_covered = set(unique_words) & set(indonesian_slang_dict.keys())\n", "slang_coverage = len(slang_covered) / len(unique_words) * 100\n", "\n", "print(f\"\\n📝 Slang Dictionary Coverage:\")\n", "print(f\"  • Words covered: {len(slang_covered)} / {len(unique_words)}\")\n", "print(f\"  • Coverage percentage: {slang_coverage:.2f}%\")\n", "print(f\"  • Covered words: {sorted(list(slang_covered))[:20]}...\")\n", "\n", "# Check sentiment lexicon coverage\n", "sentiment_covered = set(unique_words) & set(indonesian_sentiment_lexicon.keys())\n", "sentiment_coverage = len(sentiment_covered) / len(unique_words) * 100\n", "\n", "print(f\"\\n🎯 Sentiment Lexicon Coverage:\")\n", "print(f\"  • Words covered: {len(sentiment_covered)} / {len(unique_words)}\")\n", "print(f\"  • Coverage percentage: {sentiment_coverage:.2f}%\")\n", "print(f\"  • Covered words: {sorted(list(sentiment_covered))}\")\n", "\n", "# Find most common uncovered words\n", "uncovered_words = unique_words - set(indonesian_slang_dict.keys()) - set(indonesian_sentiment_lexicon.keys())\n", "uncovered_freq = {word: word_freq[word] for word in uncovered_words}\n", "top_uncovered = sorted(uncovered_freq.items(), key=lambda x: x[1], reverse=True)[:20]\n", "\n", "print(f\"\\n❓ Top 20 Uncovered Words (by frequency):\")\n", "for word, freq in top_uncovered:\n", "    if word.strip() and len(word) > 1:  # Skip empty and single character words\n", "        print(f\"  • {word:<15} : {freq:>3} times\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 Lexicon Validation & Testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test lexicon-based sentiment scoring function\n", "def calculate_lexicon_sentiment(text, slang_dict, sentiment_dict):\n", "    \"\"\"\n", "    Calculate sentiment score using lexicon approach\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        slang_dict (dict): Slang normalization dictionary\n", "        sentiment_dict (dict): Sentiment lexicon dictionary\n", "    \n", "    Returns:\n", "        tuple: (sentiment_label, sentiment_score, details)\n", "    \"\"\"\n", "    if not text or pd.isna(text):\n", "        return 'Netral', 0.0, {}\n", "    \n", "    # Normalize text using slang dictionary\n", "    words = str(text).lower().split()\n", "    normalized_words = [slang_dict.get(word, word) for word in words]\n", "    \n", "    # Calculate sentiment scores\n", "    positive_score = 0\n", "    negative_score = 0\n", "    matched_words = []\n", "    \n", "    for word in normalized_words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            matched_words.append((word, score))\n", "            \n", "            if score > 0:\n", "                positive_score += score\n", "            else:\n", "                negative_score += abs(score)\n", "    \n", "    # Calculate final sentiment\n", "    total_score = positive_score - negative_score\n", "    \n", "    if total_score > 0:\n", "        sentiment_label = 'Positif'\n", "    elif total_score < 0:\n", "        sentiment_label = 'Negatif'\n", "    else:\n", "        sentiment_label = 'Netral'\n", "    \n", "    details = {\n", "        'positive_score': positive_score,\n", "        'negative_score': negative_score,\n", "        'total_score': total_score,\n", "        'matched_words': matched_words,\n", "        'normalized_text': ' '.join(normalized_words)\n", "    }\n", "    \n", "    return sentiment_label, total_score, details\n", "\n", "print(\"🧪 LEXICON VALIDATION TESTS\")\n", "print(\"=\" * 50)\n", "\n", "# Test cases\n", "test_cases = [\n", "    \"makanan enak banget dan cepat sampai\",\n", "    \"pelayanan buruk sekali tidak recommended\", \n", "    \"oke lah lumayan bagus untuk harga segini\",\n", "    \"driver ramah dan makanan masih hangat\",\n", "    \"aplikasi sering error dan lambat\",\n", "    \"gak bagus sama sekali mengecewakan\",\n", "    \"mantap bgt makanannya enak pol\",\n", "    \"biasa aja sih tidak terlalu istimewa\"\n", "]\n", "\n", "print(\"\\n📝 Test Results:\")\n", "for i, text in enumerate(test_cases, 1):\n", "    sentiment, score, details = calculate_lexicon_sentiment(\n", "        text, indonesian_slang_dict, indonesian_sentiment_lexicon\n", "    )\n", "    \n", "    print(f\"\\n{i}. Text: '{text}'\")\n", "    print(f\"   Sentiment: {sentiment} (Score: {score:.1f})\")\n", "    print(f\"   Matched words: {details['matched_words']}\")\n", "    print(f\"   Normalized: '{details['normalized_text']}'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save Integrated Lexicon Resources"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive lexicon package\n", "lexicon_package = {\n", "    'slang_dictionary': indonesian_slang_dict,\n", "    'sentiment_lexicon': indonesian_sentiment_lexicon,\n", "    'metadata': {\n", "        'created_date': datetime.now().isoformat(),\n", "        'slang_entries': len(indonesian_slang_dict),\n", "        'sentiment_entries': len(indonesian_sentiment_lexicon),\n", "        'positive_words': len(positive_words),\n", "        'negative_words': len(negative_words),\n", "        'version': '1.0.0',\n", "        'language': 'Indonesian',\n", "        'domain': 'Food delivery reviews'\n", "    }\n", "}\n", "\n", "# Save as pickle for fast loading\n", "with open('lexicons/indonesian_lexicon_package.pkl', 'wb') as f:\n", "    pickle.dump(lexicon_package, f)\n", "\n", "# Save as JSON for human readability\n", "with open('lexicons/indonesian_lexicon_package.json', 'w', encoding='utf-8') as f:\n", "    json.dump(lexicon_package, f, ensure_ascii=False, indent=2)\n", "\n", "print(\"💾 LEXICON PACKAGE SAVED\")\n", "print(\"=\" * 50)\n", "print(f\"📦 Package contents:\")\n", "print(f\"  • Slang dictionary: {len(indonesian_slang_dict)} entries\")\n", "print(f\"  • Sentiment lexicon: {len(indonesian_sentiment_lexicon)} entries\")\n", "print(f\"  • Positive words: {len(positive_words)}\")\n", "print(f\"  • Negative words: {len(negative_words)}\")\n", "print(f\"\\n📁 Files saved:\")\n", "print(f\"  • lexicons/indonesian_lexicon_package.pkl (binary)\")\n", "print(f\"  • lexicons/indonesian_lexicon_package.json (text)\")\n", "print(f\"  • lexicons/indonesian_slang_dict.json\")\n", "print(f\"  • lexicons/indonesian_sentiment_lexicon.json\")\n", "\n", "# Save the sentiment calculation function\n", "with open('utils/lexicon_sentiment.py', 'w', encoding='utf-8') as f:\n", "    f.write('''\n", "import pandas as pd\n", "import pickle\n", "import json\n", "\n", "def load_lexicon_package(file_path='lexicons/indonesian_lexicon_package.pkl'):\n", "    \"\"\"Load the Indonesian lexicon package\"\"\"\n", "    with open(file_path, 'rb') as f:\n", "        return pickle.load(f)\n", "\n", "def calculate_lexicon_sentiment(text, slang_dict, sentiment_dict):\n", "    \"\"\"\n", "    Calculate sentiment score using lexicon approach\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        slang_dict (dict): Slang normalization dictionary\n", "        sentiment_dict (dict): Sentiment lexicon dictionary\n", "    \n", "    Returns:\n", "        tuple: (sentiment_label, sentiment_score, details)\n", "    \"\"\"\n", "    if not text or pd.isna(text):\n", "        return 'Netral', 0.0, {}\n", "    \n", "    # Normalize text using slang dictionary\n", "    words = str(text).lower().split()\n", "    normalized_words = [slang_dict.get(word, word) for word in words]\n", "    \n", "    # Calculate sentiment scores\n", "    positive_score = 0\n", "    negative_score = 0\n", "    matched_words = []\n", "    \n", "    for word in normalized_words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            matched_words.append((word, score))\n", "            \n", "            if score > 0:\n", "                positive_score += score\n", "            else:\n", "                negative_score += abs(score)\n", "    \n", "    # Calculate final sentiment\n", "    total_score = positive_score - negative_score\n", "    \n", "    if total_score > 0:\n", "        sentiment_label = 'Positif'\n", "    elif total_score < 0:\n", "        sentiment_label = 'Negatif'\n", "    else:\n", "        sentiment_label = 'Netral'\n", "    \n", "    details = {\n", "        'positive_score': positive_score,\n", "        'negative_score': negative_score,\n", "        'total_score': total_score,\n", "        'matched_words': matched_words,\n", "        'normalized_text': ' '.join(normalized_words)\n", "    }\n", "    \n", "    return sentiment_label, total_score, details\n", "''')\n", "\n", "print(f\"\\n🔧 Utility function saved: utils/lexicon_sentiment.py\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 LEXICON SETUP SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📚 LEXICON RESOURCES CREATED:\")\n", "print(f\"  ✅ Indonesian Slang Dictionary: {len(indonesian_slang_dict)} entries\")\n", "print(f\"  ✅ Indonesian Sentiment Lexicon: {len(indonesian_sentiment_lexicon)} entries\")\n", "print(f\"     • Positive words: {len(positive_words)}\")\n", "print(f\"     • Negative words: {len(negative_words)}\")\n", "\n", "print(f\"\\n💾 FILES GENERATED:\")\n", "print(f\"  📁 lexicons/\")\n", "print(f\"     • indonesian_lexicon_package.pkl (main package)\")\n", "print(f\"     • indonesian_lexicon_package.json (readable format)\")\n", "print(f\"     • indonesian_slang_dict.json\")\n", "print(f\"     • indonesian_sentiment_lexicon.json\")\n", "print(f\"  📁 utils/\")\n", "print(f\"     • lexicon_sentiment.py (utility functions)\")\n", "\n", "if 'df' in locals():\n", "    print(f\"\\n🔍 COVERAGE ANALYSIS:\")\n", "    print(f\"  • Dataset size: {len(df)} reviews\")\n", "    print(f\"  • Unique vocabulary: {len(unique_words):,} words\")\n", "    print(f\"  • Slang coverage: {slang_coverage:.1f}%\")\n", "    print(f\"  • Sentiment coverage: {sentiment_coverage:.1f}%\")\n", "\n", "print(f\"\\n🧪 VALIDATION RESULTS:\")\n", "print(f\"  ✅ Lexicon-based sentiment calculation tested\")\n", "print(f\"  ✅ Slang normalization validated\")\n", "print(f\"  ✅ Sentiment scoring verified\")\n", "print(f\"  ✅ Utility functions created\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. Advanced text preprocessing (03_text_preprocessing.ipynb)\")\n", "print(f\"  2. Feature extraction with TF-IDF (04_feature_extraction.ipynb)\")\n", "print(f\"  3. Score-based labeling (05_score_based_labeling.ipynb)\")\n", "print(f\"  4. Lexicon-based labeling (06_lexicon_based_labeling.ipynb)\")\n", "\n", "print(f\"\\n✅ LEXICON SETUP COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for text preprocessing phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}