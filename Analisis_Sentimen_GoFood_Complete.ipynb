{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Analisis <PERSON>n Comprehensive: Review GoFood Merchant di Google Play Store\n",
    "\n",
    "**Tujuan**: Melakukan analisis sentimen holistik dan comprehensive terhadap ulasan pengguna aplikasi GoFood Merchant di Google Play Store menggunakan metode Machine Learning.\n",
    "\n",
    "**Dataset**: reviews_gofood_Merchant.xlsx\n",
    "\n",
    "**Metode**: Random Forest dan XGBoost dengan preprocessing text yang optimal\n",
    "\n",
    "**Bahasa**: Indonesia"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Setup & Import Libraries"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Instalasi library yang diperlukan\n",
    "print(\"⚙️ Menginstal library yang diperlukan...\")\n",
    "!pip install pandas numpy matplotlib seaborn -q\n",
    "!pip install scikit-learn xgboost -q\n",
    "!pip install openpyxl sastrawi nltk -q\n",
    "!pip install wordcloud -q\n",
    "\n",
    "print(\"✅ Instalasi library selesai.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import semua library yang diperlukan\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import re\n",
    "import string\n",
    "from collections import Counter\n",
    "from wordcloud import WordCloud\n",
    "import nltk\n",
    "from nltk.corpus import stopwords\n",
    "from Sastrawi.Stemmer.StemmerFactory import StemmerFactory\n",
    "from sklearn.feature_extraction.text import TfidfVectorizer\n",
    "from sklearn.model_selection import train_test_split, RandomizedSearchCV\n",
    "from sklearn.ensemble import RandomForestClassifier\n",
    "from sklearn.naive_bayes import MultinomialNB\n",
    "from xgboost import XGBClassifier\n",
    "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n",
    "from sklearn.preprocessing import LabelEncoder\n",
    "import joblib\n",
    "import warnings\n",
    "from datetime import datetime\n",
    "\n",
    "warnings.filterwarnings('ignore')\n",
    "plt.style.use('default')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "# Download NLTK data\n",
    "try:\n",
    "    nltk.data.find('corpora/stopwords')\n",
    "except LookupError:\n",
    "    print(\"Mengunduh NLTK stopwords...\")\n",
    "    nltk.download('stopwords')\n",
    "    nltk.download('punkt')\n",
    "\n",
    "print(\"✅ Semua library berhasil diimpor dan dikonfigurasi.\")\n",
    "print(f\"📅 Waktu eksekusi: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Load dan Eksplorasi Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load dataset\n",
    "print(\"📂 Memuat dataset reviews_gofood_Merchant.xlsx...\")\n",
    "\n",
    "try:\n",
    "    df_raw = pd.read_excel('reviews_gofood_Merchant.xlsx', engine='openpyxl')\n",
    "    print(\"✅ Dataset berhasil dimuat!\")\n",
    "    \n",
    "    print(f\"\\n📊 Informasi Dataset:\")\n",
    "    print(f\"   • Jumlah baris: {df_raw.shape[0]:,}\")\n",
    "    print(f\"   • Jumlah kolom: {df_raw.shape[1]}\")\n",
    "    print(f\"   • Kolom tersedia: {list(df_raw.columns)}\")\n",
    "    \n",
    "    print(f\"\\n📋 Sample Data (5 baris pertama):\")\n",
    "    display(df_raw.head())\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(\"❌ Error: File 'reviews_gofood_Merchant.xlsx' tidak ditemukan!\")\n",
    "except Exception as e:\n",
    "    print(f\"❌ Error saat memuat dataset: {e}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analisis struktur data\n",
    "if 'df_raw' in locals():\n",
    "    print(\"🔍 Analisis Struktur Data:\")\n",
    "    print(\"=\"*30)\n",
    "    \n",
    "    print(\"\\n📈 Informasi Tipe Data:\")\n",
    "    print(df_raw.info())\n",
    "    \n",
    "    print(\"\\n📊 Statistik Deskriptif:\")\n",
    "    display(df_raw.describe(include='all'))\n",
    "    \n",
    "    # Identifikasi kolom penting\n",
    "    possible_review_cols = [col for col in df_raw.columns if any(keyword in col.lower() for keyword in ['review', 'ulasan', 'comment', 'text', 'content'])]\n",
    "    possible_rating_cols = [col for col in df_raw.columns if any(keyword in col.lower() for keyword in ['rating', 'score', 'nilai', 'star', 'bintang'])]\n",
    "    \n",
    "    print(f\"\\n🎯 Identifikasi Kolom:\")\n",
    "    print(f\"   • Kemungkinan kolom review: {possible_review_cols}\")\n",
    "    print(f\"   • Kemungkinan kolom rating: {possible_rating_cols}\")\n",
    "    \n",
    "    # Tentukan kolom utama\n",
    "    if possible_review_cols:\n",
    "        REVIEW_COLUMN = possible_review_cols[0]\n",
    "    else:\n",
    "        text_lengths = {}\n",
    "        for col in df_raw.select_dtypes(include=['object']).columns:\n",
    "            try:\n",
    "                avg_length = df_raw[col].astype(str).str.len().mean()\n",
    "                text_lengths[col] = avg_length\n",
    "            except:\n",
    "                pass\n",
    "        REVIEW_COLUMN = max(text_lengths, key=text_lengths.get) if text_lengths else df_raw.columns[0]\n",
    "    \n",
    "    if possible_rating_cols:\n",
    "        RATING_COLUMN = possible_rating_cols[0]\n",
    "    else:\n",
    "        numeric_cols = df_raw.select_dtypes(include=[np.number]).columns\n",
    "        RATING_COLUMN = numeric_cols[0] if len(numeric_cols) > 0 else None\n",
    "    \n",
    "    print(f\"\\n✅ Kolom yang akan digunakan:\")\n",
    "    print(f\"   • Review/Ulasan: '{REVIEW_COLUMN}'\")\n",
    "    print(f\"   • Rating/Nilai: '{RATING_COLUMN}'\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Visualisasi distribusi data\n",
    "if 'df_raw' in locals() and RATING_COLUMN:\n",
    "    df_clean = df_raw.dropna(subset=[REVIEW_COLUMN, RATING_COLUMN])\n",
    "    \n",
    "    print(\"📊 Analisis Distribusi:\")\n",
    "    print(f\"Data bersih: {len(df_clean):,} baris\")\n",
    "    \n",
    "    # Distribusi rating\n",
    "    rating_dist = df_clean[RATING_COLUMN].value_counts().sort_index()\n",
    "    print(f\"\\nDistribusi Rating:\")\n",
    "    print(rating_dist)\n",
    "    \n",
    "    # Visualisasi\n",
    "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
    "    \n",
    "    # Bar plot distribusi rating\n",
    "    rating_dist.plot(kind='bar', ax=axes[0,0], color='skyblue')\n",
    "    axes[0,0].set_title('Distribusi Rating')\n",
    "    axes[0,0].set_xlabel('Rating')\n",
    "    axes[0,0].set_ylabel('Jumlah Review')\n",
    "    \n",
    "    # Pie chart\n",
    "    rating_dist.plot(kind='pie', ax=axes[0,1], autopct='%1.1f%%')\n",
    "    axes[0,1].set_title('Proporsi Rating')\n",
    "    axes[0,1].set_ylabel('')\n",
    "    \n",
    "    # Histogram panjang review\n",
    "    review_lengths = df_clean[REVIEW_COLUMN].astype(str).str.len()\n",
    "    axes[1,0].hist(review_lengths, bins=30, color='lightgreen', alpha=0.7)\n",
    "    axes[1,0].set_title('Distribusi Panjang Review')\n",
    "    axes[1,0].set_xlabel('Panjang Karakter')\n",
    "    axes[1,0].set_ylabel('Frekuensi')\n",
    "    \n",
    "    # Box plot panjang review per rating\n",
    "    df_plot = pd.DataFrame({\n",
    "        'Rating': df_clean[RATING_COLUMN],\n",
    "        'Length': review_lengths\n",
    "    })\n",
    "    sns.boxplot(data=df_plot, x='Rating', y='Length', ax=axes[1,1])\n",
    "    axes[1,1].set_title('Panjang Review per Rating')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(f\"\\nStatistik Panjang Review:\")\n",
    "    print(f\"   • Rata-rata: {review_lengths.mean():.1f} karakter\")\n",
    "    print(f\"   • Median: {review_lengths.median():.1f} karakter\")\n",
    "    print(f\"   • Min: {review_lengths.min()} karakter\")\n",
    "    print(f\"   • Max: {review_lengths.max()} karakter\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Preprocessing Teks"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Setup preprocessing tools\n",
    "print(\"🔧 Setup Preprocessing Tools:\")\n",
    "\n",
    "# Stemmer\n",
    "factory = StemmerFactory()\n",
    "stemmer = factory.create_stemmer()\n",
    "\n",
    "# Stopwords\n",
    "indonesian_stopwords = set(stopwords.words('indonesian'))\n",
    "custom_stopwords = {\n",
    "    'aplikasi', 'app', 'gofood', 'merchant', 'google', 'play', 'store',\n",
    "    'android', 'ios', 'download', 'install', 'update', 'version',\n",
    "    'sangat', 'sekali', 'banget', 'sih', 'deh', 'nih', 'dong'\n",
    "}\n",
    "all_stopwords = indonesian_stopwords.union(custom_stopwords)\n",
    "\n",
    "# Kamus normalisasi\n",
    "kamus_normalisasi = {\n",
    "    'yg': 'yang', 'dg': 'dengan', 'dr': 'dari', 'utk': 'untuk',\n",
    "    'dgn': 'dengan', 'krn': 'karena', 'jd': 'jadi', 'jg': 'juga',\n",
    "    'tp': 'tapi', 'klo': 'kalau', 'hrs': 'harus', 'bkn': 'bukan',\n",
    "    'ga': 'tidak', 'gak': 'tidak', 'gk': 'tidak', 'tdk': 'tidak',\n",
    "    'udh': 'sudah', 'udah': 'sudah', 'sdh': 'sudah', 'blm': 'belum',\n",
    "    'bgt': 'banget', 'bngt': 'banget', 'bgs': 'bagus', 'bgus': 'bagus',\n",
    "    'byk': 'banyak', 'pake': 'pakai', 'bikin': 'buat', 'lgsg': 'langsung',\n",
    "    'orderan': 'pesanan', 'order': 'pesan', 'delivery': 'antar',\n",
    "    'driver': 'pengantar', 'resto': 'restoran', 'promo': 'promosi'\n",
    "}\n",
    "\n",
    "print(f\"✅ Stemmer siap\")\n",
    "print(f\"✅ Stopwords: {len(all_stopwords)} kata\")\n",
    "print(f\"✅ Kamus normalisasi: {len(kamus_normalisasi)} entri\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Fungsi preprocessing\n",
    "def preprocess_text(text):\n",
    "    if pd.isna(text) or text == '':\n",
    "        return ''\n",
    "    \n",
    "    text = str(text).lower()\n",
    "    \n",
    "    # Hapus URL, mention, hashtag\n",
    "    text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text)\n",
    "    text = re.sub(r'@[A-Za-z0-9_]+', '', text)\n",
    "    text = re.sub(r'#\\w+', '', text)\n",
    "    \n",
    "    # Hapus angka dan karakter khusus\n",
    "    text = re.sub(r'\\d+', '', text)\n",
    "    text = re.sub(r'[^a-zA-Z\\s]', ' ', text)\n",
    "    \n",
    "    # Normalisasi kata\n",
    "    words = text.split()\n",
    "    normalized_words = [kamus_normalisasi.get(word, word) for word in words]\n",
    "    text = ' '.join(normalized_words)\n",
    "    \n",
    "    # Hapus stopwords\n",
    "    words = text.split()\n",
    "    filtered_words = [word for word in words if word not in all_stopwords and len(word) > 2]\n",
    "    text = ' '.join(filtered_words)\n",
    "    \n",
    "    # Stemming\n",
    "    if text.strip():\n",
    "        text = stemmer.stem(text)\n",
    "    \n",
    "    # Hapus spasi berlebih\n",
    "    text = re.sub(r'\\s+', ' ', text).strip()\n",
    "    \n",
    "    return text\n",
    "\n",
    "print(\"✅ Fungsi preprocessing siap!\")\n",
    "\n",
    "# Test preprocessing\n",
    "test_text = \"Aplikasinya bagus bgt, tp kadang suka error nih. Gak bisa order makanan!\"\n",
    "processed = preprocess_text(test_text)\n",
    "print(f\"\\nTest preprocessing:\")\n",
    "print(f\"Original : {test_text}\")\n",
    "print(f\"Processed: {processed}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Terapkan preprocessing ke dataset\n",
    "if 'df_clean' in locals():\n",
    "    print(\"🔄 Menerapkan preprocessing ke dataset...\")\n",
    "    \n",
    "    df_processed = df_clean.copy()\n",
    "    df_processed['review_original'] = df_processed[REVIEW_COLUMN]\n",
    "    df_processed['review_processed'] = df_processed[REVIEW_COLUMN].apply(preprocess_text)\n",
    "    \n",
    "    # Hapus review kosong setelah preprocessing\n",
    "    df_processed = df_processed[df_processed['review_processed'].str.len() > 0]\n",
    "    \n",
    "    print(f\"Data setelah preprocessing: {len(df_processed):,} baris\")\n",
    "    \n",
    "    # Contoh hasil\n",
    "    print(\"\\n📋 Contoh Hasil Preprocessing:\")\n",
    "    for i in range(min(3, len(df_processed))):\n",
    "        original = df_processed['review_original'].iloc[i]\n",
    "        processed = df_processed['review_processed'].iloc[i]\n",
    "        rating = df_processed[RATING_COLUMN].iloc[i]\n",
    "        \n",
    "        print(f\"\\n{i+1}. Rating: {rating}\")\n",
    "        print(f\"   Original : {original}\")\n",
    "        print(f\"   Processed: {processed}\")\n",
    "    \n",
    "    print(\"\\n✅ Preprocessing selesai!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Labeling Sentimen"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Labeling sentimen berdasarkan rating\n",
    "if 'df_processed' in locals():\n",
    "    print(\"🏷️ Membuat Label Sentimen:\")\n",
    "    \n",
    "    def create_sentiment_label(rating):\n",
    "        if rating in [1, 2]:\n",
    "            return 'Negatif'\n",
    "        elif rating == 3:\n",
    "            return 'Netral'\n",
    "        elif rating in [4, 5]:\n",
    "            return 'Positif'\n",
    "        else:\n",
    "            return None\n",
    "    \n",
    "    df_processed['sentiment_label'] = df_processed[RATING_COLUMN].apply(create_sentiment_label)\n",
    "    \n",
    "    print(\"Distribusi Label Sebelum Filtering:\")\n",
    "    print(df_processed['sentiment_label'].value_counts())\n",
    "    \n",
    "    # Hapus sentimen netral untuk binary classification\n",
    "    df_final = df_processed[df_processed['sentiment_label'] != 'Netral'].copy()\n",
    "    \n",
    "    print(f\"\\nData final: {len(df_final):,} baris\")\n",
    "    print(\"Distribusi Label Final:\")\n",
    "    print(df_final['sentiment_label'].value_counts())\n",
    "    \n",
    "    # Konversi ke numerik\n",
    "    le = LabelEncoder()\n",
    "    df_final['sentiment_numeric'] = le.fit_transform(df_final['sentiment_label'])\n",
    "    \n",
    "    print(\"\\nMapping Label:\")\n",
    "    for i, label in enumerate(le.classes_):\n",
    "        print(f\"   {label} -> {i}\")\n",
    "    \n",
    "    # Visualisasi distribusi final\n",
    "    plt.figure(figsize=(10, 5))\n",
    "    \n",
    "    plt.subplot(1, 2, 1)\n",
    "    df_final['sentiment_label'].value_counts().plot(kind='bar', color=['red', 'green'])\n",
    "    plt.title('Distribusi Sentimen Final')\n",
    "    plt.ylabel('Jumlah Review')\n",
    "    plt.xticks(rotation=0)\n",
    "    \n",
    "    plt.subplot(1, 2, 2)\n",
    "    df_final['sentiment_label'].value_counts().plot(kind='pie', autopct='%1.1f%%', colors=['red', 'green'])\n",
    "    plt.title('Proporsi Sentimen')\n",
    "    plt.ylabel('')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Feature Engineering & Model Training"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Feature extraction dengan TF-IDF\n",
    "if 'df_final' in locals():\n",
    "    print(\"🔧 Feature Engineering dengan TF-IDF:\")\n",
    "    \n",
    "    X = df_final['review_processed'].fillna('')\n",
    "    y = df_final['sentiment_numeric']\n",
    "    \n",
    "    # TF-IDF Vectorizer dengan parameter optimal\n",
    "    vectorizer = TfidfVectorizer(\n",
    "        max_features=3000,\n",
    "        min_df=5,\n",
    "        ngram_range=(1, 2),\n",
    "        max_df=0.95\n",
    "    )\n",
    "    \n",
    "    X_tfidf = vectorizer.fit_transform(X)\n",
    "    \n",
    "    print(f\"✅ TF-IDF Matrix shape: {X_tfidf.shape}\")\n",
    "    print(f\"✅ Features: {X_tfidf.shape[1]:,}\")\n",
    "    print(f\"✅ Samples: {X_tfidf.shape[0]:,}\")\n",
    "    \n",
    "    # Split data\n",
    "    X_train, X_test, y_train, y_test = train_test_split(\n",
    "        X_tfidf, y, test_size=0.2, random_state=42, stratify=y\n",
    "    )\n",
    "    \n",
    "    print(f\"\\n📊 Data Split:\")\n",
    "    print(f\"   • Training: {X_train.shape[0]:,} samples\")\n",
    "    print(f\"   • Testing: {X_test.shape[0]:,} samples\")\n",
    "    \n",
    "    # Simpan vectorizer\n",
    "    joblib.dump(vectorizer, 'tfidf_vectorizer_comprehensive.pkl')\n",
    "    joblib.dump(le, 'label_encoder_comprehensive.pkl')\n",
    "    \n",
    "    print(\"\\n✅ Vectorizer dan Label Encoder disimpan!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Training Multiple Models\n",
    "if 'X_train' in locals():\n",
    "    print(\"🤖 Training Multiple Models:\")\n",
    "    print(\"=\"*30)\n",
    "    \n",
    "    models = {\n",
    "        'Naive Bayes': MultinomialNB(),\n",
    "        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),\n",
    "        'XGBoost': XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)\n",
    "    }\n",
    "    \n",
    "    results = {}\n",
    "    \n",
    "    for name, model in models.items():\n",
    "        print(f\"\\n🔄 Training {name}...\")\n",
    "        \n",
    "        # Train model\n",
    "        model.fit(X_train, y_train)\n",
    "        \n",
    "        # Predictions\n",
    "        y_pred = model.predict(X_test)\n",
    "        \n",
    "        # Metrics\n",
    "        accuracy = accuracy_score(y_test, y_pred)\n",
    "        \n",
    "        results[name] = {\n",
    "            'model': model,\n",
    "            'accuracy': accuracy,\n",
    "            'predictions': y_pred\n",
    "        }\n",
    "        \n",
    "        print(f\"✅ {name} - Accuracy: {accuracy:.4f}\")\n",
    "        \n",
    "        # Save model\n",
    "        joblib.dump(model, f'model_{name.lower().replace(\" \", \"_\")}_comprehensive.pkl')\n",
    "    \n",
    "    # Comparison\n",
    "    print(\"\\n📊 Model Comparison:\")\n",
    "    print(\"-\" * 40)\n",
    "    for name, result in results.items():\n",
    "        print(f\"{name:<15}: {result['accuracy']:.4f}\")\n",
    "    \n",
    "    # Best model\n",
    "    best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])\n",
    "    best_model = results[best_model_name]['model']\n",
    "    best_predictions = results[best_model_name]['predictions']\n",
    "    \n",
    "    print(f\"\\n🏆 Best Model: {best_model_name} ({results[best_model_name]['accuracy']:.4f})\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Model Evaluation & Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Detailed evaluation of best model\n",
    "if 'best_model' in locals():\n",
    "    print(f\"📊 Evaluasi Detail Model Terbaik: {best_model_name}\")\n",
    "    print(\"=\"*50)\n",
    "    \n",
    "    # Classification Report\n",
    "    print(\"\\n📋 Classification Report:\")\n",
    "    print(classification_report(y_test, best_predictions, target_names=le.classes_))\n",
    "    \n",
    "    # Confusion Matrix\n",
    "    cm = confusion_matrix(y_test, best_predictions)\n",
    "    \n",
    "    plt.figure(figsize=(12, 5))\n",
    "    \n",
    "    # Confusion Matrix Heatmap\n",
    "    plt.subplot(1, 2, 1)\n",
    "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n",
    "                xticklabels=le.classes_, yticklabels=le.classes_)\n",
    "    plt.title(f'Confusion Matrix - {best_model_name}')\n",
    "    plt.xlabel('Predicted')\n",
    "    plt.ylabel('Actual')\n",
    "    \n",
    "    # Model Accuracy Comparison\n",
    "    plt.subplot
