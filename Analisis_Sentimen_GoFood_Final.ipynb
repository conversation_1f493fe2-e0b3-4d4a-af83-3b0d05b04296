{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON> Comprehensive: Review GoFood Merchant\n", "\n", "**Tujuan**: <PERSON><PERSON><PERSON> sentimen holistik ulasan GoFood Merchant di Google Play Store\n", "\n", "**Dataset**: reviews_gofood_Merchant.xlsx\n", "\n", "**Metode**: Random Forest dan <PERSON>t\n", "\n", "**Bahasa**: Indonesia"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Instalasi library\n", "!pip install pandas numpy matplotlib seaborn scikit-learn xgboost openpyxl sastrawi nltk wordcloud -q\n", "\n", "# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import re\n", "from collections import Counter\n", "from wordcloud import WordCloud\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from Sastrawi.Stemmer.StemmerFactory import StemmerFactory\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.naive_bayes import MultinomialNB\n", "from xgboost import XGBClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "from sklearn.preprocessing import LabelEncoder\n", "import joblib\n", "import warnings\n", "from datetime import datetime\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('default')\n", "\n", "# Download NLTK data\n", "try:\n", "    nltk.data.find('corpora/stopwords')\n", "except LookupError:\n", "    nltk.download('stopwords')\n", "\n", "print(\"✅ Setup selesai!\")\n", "print(f\"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> dan <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load dataset\n", "try:\n", "    df_raw = pd.read_excel('reviews_gofood_Merchant.xlsx', engine='openpyxl')\n", "    print(\"✅ Dataset berhasil dimuat!\")\n", "    print(f\"Shape: {df_raw.shape}\")\n", "    print(f\"Columns: {list(df_raw.columns)}\")\n", "    display(df_raw.head())\n", "    \n", "    # Auto-detect kolom review dan rating\n", "    review_cols = [col for col in df_raw.columns if any(k in col.lower() for k in ['review', 'ulasan', 'comment', 'text'])]\n", "    rating_cols = [col for col in df_raw.columns if any(k in col.lower() for k in ['rating', 'score', 'nilai', 'star'])]\n", "    \n", "    REVIEW_COLUMN = review_cols[0] if review_cols else df_raw.select_dtypes(include=['object']).columns[0]\n", "    RATING_COLUMN = rating_cols[0] if rating_cols else df_raw.select_dtypes(include=[np.number]).columns[0]\n", "    \n", "    print(f\"\\n🎯 Kolom yang digunakan:\")\n", "    print(f\"Review: {REVIEW_COLUMN}\")\n", "    print(f\"Rating: {RATING_COLUMN}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Eksplorasi data\n", "if 'df_raw' in locals():\n", "    df_clean = df_raw.dropna(subset=[REVIEW_COLUMN, RATING_COLUMN])\n", "    \n", "    print(f\"📊 Data Analysis:\")\n", "    print(f\"Total data: {len(df_clean):,}\")\n", "    \n", "    # Distribusi rating\n", "    rating_dist = df_clean[RATING_COLUMN].value_counts().sort_index()\n", "    print(f\"\\nDistribusi Rating:\")\n", "    print(rating_dist)\n", "    \n", "    # Visualisasi\n", "    fig, axes = plt.subplots(1, 3, figsize=(15, 5))\n", "    \n", "    # Rating distribution\n", "    rating_dist.plot(kind='bar', ax=axes[0], color='skyblue')\n", "    axes[0].set_title('Distribusi Rating')\n", "    axes[0].set_ylabel('Jumlah Review')\n", "    \n", "    # Rating pie chart\n", "    rating_dist.plot(kind='pie', ax=axes[1], autopct='%1.1f%%')\n", "    axes[1].set_title('<PERSON><PERSON><PERSON> Rating')\n", "    axes[1].set_ylabel('')\n", "    \n", "    # Review length distribution\n", "    review_lengths = df_clean[REVIEW_COLUMN].astype(str).str.len()\n", "    axes[2].hist(review_lengths, bins=30, color='lightgreen', alpha=0.7)\n", "    axes[2].set_title('Distribusi Panjang Review')\n", "    axes[2].set_xlabel('Panjang Karakter')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\nStatistik Review:\")\n", "    print(f\"Rata-rata panjang: {review_lengths.mean():.1f} karakter\")\n", "    print(f\"Median panjang: {review_lengths.median():.1f} karakter\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Text Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup preprocessing tools\n", "factory = StemmerFactory()\n", "stemmer = factory.create_stemmer()\n", "\n", "# Stopwords\n", "indonesian_stopwords = set(stopwords.words('indonesian'))\n", "custom_stopwords = {'aplikasi', 'app', 'gofood', 'merchant', 'google', 'play', 'store', 'android', 'ios'}\n", "all_stopwords = indonesian_stopwords.union(custom_stopwords)\n", "\n", "# Normalization dictionary\n", "kamus_normalisasi = {\n", "    'yg': 'yang', 'dg': 'dengan', 'dr': 'dari', 'utk': 'untuk', 'dgn': 'dengan',\n", "    'krn': 'karena', 'jd': 'jadi', 'jg': 'juga', 'tp': 'tapi', 'klo': 'kalau',\n", "    'ga': 'tidak', 'gak': 'tidak', 'gk': 'tidak', 'tdk': 'tidak',\n", "    'udh': 'sudah', 'udah': 'sudah', 'sdh': 'sudah', 'blm': 'belum',\n", "    'bgt': 'banget', 'bgs': 'bagus', 'byk': 'banyak', 'pake': 'pakai',\n", "    'orderan': 'pesanan', 'order': 'pesan', 'delivery': 'antar'\n", "}\n", "\n", "def preprocess_text(text):\n", "    if pd.isna(text) or text == '':\n", "        return ''\n", "    \n", "    text = str(text).lower()\n", "    text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text)  # Remove URLs\n", "    text = re.sub(r'@[A-Za-z0-9_]+', '', text)  # Remove mentions\n", "    text = re.sub(r'#\\w+', '', text)  # Remove hashtags\n", "    text = re.sub(r'\\d+', '', text)  # Remove numbers\n", "    text = re.sub(r'[^a-zA-Z\\s]', ' ', text)  # Keep only letters\n", "    \n", "    # Normalize words\n", "    words = text.split()\n", "    normalized_words = [kamus_normalisasi.get(word, word) for word in words]\n", "    text = ' '.join(normalized_words)\n", "    \n", "    # Remove stopwords\n", "    words = text.split()\n", "    filtered_words = [word for word in words if word not in all_stopwords and len(word) > 2]\n", "    text = ' '.join(filtered_words)\n", "    \n", "    # Stemming\n", "    if text.strip():\n", "        text = stemmer.stem(text)\n", "    \n", "    return re.sub(r'\\s+', ' ', text).strip()\n", "\n", "print(\"✅ Preprocessing tools ready!\")\n", "\n", "# Test preprocessing\n", "test_text = \"Aplikasinya bagus bgt, tp kadang error. Gak bisa order makanan!\"\n", "processed = preprocess_text(test_text)\n", "print(f\"\\nTest:\")\n", "print(f\"Original : {test_text}\")\n", "print(f\"Processed: {processed}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply preprocessing\n", "if 'df_clean' in locals():\n", "    print(\"🔄 Applying preprocessing...\")\n", "    \n", "    df_processed = df_clean.copy()\n", "    df_processed['review_original'] = df_processed[REVIEW_COLUMN]\n", "    df_processed['review_processed'] = df_processed[REVIEW_COLUMN].apply(preprocess_text)\n", "    \n", "    # Remove empty reviews\n", "    df_processed = df_processed[df_processed['review_processed'].str.len() > 0]\n", "    \n", "    print(f\"Data after preprocessing: {len(df_processed):,}\")\n", "    \n", "    # Show examples\n", "    print(\"\\n📋 Preprocessing Examples:\")\n", "    for i in range(min(3, len(df_processed))):\n", "        original = df_processed['review_original'].iloc[i]\n", "        processed = df_processed['review_processed'].iloc[i]\n", "        rating = df_processed[RATING_COLUMN].iloc[i]\n", "        \n", "        print(f\"\\n{i+1}. Rating: {rating}\")\n", "        print(f\"   Original : {original}\")\n", "        print(f\"   Processed: {processed}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Sentiment Labeling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sentiment labels\n", "if 'df_processed' in locals():\n", "    def create_sentiment_label(rating):\n", "        if rating in [1, 2]:\n", "            return 'Negatif'\n", "        elif rating == 3:\n", "            return 'Netral'\n", "        elif rating in [4, 5]:\n", "            return 'Positif'\n", "        return None\n", "    \n", "    df_processed['sentiment_label'] = df_processed[RATING_COLUMN].apply(create_sentiment_label)\n", "    \n", "    print(\"📊 Label Distribution (Before):\")\n", "    print(df_processed['sentiment_label'].value_counts())\n", "    \n", "    # Remove neutral for binary classification\n", "    df_final = df_processed[df_processed['sentiment_label'] != 'Netral'].copy()\n", "    \n", "    print(f\"\\nFinal data: {len(df_final):,}\")\n", "    print(\"\\nFinal Label Distribution:\")\n", "    print(df_final['sentiment_label'].value_counts())\n", "    \n", "    # Convert to numeric\n", "    le = LabelEncoder()\n", "    df_final['sentiment_numeric'] = le.fit_transform(df_final['sentiment_label'])\n", "    \n", "    print(\"\\nLabel Mapping:\")\n", "    for i, label in enumerate(le.classes_):\n", "        print(f\"   {label} -> {i}\")\n", "    \n", "    # Visualize final distribution\n", "    plt.figure(figsize=(10, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    df_final['sentiment_label'].value_counts().plot(kind='bar', color=['red', 'green'])\n", "    plt.title('Final Sentiment Distribution')\n", "    plt.xticks(rotation=0)\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    df_final['sentiment_label'].value_counts().plot(kind='pie', autopct='%1.1f%%', colors=['red', 'green'])\n", "    plt.title('Sentiment Proportion')\n", "    plt.ylabel('')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Machine Learning Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature extraction and model training\n", "if 'df_final' in locals():\n", "    print(\"🔧 Feature Engineering with TF-IDF...\")\n", "    \n", "    X = df_final['review_processed'].fillna('')\n", "    y = df_final['sentiment_numeric']\n", "    \n", "    # TF-IDF Vectorizer\n", "    vectorizer = TfidfVectorizer(max_features=3000, min_df=5, ngram_range=(1, 2), max_df=0.95)\n", "    X_tfidf = vectorizer.fit_transform(X)\n", "    \n", "    print(f\"TF-IDF shape: {X_tfidf.shape}\")\n", "    \n", "    # Train-test split\n", "    X_train, X_test, y_train, y_test = train_test_split(X_tfidf, y, test_size=0.2, random_state=42, stratify=y)\n", "    \n", "    print(f\"Training: {X_train.shape[0]:,}, Testing: {X_test.shape[0]:,}\")\n", "    \n", "    # Train multiple models\n", "    models = {\n", "        'Naive Bayes': MultinomialNB(),\n", "        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),\n", "        'XGBoost': XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)\n", "    }\n", "    \n", "    results = {}\n", "    \n", "    print(\"\\n🤖 Training Models:\")\n", "    for name, model in models.items():\n", "        print(f\"Training {name}...\")\n", "        model.fit(X_train, y_train)\n", "        y_pred = model.predict(X_test)\n", "        accuracy = accuracy_score(y_test, y_pred)\n", "        \n", "        results[name] = {'model': model, 'accuracy': accuracy, 'predictions': y_pred}\n", "        print(f\"✅ {name}: {accuracy:.4f}\")\n", "        \n", "        # Save model\n", "        joblib.dump(model, f'model_{name.lower().replace(\" \", \"_\")}.pkl')\n", "    \n", "    # Save preprocessing tools\n", "    joblib.dump(vectorizer, 'tfidf_vectorizer.pkl')\n", "    joblib.dump(le, 'label_encoder.pkl')\n", "    \n", "    # Best model\n", "    best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])\n", "    best_model = results[best_model_name]['model']\n", "    best_predictions = results[best_model_name]['predictions']\n", "    \n", "    print(f\"\\n🏆 Best Model: {best_model_name} ({results[best_model_name]['accuracy']:.4f})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Model Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detailed evaluation\n", "if 'best_model' in locals():\n", "    print(f\"📊 Detailed Evaluation: {best_model_name}\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Classification report\n", "    print(\"\\nClassification Report:\")\n", "    print(classification_report(y_test, best_predictions, target_names=le.classes_))\n", "    \n", "    # Confusion matrix\n", "    cm = confusion_matrix(y_test, best_predictions)\n", "    \n", "    plt.figure(figsize=(12, 5))\n", "    \n", "    # Confusion matrix heatmap\n", "    plt.subplot(1, 2, 1)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=le.classes_, yticklabels=le.classes_)\n", "    plt.title(f'Confusion Matrix - {best_model_name}')\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "    \n", "    # Model comparison\n", "    plt.subplot(1, 2, 2)\n", "    model_names = list(results.keys())\n", "    accuracies = [results[name]['accuracy'] for name in model_names]\n", "    \n", "    bars = plt.bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'orange'])\n", "    plt.title('Model Accuracy Comparison')\n", "    plt.ylabel('Accuracy')\n", "    plt.ylim(0, 1)\n", "    \n", "    for bar, acc in zip(bars, accuracies):\n", "        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, f'{acc:.3f}', ha='center')\n", "    \n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Feature importance (if available)\n", "    if hasattr(best_model, 'feature_importances_'):\n", "        print(f\"\\n🔍 Top 15 Important Features:\")\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        importances = best_model.feature_importances_\n", "        \n", "        top_indices = np.argsort(importances)[-15:]\n", "        top_features = [(feature_names[i], importances[i]) for i in top_indices]\n", "        top_features.reverse()\n", "        \n", "        for i, (feature, importance) in enumerate(top_features, 1):\n", "            print(f\"{i:2d}. {feature:<20} : {importance:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Prediction Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create prediction function\n", "def predict_sentiment(text):\n", "    \"\"\"Predict sentiment of a given text\"\"\"\n", "    processed_text = preprocess_text(text)\n", "    if not processed_text:\n", "        return \"Text too short after preprocessing\"\n", "    \n", "    text_vector = vectorizer.transform([processed_text])\n", "    prediction = best_model.predict(text_vector)[0]\n", "    probability = best_model.predict_proba(text_vector)[0]\n", "    \n", "    sentiment = le.inverse_transform([prediction])[0]\n", "    confidence = max(probability)\n", "    \n", "    return {\n", "        'text': text,\n", "        'processed': processed_text,\n", "        'sentiment': sentiment,\n", "        'confidence': confidence\n", "    }\n", "\n", "# Test predictions\n", "if 'best_model' in locals():\n", "    print(\"🧪 Testing Prediction Function:\")\n", "    \n", "    test_reviews = [\n", "        \"Aplikasi GoFood Merchant sangat bagus dan mudah digunakan!\",\n", "        \"Aplikasinya sering error dan lambat, sangat mengecewakan\",\n", "        \"Fitur orderan tidak berfungsi dengan baik, perlu diperbaiki\",\n", "        \"<PERSON><PERSON><PERSON>, membantu bisnis saya berkembang\"\n", "    ]\n", "    \n", "    for i, review in enumerate(test_reviews, 1):\n", "        result = predict_sentiment(review)\n", "        print(f\"\\n{i}. Text: {result['text']}\")\n", "        print(f\"   Sentiment: {result['sentiment']} (Confidence: {result['confidence']:.3f})\")\n", "    \n", "    print(\"\\n✅ Prediction function ready for use!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Summary & Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary\n", "if 'results' in locals():\n", "    print(\"📋 ANALISIS SENTIMEN GOFOOD MERCHANT - SUMMARY\")\n", "    print(\"=\" * 60)\n", "    \n", "    print(f\"\\n📊 Dataset Information:\")\n", "    print(f\"   • Total reviews processed: {len(df_final):,}\")\n", "    print(f\"   • Positive reviews: {sum(df_final['sentiment_label'] == 'Positif'):,}\")\n", "    print(f\"   • Negative reviews: {sum(df_final['sentiment_label'] == 'Negatif'):,}\")\n", "    \n", "    print(f\"\\n🤖 Model Performance:\")\n", "    for name, result in results.items():\n", "        print(f\"   • {name:<15}: {result['accuracy']:.4f}\")\n", "    \n", "    print(f\"\\n🏆 Best Model: {best_model_name}\")\n", "    print(f\"   • Accuracy: {results[best_model_name]['accuracy']:.4f}\")\n", "    \n", "    print(f\"\\n💾 Saved Files:\")\n", "    print(f\"   • tfidf_vectorizer.pkl\")\n", "    print(f\"   • label_encoder.pkl\")\n", "    for name in models.keys():\n", "        print(f\"   • model_{name.lower().replace(' ', '_')}.pkl\")\n", "    \n", "    print(f\"\\n🎯 Business Insights:\")\n", "    pos_pct = (sum(df_final['sentiment_label'] == 'Positif') / len(df_final)) * 100\n", "    neg_pct = (sum(df_final['sentiment_label'] == 'Negatif') / len(df_final)) * 100\n", "    \n", "    print(f\"   • {pos_pct:.1f}% reviews are positive\")\n", "    print(f\"   • {neg_pct:.1f}% reviews are negative\")\n", "    \n", "    if pos_pct > neg_pct:\n", "        print(f\"   • Overall sentiment: POSITIVE 😊\")\n", "    else:\n", "        print(f\"   • Overall sentiment: NEGATIVE 😞\")\n", "    \n", "    print(f\"\\n✅ Analysis completed successfully!\")\n", "    print(f\"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}}