# Instalasi library
!pip install pandas numpy matplotlib seaborn scikit-learn xgboost openpyxl sastrawi nltk wordcloud -q

# Import libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from collections import Counter
from wordcloud import WordCloud
import nltk
from nltk.corpus import stopwords
from Sastrawi.Stemmer.StemmerFactory import StemmerFactory
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import MultinomialNB
from xgboost import XGBClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import LabelEncoder
import joblib
import warnings
from datetime import datetime

warnings.filterwarnings('ignore')
plt.style.use('default')

# Download NLTK data
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

print("✅ Setup selesai!")
print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Load dataset
try:
    df_raw = pd.read_excel('reviews_gofood_Merchant.xlsx', engine='openpyxl')
    print("✅ Dataset berhasil dimuat!")
    print(f"Shape: {df_raw.shape}")
    print(f"Columns: {list(df_raw.columns)}")
    display(df_raw.head())
    
    # Auto-detect kolom review dan rating
    review_cols = [col for col in df_raw.columns if any(k in col.lower() for k in ['review', 'ulasan', 'comment', 'text'])]
    rating_cols = [col for col in df_raw.columns if any(k in col.lower() for k in ['rating', 'score', 'nilai', 'star'])]
    
    REVIEW_COLUMN = review_cols[0] if review_cols else df_raw.select_dtypes(include=['object']).columns[0]
    RATING_COLUMN = rating_cols[0] if rating_cols else df_raw.select_dtypes(include=[np.number]).columns[0]
    
    print(f"\n🎯 Kolom yang digunakan:")
    print(f"Review: {REVIEW_COLUMN}")
    print(f"Rating: {RATING_COLUMN}")
    
except Exception as e:
    print(f"❌ Error: {e}")

# Eksplorasi data
if 'df_raw' in locals():
    df_clean = df_raw.dropna(subset=[REVIEW_COLUMN, RATING_COLUMN])
    
    print(f"📊 Data Analysis:")
    print(f"Total data: {len(df_clean):,}")
    
    # Distribusi rating
    rating_dist = df_clean[RATING_COLUMN].value_counts().sort_index()
    print(f"\nDistribusi Rating:")
    print(rating_dist)
    
    # Visualisasi
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Rating distribution
    rating_dist.plot(kind='bar', ax=axes[0], color='skyblue')
    axes[0].set_title('Distribusi Rating')
    axes[0].set_ylabel('Jumlah Review')
    
    # Rating pie chart
    rating_dist.plot(kind='pie', ax=axes[1], autopct='%1.1f%%')
    axes[1].set_title('Proporsi Rating')
    axes[1].set_ylabel('')
    
    # Review length distribution
    review_lengths = df_clean[REVIEW_COLUMN].astype(str).str.len()
    axes[2].hist(review_lengths, bins=30, color='lightgreen', alpha=0.7)
    axes[2].set_title('Distribusi Panjang Review')
    axes[2].set_xlabel('Panjang Karakter')
    
    plt.tight_layout()
    plt.show()
    
    print(f"\nStatistik Review:")
    print(f"Rata-rata panjang: {review_lengths.mean():.1f} karakter")
    print(f"Median panjang: {review_lengths.median():.1f} karakter")

# Setup preprocessing tools
factory = StemmerFactory()
stemmer = factory.create_stemmer()

# Stopwords
indonesian_stopwords = set(stopwords.words('indonesian'))
custom_stopwords = {'aplikasi', 'app', 'gofood', 'merchant', 'google', 'play', 'store', 'android', 'ios'}
all_stopwords = indonesian_stopwords.union(custom_stopwords)

# Normalization dictionary
kamus_normalisasi = {
    'yg': 'yang', 'dg': 'dengan', 'dr': 'dari', 'utk': 'untuk', 'dgn': 'dengan',
    'krn': 'karena', 'jd': 'jadi', 'jg': 'juga', 'tp': 'tapi', 'klo': 'kalau',
    'ga': 'tidak', 'gak': 'tidak', 'gk': 'tidak', 'tdk': 'tidak',
    'udh': 'sudah', 'udah': 'sudah', 'sdh': 'sudah', 'blm': 'belum',
    'bgt': 'banget', 'bgs': 'bagus', 'byk': 'banyak', 'pake': 'pakai',
    'orderan': 'pesanan', 'order': 'pesan', 'delivery': 'antar'
}

def preprocess_text(text):
    if pd.isna(text) or text == '':
        return ''
    
    text = str(text).lower()
    text = re.sub(r'http\S+|www\S+|https\S+', '', text)  # Remove URLs
    text = re.sub(r'@[A-Za-z0-9_]+', '', text)  # Remove mentions
    text = re.sub(r'#\w+', '', text)  # Remove hashtags
    text = re.sub(r'\d+', '', text)  # Remove numbers
    text = re.sub(r'[^a-zA-Z\s]', ' ', text)  # Keep only letters
    
    # Normalize words
    words = text.split()
    normalized_words = [kamus_normalisasi.get(word, word) for word in words]
    text = ' '.join(normalized_words)
    
    # Remove stopwords
    words = text.split()
    filtered_words = [word for word in words if word not in all_stopwords and len(word) > 2]
    text = ' '.join(filtered_words)
    
    # Stemming
    if text.strip():
        text = stemmer.stem(text)
    
    return re.sub(r'\s+', ' ', text).strip()

print("✅ Preprocessing tools ready!")

# Test preprocessing
test_text = "Aplikasinya bagus bgt, tp kadang error. Gak bisa order makanan!"
processed = preprocess_text(test_text)
print(f"\nTest:")
print(f"Original : {test_text}")
print(f"Processed: {processed}")

# Apply preprocessing
if 'df_clean' in locals():
    print("🔄 Applying preprocessing...")
    
    df_processed = df_clean.copy()
    df_processed['review_original'] = df_processed[REVIEW_COLUMN]
    df_processed['review_processed'] = df_processed[REVIEW_COLUMN].apply(preprocess_text)
    
    # Remove empty reviews
    df_processed = df_processed[df_processed['review_processed'].str.len() > 0]
    
    print(f"Data after preprocessing: {len(df_processed):,}")
    
    # Show examples
    print("\n📋 Preprocessing Examples:")
    for i in range(min(3, len(df_processed))):
        original = df_processed['review_original'].iloc[i]
        processed = df_processed['review_processed'].iloc[i]
        rating = df_processed[RATING_COLUMN].iloc[i]
        
        print(f"\n{i+1}. Rating: {rating}")
        print(f"   Original : {original}")
        print(f"   Processed: {processed}")

# Create sentiment labels
if 'df_processed' in locals():
    def create_sentiment_label(rating):
        if rating in [1, 2]:
            return 'Negatif'
        elif rating == 3:
            return 'Netral'
        elif rating in [4, 5]:
            return 'Positif'
        return None
    
    df_processed['sentiment_label'] = df_processed[RATING_COLUMN].apply(create_sentiment_label)
    
    print("📊 Label Distribution (Before):")
    print(df_processed['sentiment_label'].value_counts())
    
    # Remove neutral for binary classification
    df_final = df_processed[df_processed['sentiment_label'] != 'Netral'].copy()
    
    print(f"\nFinal data: {len(df_final):,}")
    print("\nFinal Label Distribution:")
    print(df_final['sentiment_label'].value_counts())
    
    # Convert to numeric
    le = LabelEncoder()
    df_final['sentiment_numeric'] = le.fit_transform(df_final['sentiment_label'])
    
    print("\nLabel Mapping:")
    for i, label in enumerate(le.classes_):
        print(f"   {label} -> {i}")
    
    # Visualize final distribution
    plt.figure(figsize=(10, 4))
    
    plt.subplot(1, 2, 1)
    df_final['sentiment_label'].value_counts().plot(kind='bar', color=['red', 'green'])
    plt.title('Final Sentiment Distribution')
    plt.xticks(rotation=0)
    
    plt.subplot(1, 2, 2)
    df_final['sentiment_label'].value_counts().plot(kind='pie', autopct='%1.1f%%', colors=['red', 'green'])
    plt.title('Sentiment Proportion')
    plt.ylabel('')
    
    plt.tight_layout()
    plt.show()

# Feature extraction and model training
if 'df_final' in locals():
    print("🔧 Feature Engineering with TF-IDF...")
    
    X = df_final['review_processed'].fillna('')
    y = df_final['sentiment_numeric']
    
    # TF-IDF Vectorizer
    vectorizer = TfidfVectorizer(max_features=3000, min_df=5, ngram_range=(1, 2), max_df=0.95)
    X_tfidf = vectorizer.fit_transform(X)
    
    print(f"TF-IDF shape: {X_tfidf.shape}")
    
    # Train-test split
    X_train, X_test, y_train, y_test = train_test_split(X_tfidf, y, test_size=0.2, random_state=42, stratify=y)
    
    print(f"Training: {X_train.shape[0]:,}, Testing: {X_test.shape[0]:,}")
    
    # Train multiple models
    models = {
        'Naive Bayes': MultinomialNB(),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
        'XGBoost': XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)
    }
    
    results = {}
    
    print("\n🤖 Training Models:")
    for name, model in models.items():
        print(f"Training {name}...")
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        results[name] = {'model': model, 'accuracy': accuracy, 'predictions': y_pred}
        print(f"✅ {name}: {accuracy:.4f}")
        
        # Save model
        joblib.dump(model, f'model_{name.lower().replace(" ", "_")}.pkl')
    
    # Save preprocessing tools
    joblib.dump(vectorizer, 'tfidf_vectorizer.pkl')
    joblib.dump(le, 'label_encoder.pkl')
    
    # Best model
    best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
    best_model = results[best_model_name]['model']
    best_predictions = results[best_model_name]['predictions']
    
    print(f"\n🏆 Best Model: {best_model_name} ({results[best_model_name]['accuracy']:.4f})")

# Detailed evaluation
if 'best_model' in locals():
    print(f"📊 Detailed Evaluation: {best_model_name}")
    print("=" * 50)
    
    # Classification report
    print("\nClassification Report:")
    print(classification_report(y_test, best_predictions, target_names=le.classes_))
    
    # Confusion matrix
    cm = confusion_matrix(y_test, best_predictions)
    
    plt.figure(figsize=(12, 5))
    
    # Confusion matrix heatmap
    plt.subplot(1, 2, 1)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=le.classes_, yticklabels=le.classes_)
    plt.title(f'Confusion Matrix - {best_model_name}')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    
    # Model comparison
    plt.subplot(1, 2, 2)
    model_names = list(results.keys())
    accuracies = [results[name]['accuracy'] for name in model_names]
    
    bars = plt.bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'orange'])
    plt.title('Model Accuracy Comparison')
    plt.ylabel('Accuracy')
    plt.ylim(0, 1)
    
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, f'{acc:.3f}', ha='center')
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
    
    # Feature importance (if available)
    if hasattr(best_model, 'feature_importances_'):
        print(f"\n🔍 Top 15 Important Features:")
        feature_names = vectorizer.get_feature_names_out()
        importances = best_model.feature_importances_
        
        top_indices = np.argsort(importances)[-15:]
        top_features = [(feature_names[i], importances[i]) for i in top_indices]
        top_features.reverse()
        
        for i, (feature, importance) in enumerate(top_features, 1):
            print(f"{i:2d}. {feature:<20} : {importance:.4f}")

# Create prediction function
def predict_sentiment(text):
    """Predict sentiment of a given text"""
    processed_text = preprocess_text(text)
    if not processed_text:
        return "Text too short after preprocessing"
    
    text_vector = vectorizer.transform([processed_text])
    prediction = best_model.predict(text_vector)[0]
    probability = best_model.predict_proba(text_vector)[0]
    
    sentiment = le.inverse_transform([prediction])[0]
    confidence = max(probability)
    
    return {
        'text': text,
        'processed': processed_text,
        'sentiment': sentiment,
        'confidence': confidence
    }

# Test predictions
if 'best_model' in locals():
    print("🧪 Testing Prediction Function:")
    
    test_reviews = [
        "Aplikasi GoFood Merchant sangat bagus dan mudah digunakan!",
        "Aplikasinya sering error dan lambat, sangat mengecewakan",
        "Fitur orderan tidak berfungsi dengan baik, perlu diperbaiki",
        "Mantap aplikasinya, membantu bisnis saya berkembang"
    ]
    
    for i, review in enumerate(test_reviews, 1):
        result = predict_sentiment(review)
        print(f"\n{i}. Text: {result['text']}")
        print(f"   Sentiment: {result['sentiment']} (Confidence: {result['confidence']:.3f})")
    
    print("\n✅ Prediction function ready for use!")

# Final summary
if 'results' in locals():
    print("📋 ANALISIS SENTIMEN GOFOOD MERCHANT - SUMMARY")
    print("=" * 60)
    
    print(f"\n📊 Dataset Information:")
    print(f"   • Total reviews processed: {len(df_final):,}")
    print(f"   • Positive reviews: {sum(df_final['sentiment_label'] == 'Positif'):,}")
    print(f"   • Negative reviews: {sum(df_final['sentiment_label'] == 'Negatif'):,}")
    
    print(f"\n🤖 Model Performance:")
    for name, result in results.items():
        print(f"   • {name:<15}: {result['accuracy']:.4f}")
    
    print(f"\n🏆 Best Model: {best_model_name}")
    print(f"   • Accuracy: {results[best_model_name]['accuracy']:.4f}")
    
    print(f"\n💾 Saved Files:")
    print(f"   • tfidf_vectorizer.pkl")
    print(f"   • label_encoder.pkl")
    for name in models.keys():
        print(f"   • model_{name.lower().replace(' ', '_')}.pkl")
    
    print(f"\n🎯 Business Insights:")
    pos_pct = (sum(df_final['sentiment_label'] == 'Positif') / len(df_final)) * 100
    neg_pct = (sum(df_final['sentiment_label'] == 'Negatif') / len(df_final)) * 100
    
    print(f"   • {pos_pct:.1f}% reviews are positive")
    print(f"   • {neg_pct:.1f}% reviews are negative")
    
    if pos_pct > neg_pct:
        print(f"   • Overall sentiment: POSITIVE 😊")
    else:
        print(f"   • Overall sentiment: NEGATIVE 😞")
    
    print(f"\n✅ Analysis completed successfully!")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")