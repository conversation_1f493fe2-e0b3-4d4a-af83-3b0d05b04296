{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏷️ 05. Score-Based Sentiment Labeling\n", "\n", "**Tujuan**: Membuat label sentimen berdasarkan rating/score yang diberikan pengguna\n", "\n", "**Key Features**:\n", "1. 📊 **Rating Analysis** - Analisis distribusi rating\n", "2. 🎯 **Label Mapping** - Mapping rating ke sentimen\n", "3. ⚖️ **Class Balance** - <PERSON><PERSON><PERSON> k<PERSON> kelas\n", "4. 🔄 **Multiple Strategies** - <PERSON><PERSON><PERSON><PERSON> strategi pelabelan\n", "5. 📈 **Quality Assessment** - Evaluasi kualitas label\n", "6. 💾 **Label Persistence** - <PERSON><PERSON><PERSON> hasil pela<PERSON>an\n", "\n", "**Input**: Dataset dengan kolom rating/nilai  \n", "**Output**: Dataset dengan label sentimen score-based\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter\n", "import json\n", "\n", "# Machine learning libraries\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.utils.class_weight import compute_class_weight\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import joblib\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "\n", "print(\"🏷️ Score-based labeling libraries imported successfully!\")\n", "print(f\"🕐 Labeling started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Load Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the preprocessed dataset\n", "try:\n", "    df = pd.read_excel('gofood_advanced_preprocessed.xlsx')\n", "    print(f\"✅ Advanced preprocessed dataset loaded: {len(df)} reviews\")\n", "    print(f\"📋 Columns: {list(df.columns)}\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"⚠️ Advanced preprocessed file not found. Trying alternatives...\")\n", "    try:\n", "        df = pd.read_excel('gofood_ulasan_preprocessed.xlsx')\n", "        print(f\"✅ Basic preprocessed dataset loaded: {len(df)} reviews\")\n", "        # Ensure we have the required columns\n", "        if 'ulasan_stemmed' in df.columns:\n", "            df['final_processed_text'] = df['ulasan_stemmed']\n", "    except FileNotFoundError:\n", "        print(\"❌ No preprocessed data found. Creating sample data...\")\n", "        sample_data = {\n", "            'final_processed_text': [\n", "                'makanan enak cepat sampai driver ramah',\n", "                'pelayanan buruk makanan dingin tidak enak',\n", "                'lumayan oke tapi agak lama tunggu',\n", "                'mantap gofood terbaik recommended sekali',\n", "                'aplikasi sering error susah order',\n", "                'makanan segar enak harga terjangkau',\n", "                'driver tidak sopan makanan tumpah',\n", "                'biasa saja tidak istimewa',\n", "                'sangat puas pelayanan excellent',\n", "                'mengecewakan sekali tidak recommended'\n", "            ],\n", "            'nilai': [5, 1, 3, 5, 2, 4, 1, 3, 5, 1]\n", "        }\n", "        df = pd.DataFrame(sample_data)\n", "        print(f\"📝 Sample dataset created with {len(df)} reviews\")\n", "\n", "# Identify rating column\n", "rating_column = None\n", "for col in ['nilai', 'rating', 'score', 'stars']:\n", "    if col in df.columns:\n", "        rating_column = col\n", "        break\n", "\n", "if rating_column is None:\n", "    print(\"❌ No rating column found. Cannot proceed with score-based labeling.\")\n", "    raise ValueError(\"Rating column is required for score-based labeling\")\n", "\n", "print(f\"📊 Using rating column: {rating_column}\")\n", "\n", "# Basic dataset info\n", "print(f\"\\n📈 Dataset Overview:\")\n", "print(f\"  • Total reviews: {len(df)}\")\n", "print(f\"  • Rating column: {rating_column}\")\n", "print(f\"  • Rating range: {df[rating_column].min()} - {df[rating_column].max()}\")\n", "print(f\"  • Missing ratings: {df[rating_column].isna().sum()}\")\n", "\n", "# Remove rows with missing ratings\n", "if df[rating_column].isna().sum() > 0:\n", "    df = df.dropna(subset=[rating_column])\n", "    print(f\"  • After removing missing ratings: {len(df)} reviews\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Rating Distribution Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive rating analysis\n", "print(\"📊 RATING DISTRIBUTION ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Basic statistics\n", "rating_stats = df[rating_column].describe()\n", "print(f\"\\n📈 Rating Statistics:\")\n", "print(f\"  • Count: {rating_stats['count']:.0f}\")\n", "print(f\"  • Mean: {rating_stats['mean']:.2f}\")\n", "print(f\"  • Median: {rating_stats['50%']:.2f}\")\n", "print(f\"  • Std Dev: {rating_stats['std']:.2f}\")\n", "print(f\"  • Min: {rating_stats['min']:.0f}\")\n", "print(f\"  • Max: {rating_stats['max']:.0f}\")\n", "\n", "# Rating distribution\n", "rating_counts = df[rating_column].value_counts().sort_index()\n", "rating_percentages = (rating_counts / len(df)) * 100\n", "\n", "print(f\"\\n⭐ Rating Distribution:\")\n", "for rating, count in rating_counts.items():\n", "    percentage = rating_percentages[rating]\n", "    bar = '█' * int(percentage / 2)  # Visual bar\n", "    print(f\"  {rating} stars: {count:>4} ({percentage:>5.1f}%) {bar}\")\n", "\n", "# Rating patterns analysis\n", "print(f\"\\n🔍 Rating Patterns:\")\n", "low_ratings = (df[rating_column] <= 2).sum()\n", "mid_ratings = (df[rating_column] == 3).sum()\n", "high_ratings = (df[rating_column] >= 4).sum()\n", "\n", "print(f\"  • Low ratings (1-2): {low_ratings} ({low_ratings/len(df)*100:.1f}%)\")\n", "print(f\"  • Mid ratings (3): {mid_ratings} ({mid_ratings/len(df)*100:.1f}%)\")\n", "print(f\"  • High ratings (4-5): {high_ratings} ({high_ratings/len(df)*100:.1f}%)\")\n", "\n", "# Skewness analysis\n", "skewness = df[rating_column].skew()\n", "print(f\"\\n📐 Distribution Shape:\")\n", "print(f\"  • Skewness: {skewness:.3f}\")\n", "if skewness > 0.5:\n", "    print(f\"  • Distribution: Right-skewed (more low ratings)\")\n", "elif skewness < -0.5:\n", "    print(f\"  • Distribution: Left-skewed (more high ratings)\")\n", "else:\n", "    print(f\"  • Distribution: Approximately symmetric\")\n", "\n", "# Mode analysis\n", "mode_rating = df[rating_column].mode().iloc[0]\n", "mode_count = rating_counts[mode_rating]\n", "print(f\"  • Most common rating: {mode_rating} ({mode_count} reviews, {mode_count/len(df)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Sentiment Labeling Strategies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define multiple labeling strategies\n", "print(\"🎯 SENTIMENT LABELING STRATEGIES\")\n", "print(\"=\" * 50)\n", "\n", "def strategy_binary_strict(rating):\n", "    \"\"\"Binary classification: 1-2 = Negative, 4-5 = Positive, 3 = Exclude\"\"\"\n", "    if rating <= 2:\n", "        return 'Negative'\n", "    elif rating >= 4:\n", "        return 'Positive'\n", "    else:\n", "        return None  # Exclude neutral\n", "\n", "def strategy_binary_inclusive(rating):\n", "    \"\"\"Binary classification: 1-3 = Negative, 4-5 = Positive\"\"\"\n", "    if rating <= 3:\n", "        return 'Negative'\n", "    else:\n", "        return 'Positive'\n", "\n", "def strategy_ternary(rating):\n", "    \"\"\"Three-class: 1-2 = Negative, 3 = Neutral, 4-5 = Positive\"\"\"\n", "    if rating <= 2:\n", "        return 'Negative'\n", "    elif rating == 3:\n", "        return 'Neutral'\n", "    else:\n", "        return 'Positive'\n", "\n", "def strategy_fine_grained(rating):\n", "    \"\"\"Five-class: Each rating as separate class\"\"\"\n", "    rating_map = {\n", "        1: 'Very Negative',\n", "        2: 'Negative', \n", "        3: 'Neutral',\n", "        4: 'Positive',\n", "        5: 'Very Positive'\n", "    }\n", "    return rating_map.get(rating, 'Unknown')\n", "\n", "def strategy_balanced_binary(rating):\n", "    \"\"\"Balanced binary: 1-2 = Negative, 5 = Positive, exclude 3-4\"\"\"\n", "    if rating <= 2:\n", "        return 'Negative'\n", "    elif rating == 5:\n", "        return 'Positive'\n", "    else:\n", "        return None  # Exclude for balance\n", "\n", "# Apply all strategies\n", "strategies = {\n", "    'binary_strict': strategy_binary_strict,\n", "    'binary_inclusive': strategy_binary_inclusive,\n", "    'ternary': strategy_ternary,\n", "    'fine_grained': strategy_fine_grained,\n", "    'balanced_binary': strategy_balanced_binary\n", "}\n", "\n", "print(f\"\\n🔄 Applying {len(strategies)} labeling strategies...\")\n", "\n", "strategy_results = {}\n", "\n", "for strategy_name, strategy_func in strategies.items():\n", "    # Apply strategy\n", "    df[f'sentiment_{strategy_name}'] = df[rating_column].apply(strategy_func)\n", "    \n", "    # Calculate statistics\n", "    labels = df[f'sentiment_{strategy_name}'].dropna()\n", "    label_counts = labels.value_counts()\n", "    total_labeled = len(labels)\n", "    excluded = len(df) - total_labeled\n", "    \n", "    strategy_results[strategy_name] = {\n", "        'total_labeled': total_labeled,\n", "        'excluded': excluded,\n", "        'label_counts': label_counts.to_dict(),\n", "        'label_distribution': (label_counts / total_labeled * 100).to_dict()\n", "    }\n", "    \n", "    print(f\"\\n📊 {strategy_name.upper().replace('_', ' ')}:\")\n", "    print(f\"  • Labeled samples: {total_labeled} / {len(df)} ({total_labeled/len(df)*100:.1f}%)\")\n", "    print(f\"  • Excluded samples: {excluded} ({excluded/len(df)*100:.1f}%)\")\n", "    print(f\"  • Label distribution:\")\n", "    for label, count in label_counts.items():\n", "        percentage = count / total_labeled * 100\n", "        print(f\"    - {label}: {count} ({percentage:.1f}%)\")\n", "\n", "print(f\"\\n✅ All labeling strategies applied successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Strategy Comparison & Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive comparison visualization\n", "print(\"📊 STRATEGY COMPARISON & VISUALIZATION\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare data for visualization\n", "strategy_names = list(strategy_results.keys())\n", "labeled_counts = [strategy_results[s]['total_labeled'] for s in strategy_names]\n", "excluded_counts = [strategy_results[s]['excluded'] for s in strategy_names]\n", "\n", "# Create subplots\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Original Rating Distribution', 'Labeled vs Excluded Samples',\n", "                   'Strategy Comparison - Sample Usage', 'Binary Strict - Label Distribution'),\n", "    specs=[[{'type': 'bar'}, {'type': 'bar'}],\n", "           [{'type': 'bar'}, {'type': 'pie'}]]\n", ")\n", "\n", "# Original rating distribution\n", "fig.add_trace(\n", "    go.Bar(x=rating_counts.index, y=rating_counts.values, \n", "           name='Rating Count', marker_color='lightblue'),\n", "    row=1, col=1\n", ")\n", "\n", "# Labeled vs excluded\n", "fig.add_trace(\n", "    go.Bar(x=strategy_names, y=labeled_counts, \n", "           name='Labeled', marker_color='lightgreen'),\n", "    row=1, col=2\n", ")\n", "fig.add_trace(\n", "    go.Bar(x=strategy_names, y=excluded_counts, \n", "           name='Excluded', marker_color='lightcoral'),\n", "    row=1, col=2\n", ")\n", "\n", "# Sample usage percentage\n", "usage_percentages = [labeled / len(df) * 100 for labeled in labeled_counts]\n", "fig.add_trace(\n", "    go.Bar(x=strategy_names, y=usage_percentages, \n", "           name='Usage %', marker_color='lightyellow'),\n", "    row=2, col=1\n", ")\n", "\n", "# Binary strict distribution (most common for ML)\n", "if 'binary_strict' in strategy_results:\n", "    binary_labels = list(strategy_results['binary_strict']['label_counts'].keys())\n", "    binary_counts = list(strategy_results['binary_strict']['label_counts'].values())\n", "    \n", "    fig.add_trace(\n", "        go.Pie(labels=binary_labels, values=binary_counts, name='Binary Strict'),\n", "        row=2, col=2\n", "    )\n", "\n", "fig.update_layout(height=800, showlegend=True, \n", "                 title_text=\"📊 Score-Based Labeling Strategy Analysis\")\n", "fig.show()\n", "\n", "# Strategy comparison table\n", "print(f\"\\n📋 STRATEGY COMPARISON TABLE:\")\n", "comparison_data = []\n", "for strategy_name, results in strategy_results.items():\n", "    total_labeled = results['total_labeled']\n", "    usage_pct = total_labeled / len(df) * 100\n", "    num_classes = len(results['label_counts'])\n", "    \n", "    # Calculate class balance (standard deviation of percentages)\n", "    percentages = list(results['label_distribution'].values())\n", "    balance_score = np.std(percentages) if len(percentages) > 1 else 0\n", "    \n", "    comparison_data.append({\n", "        'Strategy': strategy_name.replace('_', ' ').title(),\n", "        'Samples Used': total_labeled,\n", "        'Usage %': f\"{usage_pct:.1f}%\",\n", "        'Classes': num_classes,\n", "        'Balance Score': f\"{balance_score:.1f}\",\n", "        'Dominant Class %': f\"{max(percentages):.1f}%\" if percentages else \"N/A\"\n", "    })\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "print(comparison_df.to_string(index=False))\n", "\n", "print(f\"\\n💡 Strategy Recommendations:\")\n", "print(f\"  • For balanced binary classification: 'balanced_binary' or 'binary_strict'\")\n", "print(f\"  • For maximum data usage: 'binary_inclusive' or 'ternary'\")\n", "print(f\"  • For fine-grained analysis: 'fine_grained'\")\n", "print(f\"  • For research/comparison: 'ternary' (includes neutral class)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⚖️ Class Balance Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detailed class balance analysis\n", "print(\"⚖️ CLASS BALANCE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "def analyze_class_balance(labels, strategy_name):\n", "    \"\"\"Analyze class balance for a given labeling strategy\"\"\"\n", "    if labels.empty:\n", "        return None\n", "    \n", "    label_counts = labels.value_counts()\n", "    total_samples = len(labels)\n", "    \n", "    # Calculate balance metrics\n", "    percentages = (label_counts / total_samples * 100).values\n", "    balance_ratio = label_counts.min() / label_counts.max()\n", "    imbalance_ratio = label_counts.max() / label_counts.min()\n", "    \n", "    # Gini coefficient for imbalance\n", "    sorted_counts = np.sort(label_counts.values)\n", "    n = len(sorted_counts)\n", "    gini = (2 * np.sum((np.arange(1, n+1) * sorted_counts))) / (n * np.sum(sorted_counts)) - (n+1)/n\n", "    \n", "    return {\n", "        'strategy': strategy_name,\n", "        'total_samples': total_samples,\n", "        'num_classes': len(label_counts),\n", "        'label_counts': label_counts.to_dict(),\n", "        'percentages': dict(zip(label_counts.index, percentages)),\n", "        'balance_ratio': balance_ratio,\n", "        'imbalance_ratio': imbalance_ratio,\n", "        'gini_coefficient': gini,\n", "        'is_balanced': balance_ratio >= 0.5  # Consider balanced if minority >= 50% of majority\n", "    }\n", "\n", "# Analyze balance for each strategy\n", "balance_analyses = {}\n", "for strategy_name in strategies.keys():\n", "    labels = df[f'sentiment_{strategy_name}'].dropna()\n", "    analysis = analyze_class_balance(labels, strategy_name)\n", "    if analysis:\n", "        balance_analyses[strategy_name] = analysis\n", "\n", "# Display balance analysis\n", "print(f\"\\n📊 Class Balance Summary:\")\n", "print(f\"{'Strategy':<18} {'Samples':<8} {'Classes':<8} {'Balance Ratio':<13} {'Imbalance':<10} {'Balanced?':<10}\")\n", "print(\"-\" * 75)\n", "\n", "for strategy_name, analysis in balance_analyses.items():\n", "    strategy_display = strategy_name.replace('_', ' ').title()[:17]\n", "    samples = analysis['total_samples']\n", "    classes = analysis['num_classes']\n", "    balance_ratio = analysis['balance_ratio']\n", "    imbalance_ratio = analysis['imbalance_ratio']\n", "    is_balanced = \"Yes\" if analysis['is_balanced'] else \"No\"\n", "    \n", "    print(f\"{strategy_display:<18} {samples:<8} {classes:<8} {balance_ratio:<13.3f} {imbalance_ratio:<10.1f} {is_balanced:<10}\")\n", "\n", "# Detailed analysis for binary strategies (most important for ML)\n", "binary_strategies = ['binary_strict', 'binary_inclusive', 'balanced_binary']\n", "print(f\"\\n🎯 DETAILED BINARY CLASSIFICATION ANALYSIS:\")\n", "\n", "for strategy_name in binary_strategies:\n", "    if strategy_name in balance_analyses:\n", "        analysis = balance_analyses[strategy_name]\n", "        print(f\"\\n📊 {strategy_name.replace('_', ' ').upper()}:\")\n", "        print(f\"  • Total samples: {analysis['total_samples']}\")\n", "        print(f\"  • Class distribution:\")\n", "        \n", "        for label, count in analysis['label_counts'].items():\n", "            percentage = analysis['percentages'][label]\n", "            print(f\"    - {label}: {count} ({percentage:.1f}%)\")\n", "        \n", "        print(f\"  • Balance ratio: {analysis['balance_ratio']:.3f}\")\n", "        print(f\"  • Imbalance ratio: {analysis['imbalance_ratio']:.1f}:1\")\n", "        print(f\"  • Gini coefficient: {analysis['gini_coefficient']:.3f}\")\n", "        print(f\"  • Balanced: {'Yes' if analysis['is_balanced'] else 'No'}\")\n", "        \n", "        # Recommendation\n", "        if analysis['balance_ratio'] >= 0.8:\n", "            print(f\"  • ✅ Excellent balance for ML training\")\n", "        elif analysis['balance_ratio'] >= 0.5:\n", "            print(f\"  • ✅ Good balance for ML training\")\n", "        elif analysis['balance_ratio'] >= 0.3:\n", "            print(f\"  • ⚠️ Moderate imbalance - consider class weights\")\n", "        else:\n", "            print(f\"  • ❌ High imbalance - requires balancing techniques\")\n", "\n", "# Calculate class weights for imbalanced datasets\n", "print(f\"\\n⚖️ CLASS WEIGHTS FOR IMBALANCED DATASETS:\")\n", "for strategy_name in binary_strategies:\n", "    if strategy_name in balance_analyses:\n", "        labels = df[f'sentiment_{strategy_name}'].dropna()\n", "        if len(labels) > 0 and len(labels.unique()) > 1:\n", "            # Calculate class weights\n", "            le = LabelEncoder()\n", "            y_encoded = le.fit_transform(labels)\n", "            class_weights = compute_class_weight('balanced', classes=np.unique(y_encoded), y=y_encoded)\n", "            \n", "            print(f\"\\n🔧 {strategy_name.replace('_', ' ').upper()} - Recommended class weights:\")\n", "            for i, class_name in enumerate(le.classes_):\n", "                print(f\"  • {class_name}: {class_weights[i]:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Recommended Strategy Selection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Select and prepare the recommended strategy\n", "print(\"🎯 RECOMMENDED STRATEGY SELECTION\")\n", "print(\"=\" * 50)\n", "\n", "# Strategy scoring system\n", "def score_strategy(analysis, total_dataset_size):\n", "    \"\"\"Score a strategy based on multiple criteria\"\"\"\n", "    if not analysis:\n", "        return 0\n", "    \n", "    score = 0\n", "    \n", "    # Data usage score (0-30 points)\n", "    usage_ratio = analysis['total_samples'] / total_dataset_size\n", "    score += min(30, usage_ratio * 30)\n", "    \n", "    # Balance score (0-40 points)\n", "    balance_ratio = analysis['balance_ratio']\n", "    if balance_ratio >= 0.8:\n", "        score += 40\n", "    elif balance_ratio >= 0.5:\n", "        score += 30\n", "    elif balance_ratio >= 0.3:\n", "        score += 20\n", "    else:\n", "        score += 10\n", "    \n", "    # Simplicity score (0-20 points) - prefer binary classification\n", "    if analysis['num_classes'] == 2:\n", "        score += 20\n", "    elif analysis['num_classes'] == 3:\n", "        score += 15\n", "    else:\n", "        score += 10\n", "    \n", "    # Sample size score (0-10 points)\n", "    if analysis['total_samples'] >= 100:\n", "        score += 10\n", "    elif analysis['total_samples'] >= 50:\n", "        score += 7\n", "    elif analysis['total_samples'] >= 20:\n", "        score += 5\n", "    else:\n", "        score += 2\n", "    \n", "    return score\n", "\n", "# Score all strategies\n", "strategy_scores = {}\n", "for strategy_name, analysis in balance_analyses.items():\n", "    score = score_strategy(analysis, len(df))\n", "    strategy_scores[strategy_name] = score\n", "\n", "# Rank strategies\n", "ranked_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)\n", "\n", "print(f\"\\n🏆 STRATEGY RANKING:\")\n", "print(f\"{'Rank':<5} {'Strategy':<18} {'Score':<6} {'Samples':<8} {'Balance':<8} {'Classes':<8}\")\n", "print(\"-\" * 60)\n", "\n", "for rank, (strategy_name, score) in enumerate(ranked_strategies, 1):\n", "    analysis = balance_analyses[strategy_name]\n", "    strategy_display = strategy_name.replace('_', ' ').title()[:17]\n", "    samples = analysis['total_samples']\n", "    balance = f\"{analysis['balance_ratio']:.2f}\"\n", "    classes = analysis['num_classes']\n", "    \n", "    print(f\"{rank:<5} {strategy_display:<18} {score:<6.0f} {samples:<8} {balance:<8} {classes:<8}\")\n", "\n", "# Select recommended strategy\n", "recommended_strategy = ranked_strategies[0][0]\n", "recommended_analysis = balance_analyses[recommended_strategy]\n", "\n", "print(f\"\\n🎯 RECOMMENDED STRATEGY: {recommended_strategy.replace('_', ' ').upper()}\")\n", "print(f\"  • Score: {strategy_scores[recommended_strategy]:.0f}/100\")\n", "print(f\"  • Samples: {recommended_analysis['total_samples']} / {len(df)}\")\n", "print(f\"  • Usage: {recommended_analysis['total_samples']/len(df)*100:.1f}%\")\n", "print(f\"  • Classes: {recommended_analysis['num_classes']}\")\n", "print(f\"  • Balance ratio: {recommended_analysis['balance_ratio']:.3f}\")\n", "print(f\"  • Class distribution:\")\n", "for label, percentage in recommended_analysis['percentages'].items():\n", "    print(f\"    - {label}: {percentage:.1f}%\")\n", "\n", "# Prepare final labeled dataset\n", "print(f\"\\n📊 PREPARING FINAL LABELED DATASET...\")\n", "\n", "# Create final dataset with recommended strategy\n", "df_labeled = df.copy()\n", "df_labeled['sentiment_label'] = df_labeled[f'sentiment_{recommended_strategy}']\n", "df_labeled['labeling_strategy'] = recommended_strategy\n", "df_labeled['original_rating'] = df_labeled[rating_column]\n", "\n", "# Remove rows with no sentiment label (excluded by strategy)\n", "df_final = df_labeled.dropna(subset=['sentiment_label']).copy()\n", "\n", "# Add numeric labels for ML\n", "le = LabelEncoder()\n", "df_final['sentiment_numeric'] = le.fit_transform(df_final['sentiment_label'])\n", "\n", "# Create label mapping\n", "label_mapping = dict(zip(le.classes_, le.transform(le.classes_)))\n", "\n", "print(f\"✅ Final labeled dataset prepared:\")\n", "print(f\"  • Total samples: {len(df_final)}\")\n", "print(f\"  • Excluded samples: {len(df) - len(df_final)}\")\n", "print(f\"  • Label mapping: {label_mapping}\")\n", "print(f\"  • Columns added: sentiment_label, sentiment_numeric, labeling_strategy, original_rating\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save Score-Based Labeling Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all results\n", "print(\"💾 SAVING SCORE-BASED LABELING RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Create directories\n", "import os\n", "os.makedirs('data/labeled', exist_ok=True)\n", "os.makedirs('data/analysis', exist_ok=True)\n", "\n", "# Save final labeled dataset\n", "output_file = 'data/labeled/gofood_score_based_labeled.xlsx'\n", "df_final.to_excel(output_file, index=False, engine='openpyxl')\n", "print(f\"✅ Final labeled dataset: {output_file}\")\n", "print(f\"   • Samples: {len(df_final)}\")\n", "print(f\"   • Strategy: {recommended_strategy}\")\n", "print(f\"   • Classes: {list(label_mapping.keys())}\")\n", "\n", "# Save all strategy results\n", "all_strategies_file = 'data/labeled/gofood_all_strategies.xlsx'\n", "df_labeled.to_excel(all_strategies_file, index=False, engine='openpyxl')\n", "print(f\"\\n✅ All strategies dataset: {all_strategies_file}\")\n", "print(f\"   • Contains all {len(strategies)} labeling strategies\")\n", "\n", "# Save strategy analysis\n", "strategy_analysis_data = []\n", "for strategy_name, analysis in balance_analyses.items():\n", "    row = {\n", "        'strategy': strategy_name,\n", "        'total_samples': analysis['total_samples'],\n", "        'num_classes': analysis['num_classes'],\n", "        'balance_ratio': analysis['balance_ratio'],\n", "        'imbalance_ratio': analysis['imbalance_ratio'],\n", "        'gini_coefficient': analysis['gini_coefficient'],\n", "        'is_balanced': analysis['is_balanced'],\n", "        'score': strategy_scores.get(strategy_name, 0),\n", "        'usage_percentage': analysis['total_samples'] / len(df) * 100\n", "    }\n", "    \n", "    # Add class distribution\n", "    for label, count in analysis['label_counts'].items():\n", "        row[f'{label.lower()}_count'] = count\n", "        row[f'{label.lower()}_percentage'] = analysis['percentages'][label]\n", "    \n", "    strategy_analysis_data.append(row)\n", "\n", "strategy_df = pd.DataFrame(strategy_analysis_data)\n", "strategy_analysis_file = 'data/analysis/score_based_strategy_analysis.xlsx'\n", "strategy_df.to_excel(strategy_analysis_file, index=False, engine='openpyxl')\n", "print(f\"\\n✅ Strategy analysis: {strategy_analysis_file}\")\n", "\n", "# Save label encoder and mappings\n", "label_artifacts = {\n", "    'label_encoder': le,\n", "    'label_mapping': label_mapping,\n", "    'recommended_strategy': recommended_strategy,\n", "    'strategy_results': strategy_results,\n", "    'balance_analyses': balance_analyses,\n", "    'creation_date': datetime.now().isoformat(),\n", "    'dataset_info': {\n", "        'total_original_samples': len(df),\n", "        'total_labeled_samples': len(df_final),\n", "        'excluded_samples': len(df) - len(df_final),\n", "        'rating_column': rating_column\n", "    }\n", "}\n", "\n", "artifacts_file = 'data/labeled/score_based_artifacts.pkl'\n", "joblib.dump(label_artifacts, artifacts_file)\n", "print(f\"\\n✅ Label artifacts: {artifacts_file}\")\n", "\n", "# Save summary report\n", "summary_report = {\n", "    'labeling_method': 'score_based',\n", "    'creation_date': datetime.now().isoformat(),\n", "    'dataset_summary': {\n", "        'original_samples': len(df),\n", "        'labeled_samples': len(df_final),\n", "        'exclusion_rate': (len(df) - len(df_final)) / len(df) * 100,\n", "        'rating_column': rating_column\n", "    },\n", "    'recommended_strategy': {\n", "        'name': recommended_strategy,\n", "        'score': strategy_scores[recommended_strategy],\n", "        'samples': recommended_analysis['total_samples'],\n", "        'classes': recommended_analysis['num_classes'],\n", "        'balance_ratio': recommended_analysis['balance_ratio'],\n", "        'class_distribution': recommended_analysis['percentages']\n", "    },\n", "    'all_strategies': {\n", "        name: {\n", "            'samples': results['total_labeled'],\n", "            'excluded': results['excluded'],\n", "            'classes': len(results['label_counts']),\n", "            'distribution': results['label_distribution']\n", "        } for name, results in strategy_results.items()\n", "    },\n", "    'files_generated': {\n", "        'labeled_dataset': output_file,\n", "        'all_strategies': all_strategies_file,\n", "        'strategy_analysis': strategy_analysis_file,\n", "        'artifacts': artifacts_file\n", "    }\n", "}\n", "\n", "summary_file = 'data/analysis/score_based_summary.json'\n", "with open(summary_file, 'w', encoding='utf-8') as f:\n", "    json.dump(summary_report, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n✅ Summary report: {summary_file}\")\n", "\n", "# Create utility functions for loading score-based labels\n", "score_utils_code = '''\n", "import pandas as pd\n", "import joblib\n", "import json\n", "\n", "def load_score_based_labels(file_path='data/labeled/gofood_score_based_labeled.xlsx'):\n", "    \"\"\"Load the score-based labeled dataset\"\"\"\n", "    return pd.read_excel(file_path)\n", "\n", "def load_label_artifacts(file_path='data/labeled/score_based_artifacts.pkl'):\n", "    \"\"\"Load label encoder and related artifacts\"\"\"\n", "    return joblib.load(file_path)\n", "\n", "def get_label_mapping():\n", "    \"\"\"Get the label mapping for score-based labels\"\"\"\n", "    artifacts = load_label_artifacts()\n", "    return artifacts['label_mapping']\n", "\n", "def apply_score_based_strategy(ratings, strategy='binary_strict'):\n", "    \"\"\"Apply a score-based labeling strategy to new ratings\"\"\"\n", "    strategies = {\n", "        'binary_strict': lambda r: 'Negative' if r <= 2 else ('Positive' if r >= 4 else None),\n", "        'binary_inclusive': lambda r: 'Negative' if r <= 3 else 'Positive',\n", "        'ternary': lambda r: 'Negative' if r <= 2 else ('Neutral' if r == 3 else 'Positive'),\n", "        'fine_grained': lambda r: {1: 'Very Negative', 2: 'Negative', 3: 'Neutral', 4: 'Positive', 5: 'Very Positive'}.get(r, 'Unknown'),\n", "        'balanced_binary': lambda r: 'Negative' if r <= 2 else ('Positive' if r == 5 else None)\n", "    }\n", "    \n", "    if strategy not in strategies:\n", "        raise ValueError(f\"Unknown strategy: {strategy}\")\n", "    \n", "    if isinstance(ratings, (int, float)):\n", "        return strategies[strategy](ratings)\n", "    else:\n", "        return [strategies[strategy](r) for r in ratings]\n", "\n", "def get_strategy_info():\n", "    \"\"\"Get information about all available strategies\"\"\"\n", "    with open('data/analysis/score_based_summary.json', 'r') as f:\n", "        summary = json.load(f)\n", "    return summary['all_strategies']\n", "'''\n", "\n", "with open('utils/score_based_labeling.py', 'w', encoding='utf-8') as f:\n", "    f.write(score_utils_code)\n", "\n", "print(f\"\\n🔧 Utility functions: utils/score_based_labeling.py\")\n", "\n", "print(f\"\\n📊 FILES GENERATED SUMMARY:\")\n", "print(f\"  📁 data/labeled/\")\n", "print(f\"     • gofood_score_based_labeled.xlsx - Final labeled dataset\")\n", "print(f\"     • gofood_all_strategies.xlsx - All strategies comparison\")\n", "print(f\"     • score_based_artifacts.pkl - Label encoder & mappings\")\n", "print(f\"  📁 data/analysis/\")\n", "print(f\"     • score_based_strategy_analysis.xlsx - Strategy comparison\")\n", "print(f\"     • score_based_summary.json - Complete summary\")\n", "print(f\"  🔧 utils/score_based_labeling.py - Utility functions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 SCORE-BASED LABELING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n🏷️ LABELING COMPLETED:\")\n", "print(f\"  • Original dataset: {len(df)} reviews\")\n", "print(f\"  • Rating column: {rating_column}\")\n", "print(f\"  • Rating range: {df[rating_column].min()}-{df[rating_column].max()}\")\n", "\n", "print(f\"\\n🎯 STRATEGIES EVALUATED: {len(strategies)}\")\n", "for i, (strategy_name, score) in enumerate(ranked_strategies, 1):\n", "    analysis = balance_analyses[strategy_name]\n", "    print(f\"  {i}. {strategy_name.replace('_', ' ').title():<18} - Score: {score:>3.0f}, Samples: {analysis['total_samples']:>3}\")\n", "\n", "print(f\"\\n🏆 RECOMMENDED STRATEGY: {recommended_strategy.replace('_', ' ').upper()}\")\n", "print(f\"  • Final labeled samples: {len(df_final)}\")\n", "print(f\"  • Data usage: {len(df_final)/len(df)*100:.1f}%\")\n", "print(f\"  • Classes: {recommended_analysis['num_classes']}\")\n", "print(f\"  • Balance ratio: {recommended_analysis['balance_ratio']:.3f}\")\n", "print(f\"  • Class distribution:\")\n", "for label, percentage in recommended_analysis['percentages'].items():\n", "    count = recommended_analysis['label_counts'][label]\n", "    print(f\"    - {label}: {count} ({percentage:.1f}%)\")\n", "\n", "print(f\"\\n📊 QUALITY METRICS:\")\n", "print(f\"  • Strategy score: {strategy_scores[recommended_strategy]:.0f}/100\")\n", "print(f\"  • Balance quality: {'Excellent' if recommended_analysis['balance_ratio'] >= 0.8 else 'Good' if recommended_analysis['balance_ratio'] >= 0.5 else 'Moderate'}\")\n", "print(f\"  • Sample efficiency: {len(df_final)/len(df)*100:.1f}%\")\n", "print(f\"  • ML readiness: {'Ready' if recommended_analysis['balance_ratio'] >= 0.3 else 'Needs balancing'}\")\n", "\n", "print(f\"\\n💾 OUTPUT FILES:\")\n", "print(f\"  • Main dataset: data/labeled/gofood_score_based_labeled.xlsx\")\n", "print(f\"  • All strategies: data/labeled/gofood_all_strategies.xlsx\")\n", "print(f\"  • Analysis: data/analysis/score_based_strategy_analysis.xlsx\")\n", "print(f\"  • Summary: data/analysis/score_based_summary.json\")\n", "print(f\"  • Artifacts: data/labeled/score_based_artifacts.pkl\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. Lexicon-based sentiment labeling (06_lexicon_based_labeling.ipynb)\")\n", "print(f\"  2. Compare score-based vs lexicon-based labels\")\n", "print(f\"  3. Random Forest training (07_random_forest_models.ipynb)\")\n", "print(f\"  4. XGBoost training (08_xgboost_models.ipynb)\")\n", "\n", "print(f\"\\n✅ SCORE-BASED LABELING COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for lexicon-based labeling phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}