# 📋 Action Plan - Ana<PERSON><PERSON>timen Komprehensif GoFood Indonesia

## 🎯 Tujuan Proyek
Mengembangkan sistem analisis sentimen komprehensif untuk ulasan GoFood dalam bahasa Indonesia dengan:
- Penanganan slang dan normalisasi bahasa Indonesia yang optimal
- Perbandingan multiple algoritma (Random Forest vs XGBoost)
- Multiple strategi pelabelan (Score-based vs Lexicon-based)
- Multiple split ratio untuk training-testing
- <PERSON><PERSON>is statistik, EDA, dan topic modeling yang mendalam

## 📊 Dataset
- **File**: `gofood_ulasan_preprocessed.xlsx`
- **Bahasa**: Indonesia
- **Domain**: Ulasan aplikasi GoFood
- **Kolom utama**: `ulasan_stemmed`, `nilai` (rating)

## 🔄 Pipeline Pengembangan

### Phase 1: Setup & Data Understanding
- [x] **01_data_exploration.ipynb** - EDA dan analisis statistik
- [x] **02_lexicon_setup.ipynb** - Setup lexicon bahasa Indonesia

### Phase 2: Text Preprocessing
- [x] **03_text_preprocessing.ipynb** - Preprocessing komprehensif dengan normalisasi slang
- [x] **04_feature_extraction.ipynb** - TF-IDF, N-gram, dan feature engineering

### Phase 3: Labeling Strategies
- [x] **05_score_based_labeling.ipynb** - Pelabelan berdasarkan rating
- [x] **06_lexicon_based_labeling.ipynb** - Pelabelan berdasarkan lexicon

### Phase 4: Model Training & Evaluation
- [x] **07_random_forest_models.ipynb** - Random Forest dengan berbagai split ratio
- [x] **08_xgboost_models.ipynb** - XGBoost dengan berbagai split ratio
- [x] **09_model_comparison.ipynb** - Perbandingan komprehensif semua model

### Phase 5: Advanced Analysis
- [x] **10_topic_modeling.ipynb** - Topic modeling dan analisis tema
- [x] **11_visualization_dashboard.ipynb** - Dashboard visualisasi komprehensif
- [x] **12_final_evaluation.ipynb** - Evaluasi final dan rekomendasi

## 🛠️ Teknologi & Library

### Core Libraries
```python
# Data Processing
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Text Processing
import re
import nltk
from Sastrawi.Stemmer.StemmerFactory import StemmerFactory
from Sastrawi.StopWordRemover.StopWordRemoverFactory import StopWordRemoverFactory

# Machine Learning
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import xgboost as xgb

# Topic Modeling
from sklearn.decomposition import LatentDirichletAllocation
import pyLDAvis
import pyLDAvis.sklearn

# Visualization
import plotly.express as px
import plotly.graph_objects as go
import wordcloud
```

### Lexicon Resources
1. **SentiWordNet ID** - Lexicon sentimen bahasa Indonesia
2. **InSet Lexicon** - Indonesian Sentiment Lexicon
3. **NusaX Lexicon** - Multilingual sentiment lexicon
4. **Custom Normalization Dictionary** - Slang dan kata tidak baku

## 📈 Split Ratio Testing
Akan diuji dengan berbagai rasio:
- 70:30 (Training:Testing)
- 75:25 (Training:Testing)
- 65:35 (Training:Testing)
- 60:40 (Training:Testing)
- Dan kebalikannya untuk cross-validation

## 🔬 Strategi Pelabelan

### 1. Score-Based Labeling
```python
def score_based_labeling(rating):
    if rating <= 2:
        return 'Negatif'
    elif rating >= 4:
        return 'Positif'
    else:
        return 'Netral'
```

### 2. Lexicon-Based Labeling
```python
def lexicon_based_labeling(text, lexicon_dict):
    # Implementasi berdasarkan skor lexicon
    positive_score = sum([lexicon_dict.get(word, 0) for word in text.split() if lexicon_dict.get(word, 0) > 0])
    negative_score = sum([abs(lexicon_dict.get(word, 0)) for word in text.split() if lexicon_dict.get(word, 0) < 0])
    
    if positive_score > negative_score:
        return 'Positif'
    elif negative_score > positive_score:
        return 'Negatif'
    else:
        return 'Netral'
```

## 🎯 Kombinasi Model-Labeling
1. **Random Forest + Score-based**
2. **Random Forest + Lexicon-based**
3. **XGBoost + Score-based**
4. **XGBoost + Lexicon-based**

## 📊 Metrik Evaluasi
- **Accuracy**
- **Precision, Recall, F1-Score**
- **Confusion Matrix**
- **ROC-AUC**
- **Cross-validation scores**
- **Feature importance analysis**

## 🎨 Visualisasi yang Akan Dibuat
1. **Data Distribution** - Distribusi rating dan sentimen
2. **Word Clouds** - Kata-kata dominan per sentimen
3. **N-gram Analysis** - Bigram dan trigram analysis
4. **Topic Modeling Visualization** - LDA topic distribution
5. **Model Performance Comparison** - Heatmap perbandingan akurasi
6. **Feature Importance** - Top features untuk setiap model
7. **Confusion Matrix Heatmaps**
8. **ROC Curves Comparison**

## 📝 Deliverables
1. **12 Jupyter Notebooks** - Pipeline lengkap
2. **Development Log** - Changelog setiap tahap
3. **Final Report** - Laporan komprehensif
4. **Model Files** - Trained models (.pkl)
5. **Visualization Dashboard** - Interactive dashboard

## ⏱️ Timeline Estimasi
- **Phase 1**: 2-3 jam (Setup & EDA)
- **Phase 2**: 3-4 jam (Text Preprocessing)
- **Phase 3**: 2-3 jam (Labeling Strategies)
- **Phase 4**: 4-5 jam (Model Training)
- **Phase 5**: 3-4 jam (Advanced Analysis)
- **Total**: 14-19 jam

## 🔄 Quality Assurance
- Validasi setiap tahap preprocessing
- Cross-validation untuk semua model
- Statistical significance testing
- Error analysis dan debugging
- Documentation yang komprehensif

---
**Status**: 🚀 Ready to Execute
**Last Updated**: 2025-01-23
**Version**: 1.0
