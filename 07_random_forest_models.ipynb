{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🌲 07. Random Forest Model Training & Optimization\n", "\n", "**Tujuan**: Training dan optimasi Random Forest models untuk sentiment analysis\n", "\n", "**Key Features**:\n", "1. 🔄 **Data Integration** - Combine features dengan labels\n", "2. 🌲 **Random Forest Training** - Multiple configurations\n", "3. 🎯 **Hyperparameter Tuning** - Grid search & random search\n", "4. 📊 **Model Evaluation** - Comprehensive metrics\n", "5. 🔍 **Feature Importance** - <PERSON><PERSON><PERSON> penting<PERSON> fitur\n", "6. 💾 **Model Persistence** - Simpan model terb<PERSON><PERSON>\n", "\n", "**Input**: Feature matrices + labeled datasets  \n", "**Output**: Trained Random Forest models dengan evaluasi\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "import joblib\n", "import os\n", "\n", "# Machine learning libraries\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import (\n", "    train_test_split, GridSearchCV, RandomizedSearchCV, \n", "    cross_val_score, StratifiedKFold\n", ")\n", "from sklearn.metrics import (\n", "    classification_report, confusion_matrix, accuracy_score,\n", "    precision_recall_fscore_support, roc_auc_score, roc_curve,\n", "    precision_recall_curve, matthews_corrcoef, cohen_kappa_score\n", ")\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.utils.class_weight import compute_class_weight\n", "import scipy.stats as stats\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "np.random.seed(42)\n", "\n", "print(\"🌲 Random Forest training libraries imported successfully!\")\n", "print(f\"🕐 Model training started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Load Data & Features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load feature matrices\n", "print(\"📂 LOADING DATA & FEATURES\")\n", "print(\"=\" * 50)\n", "\n", "# Load TF-IDF features (optimized configuration)\n", "try:\n", "    X_features = joblib.load('data/features/X_optimized.pkl')\n", "    vectorizer = joblib.load('models/vectorizers/tfidf_optimized.pkl')\n", "    print(f\"✅ Optimized TF-IDF features loaded: {X_features.shape}\")\n", "    feature_config = 'optimized'\n", "except FileNotFoundError:\n", "    print(\"⚠️ Optimized features not found. Trying alternatives...\")\n", "    try:\n", "        X_features = joblib.load('data/features/X_unigram.pkl')\n", "        vectorizer = joblib.load('models/vectorizers/tfidf_unigram.pkl')\n", "        print(f\"✅ Unigram TF-IDF features loaded: {X_features.shape}\")\n", "        feature_config = 'unigram'\n", "    except FileNotFoundError:\n", "        print(\"❌ No feature matrices found. Please run feature extraction first.\")\n", "        raise FileNotFoundError(\"Feature matrices required for model training\")\n", "\n", "# Load labeled datasets\n", "score_based_data = None\n", "lexicon_based_data = None\n", "\n", "# Try to load score-based labels\n", "try:\n", "    score_based_data = pd.read_excel('data/labeled/gofood_score_based_labeled.xlsx')\n", "    print(f\"✅ Score-based labels loaded: {len(score_based_data)} samples\")\n", "except FileNotFoundError:\n", "    print(\"⚠️ Score-based labels not found\")\n", "\n", "# Try to load lexicon-based labels\n", "try:\n", "    lexicon_based_data = pd.read_excel('data/labeled/gofood_lexicon_based_labeled.xlsx')\n", "    print(f\"✅ Lexicon-based labels loaded: {len(lexicon_based_data)} samples\")\n", "except FileNotFoundError:\n", "    print(\"⚠️ Lexicon-based labels not found\")\n", "\n", "# Determine which labeling method to use\n", "if score_based_data is not None and len(score_based_data) == X_features.shape[0]:\n", "    primary_data = score_based_data\n", "    label_column = 'sentiment_label'\n", "    numeric_column = 'sentiment_numeric'\n", "    labeling_method = 'score_based'\n", "    print(f\"🎯 Using score-based labels as primary\")\n", "elif lexicon_based_data is not None and len(lexicon_based_data) == X_features.shape[0]:\n", "    primary_data = lexicon_based_data\n", "    label_column = 'lexicon_sentiment_label'\n", "    numeric_column = 'lexicon_sentiment_numeric'\n", "    labeling_method = 'lexicon_based'\n", "    print(f\"🎯 Using lexicon-based labels as primary\")\n", "else:\n", "    print(\"❌ No compatible labeled data found\")\n", "    # Create sample data for demonstration\n", "    print(\"📝 Creating sample labeled data...\")\n", "    n_samples = X_features.shape[0]\n", "    sample_labels = np.random.choice(['Positive', 'Negative', 'Neutral'], n_samples)\n", "    le = LabelEncoder()\n", "    sample_numeric = le.fit_transform(sample_labels)\n", "    \n", "    primary_data = pd.DataFrame({\n", "        'sentiment_label': sample_labels,\n", "        'sentiment_numeric': sample_numeric\n", "    })\n", "    label_column = 'sentiment_label'\n", "    numeric_column = 'sentiment_numeric'\n", "    labeling_method = 'sample'\n", "    print(f\"📝 Sample data created: {len(primary_data)} samples\")\n", "\n", "# Extract labels\n", "y_labels = primary_data[label_column].values\n", "y_numeric = primary_data[numeric_column].values\n", "\n", "print(f\"\\n📊 Dataset Summary:\")\n", "print(f\"  • Features shape: {X_features.shape}\")\n", "print(f\"  • Labels: {len(y_labels)}\")\n", "print(f\"  • Labeling method: {labeling_method}\")\n", "print(f\"  • Feature config: {feature_config}\")\n", "print(f\"  • Classes: {np.unique(y_labels)}\")\n", "print(f\"  • Class distribution: {dict(zip(*np.unique(y_labels, return_counts=True)))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔄 Data Preparation & Splitting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare data for training\n", "print(\"🔄 DATA PREPARATION & SPLITTING\")\n", "print(\"=\" * 50)\n", "\n", "# Convert sparse matrix to dense if needed (for small datasets)\n", "if X_features.shape[0] < 10000 and hasattr(X_features, 'toarray'):\n", "    print(f\"Converting sparse matrix to dense for small dataset...\")\n", "    X_dense = X_features.toarray()\n", "    use_sparse = False\n", "else:\n", "    X_dense = X_features\n", "    use_sparse = True\n", "\n", "print(f\"📊 Feature Matrix Info:\")\n", "print(f\"  • Shape: {X_dense.shape}\")\n", "print(f\"  • Type: {'Sparse' if use_sparse else 'Dense'}\")\n", "print(f\"  • Memory usage: {X_dense.nbytes / 1024**2:.1f} MB\" if not use_sparse else f\"  • Sparse matrix\")\n", "\n", "# Analyze class distribution\n", "unique_labels, label_counts = np.unique(y_labels, return_counts=True)\n", "class_distribution = dict(zip(unique_labels, label_counts))\n", "total_samples = len(y_labels)\n", "\n", "print(f\"\\n📈 Class Distribution Analysis:\")\n", "for label, count in class_distribution.items():\n", "    percentage = count / total_samples * 100\n", "    print(f\"  • {label}: {count} ({percentage:.1f}%)\")\n", "\n", "# Calculate class imbalance\n", "max_count = max(label_counts)\n", "min_count = min(label_counts)\n", "imbalance_ratio = max_count / min_count\n", "print(f\"  • Imbalance ratio: {imbalance_ratio:.2f}:1\")\n", "\n", "# Determine if class weights are needed\n", "use_class_weights = imbalance_ratio > 2.0\n", "print(f\"  • Class weights needed: {'Yes' if use_class_weights else 'No'}\")\n", "\n", "# Calculate class weights if needed\n", "if use_class_weights:\n", "    class_weights = compute_class_weight('balanced', classes=unique_labels, y=y_labels)\n", "    class_weight_dict = dict(zip(unique_labels, class_weights))\n", "    print(f\"  • Class weights: {class_weight_dict}\")\n", "else:\n", "    class_weight_dict = None\n", "\n", "# Split data into train/validation/test sets\n", "print(f\"\\n🔀 Data Splitting:\")\n", "\n", "# First split: train+val vs test (80% vs 20%)\n", "X_temp, X_test, y_temp, y_test = train_test_split(\n", "    X_dense, y_numeric, \n", "    test_size=0.2, \n", "    random_state=42, \n", "    stratify=y_numeric\n", ")\n", "\n", "# Second split: train vs val (75% vs 25% of temp = 60% vs 20% of total)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_temp, y_temp, \n", "    test_size=0.25, \n", "    random_state=42, \n", "    stratify=y_temp\n", ")\n", "\n", "print(f\"  • Training set: {X_train.shape[0]} samples ({X_train.shape[0]/total_samples*100:.1f}%)\")\n", "print(f\"  • Validation set: {X_val.shape[0]} samples ({X_val.shape[0]/total_samples*100:.1f}%)\")\n", "print(f\"  • Test set: {X_test.shape[0]} samples ({X_test.shape[0]/total_samples*100:.1f}%)\")\n", "\n", "# Verify stratification\n", "print(f\"\\n✅ Stratification Verification:\")\n", "for split_name, y_split in [('Train', y_train), ('Val', y_val), ('Test', y_test)]:\n", "    split_dist = dict(zip(*np.unique(y_split, return_counts=True)))\n", "    split_pct = {k: v/len(y_split)*100 for k, v in split_dist.items()}\n", "    print(f\"  • {split_name}: {split_pct}\")\n", "\n", "# Store split information\n", "split_info = {\n", "    'total_samples': total_samples,\n", "    'train_size': len(X_train),\n", "    'val_size': len(X_val),\n", "    'test_size': len(X_test),\n", "    'feature_shape': X_dense.shape,\n", "    'class_distribution': class_distribution,\n", "    'imbalance_ratio': imbalance_ratio,\n", "    'use_class_weights': use_class_weights,\n", "    'class_weights': class_weight_dict\n", "}\n", "\n", "print(f\"\\n✅ Data preparation completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🌲 Baseline Random Forest Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train baseline Random Forest model\n", "print(\"🌲 BASELINE RANDOM FOREST MODEL\")\n", "print(\"=\" * 50)\n", "\n", "# Define baseline parameters\n", "baseline_params = {\n", "    'n_estimators': 100,\n", "    'max_depth': None,\n", "    'min_samples_split': 2,\n", "    'min_samples_leaf': 1,\n", "    'max_features': 'sqrt',\n", "    'bootstrap': True,\n", "    'random_state': 42,\n", "    'n_jobs': -1,\n", "    'class_weight': 'balanced' if use_class_weights else None\n", "}\n", "\n", "print(f\"\\n🔧 Baseline Parameters:\")\n", "for param, value in baseline_params.items():\n", "    print(f\"  • {param}: {value}\")\n", "\n", "# Train baseline model\n", "print(f\"\\n🔄 Training baseline model...\")\n", "start_time = datetime.now()\n", "\n", "rf_baseline = RandomForestClassifier(**baseline_params)\n", "rf_baseline.fit(X_train, y_train)\n", "\n", "training_time = (datetime.now() - start_time).total_seconds()\n", "print(f\"✅ Training completed in {training_time:.2f} seconds\")\n", "\n", "# Make predictions\n", "print(f\"\\n📊 Making predictions...\")\n", "y_train_pred = rf_baseline.predict(X_train)\n", "y_val_pred = rf_baseline.predict(X_val)\n", "y_test_pred = rf_baseline.predict(X_test)\n", "\n", "# Calculate probabilities for ROC analysis\n", "if len(unique_labels) == 2:  # Binary classification\n", "    y_train_proba = rf_baseline.predict_proba(X_train)[:, 1]\n", "    y_val_proba = rf_baseline.predict_proba(X_val)[:, 1]\n", "    y_test_proba = rf_baseline.predict_proba(X_test)[:, 1]\n", "else:  # Multi-class\n", "    y_train_proba = rf_baseline.predict_proba(X_train)\n", "    y_val_proba = rf_baseline.predict_proba(X_val)\n", "    y_test_proba = rf_baseline.predict_proba(X_test)\n", "\n", "# Evaluate baseline model\n", "def evaluate_model(y_true, y_pred, y_proba=None, set_name=\"\"):\n", "    \"\"\"Comprehensive model evaluation\"\"\"\n", "    results = {}\n", "    \n", "    # Basic metrics\n", "    results['accuracy'] = accuracy_score(y_true, y_pred)\n", "    results['precision'], results['recall'], results['f1'], _ = precision_recall_fscore_support(\n", "        y_true, y_pred, average='weighted'\n", "    )\n", "    \n", "    # Additional metrics\n", "    results['kappa'] = cohen_kappa_score(y_true, y_pred)\n", "    results['mcc'] = matthews_corrcoef(y_true, y_pred)\n", "    \n", "    # ROC AUC for binary classification\n", "    if len(np.unique(y_true)) == 2 and y_proba is not None:\n", "        results['roc_auc'] = roc_auc_score(y_true, y_proba)\n", "    \n", "    return results\n", "\n", "# Evaluate on all sets\n", "train_results = evaluate_model(y_train, y_train_pred, y_train_proba, \"Train\")\n", "val_results = evaluate_model(y_val, y_val_pred, y_val_proba, \"Validation\")\n", "test_results = evaluate_model(y_test, y_test_pred, y_test_proba, \"Test\")\n", "\n", "print(f\"\\n📈 BASELINE MODEL PERFORMANCE:\")\n", "print(f\"{'Metric':<12} {'Train':<8} {'Val':<8} {'Test':<8}\")\n", "print(\"-\" * 40)\n", "\n", "for metric in ['accuracy', 'precision', 'recall', 'f1', 'kappa', 'mcc']:\n", "    train_val = train_results.get(metric, 0)\n", "    val_val = val_results.get(metric, 0)\n", "    test_val = test_results.get(metric, 0)\n", "    print(f\"{metric.capitalize():<12} {train_val:<8.3f} {val_val:<8.3f} {test_val:<8.3f}\")\n", "\n", "if 'roc_auc' in val_results:\n", "    print(f\"{'ROC AUC':<12} {train_results['roc_auc']:<8.3f} {val_results['roc_auc']:<8.3f} {test_results['roc_auc']:<8.3f}\")\n", "\n", "# Check for overfitting\n", "train_val_diff = train_results['accuracy'] - val_results['accuracy']\n", "print(f\"\\n🔍 Overfitting Analysis:\")\n", "print(f\"  • Train-Val accuracy difference: {train_val_diff:.3f}\")\n", "if train_val_diff > 0.1:\n", "    print(f\"  • ⚠️ Potential overfitting detected\")\n", "elif train_val_diff > 0.05:\n", "    print(f\"  • ⚠️ Mild overfitting\")\n", "else:\n", "    print(f\"  • ✅ Good generalization\")\n", "\n", "# Feature importance analysis\n", "print(f\"\\n🔍 Feature Importance Analysis:\")\n", "feature_names = vectorizer.get_feature_names_out()\n", "feature_importance = rf_baseline.feature_importances_\n", "\n", "# Get top important features\n", "top_indices = np.argsort(feature_importance)[-20:][::-1]\n", "top_features = [(feature_names[i], feature_importance[i]) for i in top_indices]\n", "\n", "print(f\"  • Top 10 Most Important Features:\")\n", "for i, (feature, importance) in enumerate(top_features[:10]):\n", "    print(f\"    {i+1:2d}. {feature:<20} : {importance:.4f}\")\n", "\n", "# Store baseline results\n", "baseline_results = {\n", "    'model': rf_baseline,\n", "    'params': baseline_params,\n", "    'training_time': training_time,\n", "    'train_metrics': train_results,\n", "    'val_metrics': val_results,\n", "    'test_metrics': test_results,\n", "    'feature_importance': dict(zip(feature_names, feature_importance)),\n", "    'top_features': top_features\n", "}\n", "\n", "print(f\"\\n✅ Baseline model evaluation completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Hyperparameter Tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hyperparameter tuning with Grid Search and Random Search\n", "print(\"🎯 HYPERPARAMETER TUNING\")\n", "print(\"=\" * 50)\n", "\n", "# Define parameter grids\n", "# Grid Search - focused search around promising values\n", "grid_params = {\n", "    'n_estimators': [100, 200, 300],\n", "    'max_depth': [10, 20, 30, None],\n", "    'min_samples_split': [2, 5, 10],\n", "    'min_samples_leaf': [1, 2, 4],\n", "    'max_features': ['sqrt', 'log2', 0.3]\n", "}\n", "\n", "# Random Search - broader exploration\n", "random_params = {\n", "    'n_estimators': stats.randint(50, 500),\n", "    'max_depth': [5, 10, 15, 20, 25, 30, None],\n", "    'min_samples_split': stats.randint(2, 20),\n", "    'min_samples_leaf': stats.randint(1, 10),\n", "    'max_features': ['sqrt', 'log2', 0.2, 0.3, 0.4, 0.5],\n", "    'bootstrap': [True, False]\n", "}\n", "\n", "# Setup cross-validation\n", "cv_folds = 5\n", "cv_strategy = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)\n", "\n", "print(f\"\\n🔧 Tuning Configuration:\")\n", "print(f\"  • CV folds: {cv_folds}\")\n", "print(f\"  • Grid search combinations: {np.prod([len(v) for v in grid_params.values()])}\")\n", "print(f\"  • Random search iterations: 50\")\n", "\n", "# Base estimator for tuning\n", "base_rf = RandomForestClassifier(\n", "    random_state=42,\n", "    n_jobs=-1,\n", "    class_weight='balanced' if use_class_weights else None\n", ")\n", "\n", "# Grid Search\n", "print(f\"\\n🔍 Starting Grid Search...\")\n", "grid_start = datetime.now()\n", "\n", "grid_search = GridSearchCV(\n", "    estimator=base_rf,\n", "    param_grid=grid_params,\n", "    cv=cv_strategy,\n", "    scoring='f1_weighted',\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "\n", "grid_search.fit(X_train, y_train)\n", "grid_time = (datetime.now() - grid_start).total_seconds()\n", "\n", "print(f\"✅ Grid Search completed in {grid_time:.1f} seconds\")\n", "print(f\"  • Best CV score: {grid_search.best_score_:.4f}\")\n", "print(f\"  • Best parameters: {grid_search.best_params_}\")\n", "\n", "# Random Search\n", "print(f\"\\n🎲 Starting Random Search...\")\n", "random_start = datetime.now()\n", "\n", "random_search = RandomizedSearchCV(\n", "    estimator=base_rf,\n", "    param_distributions=random_params,\n", "    n_iter=50,\n", "    cv=cv_strategy,\n", "    scoring='f1_weighted',\n", "    n_jobs=-1,\n", "    random_state=42,\n", "    verbose=1\n", ")\n", "\n", "random_search.fit(X_train, y_train)\n", "random_time = (datetime.now() - random_start).total_seconds()\n", "\n", "print(f\"✅ Random Search completed in {random_time:.1f} seconds\")\n", "print(f\"  • Best CV score: {random_search.best_score_:.4f}\")\n", "print(f\"  • Best parameters: {random_search.best_params_}\")\n", "\n", "# Compare tuning results\n", "print(f\"\\n📊 TUNING RESULTS COMPARISON:\")\n", "print(f\"{'Method':<15} {'CV Score':<10} {'Time (s)':<10}\")\n", "print(\"-\" * 40)\n", "print(f\"{'Baseline':<15} {val_results['f1']:<10.4f} {training_time:<10.1f}\")\n", "print(f\"{'Grid Search':<15} {grid_search.best_score_:<10.4f} {grid_time:<10.1f}\")\n", "print(f\"{'Random Search':<15} {random_search.best_score_:<10.4f} {random_time:<10.1f}\")\n", "\n", "# Select best model\n", "if grid_search.best_score_ > random_search.best_score_:\n", "    best_model = grid_search.best_estimator_\n", "    best_params = grid_search.best_params_\n", "    best_score = grid_search.best_score_\n", "    best_method = 'Grid Search'\n", "else:\n", "    best_model = random_search.best_estimator_\n", "    best_params = random_search.best_params_\n", "    best_score = random_search.best_score_\n", "    best_method = 'Random Search'\n", "\n", "print(f\"\\n🏆 BEST MODEL: {best_method}\")\n", "print(f\"  • CV Score: {best_score:.4f}\")\n", "print(f\"  • Parameters: {best_params}\")\n", "\n", "# Store tuning results\n", "tuning_results = {\n", "    'grid_search': {\n", "        'best_score': grid_search.best_score_,\n", "        'best_params': grid_search.best_params_,\n", "        'time': grid_time\n", "    },\n", "    'random_search': {\n", "        'best_score': random_search.best_score_,\n", "        'best_params': random_search.best_params_,\n", "        'time': random_time\n", "    },\n", "    'best_method': best_method,\n", "    'best_model': best_model,\n", "    'best_params': best_params,\n", "    'best_score': best_score\n", "}\n", "\n", "print(f\"\\n✅ Hyperparameter tuning completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Best Model Evaluation & Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive evaluation of the best model\n", "print(\"📊 BEST MODEL EVALUATION & ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Make predictions with best model\n", "print(f\"\\n🔄 Evaluating best model ({best_method})...\")\n", "y_train_best = best_model.predict(X_train)\n", "y_val_best = best_model.predict(X_val)\n", "y_test_best = best_model.predict(X_test)\n", "\n", "# Get prediction probabilities\n", "if len(unique_labels) == 2:\n", "    y_train_proba_best = best_model.predict_proba(X_train)[:, 1]\n", "    y_val_proba_best = best_model.predict_proba(X_val)[:, 1]\n", "    y_test_proba_best = best_model.predict_proba(X_test)[:, 1]\n", "else:\n", "    y_train_proba_best = best_model.predict_proba(X_train)\n", "    y_val_proba_best = best_model.predict_proba(X_val)\n", "    y_test_proba_best = best_model.predict_proba(X_test)\n", "\n", "# Evaluate best model\n", "best_train_results = evaluate_model(y_train, y_train_best, y_train_proba_best, \"Train\")\n", "best_val_results = evaluate_model(y_val, y_val_best, y_val_proba_best, \"Validation\")\n", "best_test_results = evaluate_model(y_test, y_test_best, y_test_proba_best, \"Test\")\n", "\n", "# Compare with baseline\n", "print(f\"\\n📈 PERFORMANCE COMPARISON:\")\n", "print(f\"{'Metric':<12} {'Baseline':<10} {'Best Model':<12} {'Improvement':<12}\")\n", "print(\"-\" * 50)\n", "\n", "for metric in ['accuracy', 'precision', 'recall', 'f1', 'kappa', 'mcc']:\n", "    baseline_val = val_results.get(metric, 0)\n", "    best_val = best_val_results.get(metric, 0)\n", "    improvement = best_val - baseline_val\n", "    improvement_pct = (improvement / baseline_val * 100) if baseline_val > 0 else 0\n", "    \n", "    print(f\"{metric.capitalize():<12} {baseline_val:<10.3f} {best_val:<12.3f} {improvement:>+7.3f} ({improvement_pct:>+5.1f}%)\")\n", "\n", "# Detailed classification report\n", "print(f\"\\n📋 DETAILED CLASSIFICATION REPORT (Test Set):\")\n", "class_names = [str(label) for label in unique_labels]\n", "print(classification_report(y_test, y_test_best, target_names=class_names))\n", "\n", "# Confusion Matrix\n", "print(f\"\\n🔍 CONFUSION MATRIX (Test Set):\")\n", "cm = confusion_matrix(y_test, y_test_best)\n", "cm_df = pd.DataFrame(cm, index=class_names, columns=class_names)\n", "print(cm_df)\n", "\n", "# Calculate per-class metrics\n", "print(f\"\\n📊 PER-CLASS PERFORMANCE (Test Set):\")\n", "precision, recall, f1, support = precision_recall_fscore_support(y_test, y_test_best, average=None)\n", "\n", "print(f\"{'Class':<15} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<10}\")\n", "print(\"-\" * 60)\n", "for i, class_name in enumerate(class_names):\n", "    print(f\"{class_name:<15} {precision[i]:<10.3f} {recall[i]:<10.3f} {f1[i]:<10.3f} {support[i]:<10}\")\n", "\n", "# Feature importance analysis for best model\n", "print(f\"\\n🔍 FEATURE IMPORTANCE ANALYSIS (Best Model):\")\n", "best_feature_importance = best_model.feature_importances_\n", "best_top_indices = np.argsort(best_feature_importance)[-20:][::-1]\n", "best_top_features = [(feature_names[i], best_feature_importance[i]) for i in best_top_indices]\n", "\n", "print(f\"  • Top 15 Most Important Features:\")\n", "for i, (feature, importance) in enumerate(best_top_features[:15]):\n", "    print(f\"    {i+1:2d}. {feature:<25} : {importance:.4f}\")\n", "\n", "# Feature importance comparison\n", "print(f\"\\n🔄 Feature Importance Changes (Baseline vs Best):\")\n", "baseline_importance = baseline_results['feature_importance']\n", "importance_changes = []\n", "\n", "for feature, best_imp in best_top_features[:10]:\n", "    baseline_imp = baseline_importance.get(feature, 0)\n", "    change = best_imp - baseline_imp\n", "    importance_changes.append((feature, baseline_imp, best_imp, change))\n", "\n", "print(f\"{'Feature':<20} {'Baseline':<10} {'Best':<10} {'Change':<10}\")\n", "print(\"-\" * 55)\n", "for feature, base_imp, best_imp, change in importance_changes:\n", "    print(f\"{feature:<20} {base_imp:<10.4f} {best_imp:<10.4f} {change:>+10.4f}\")\n", "\n", "# Model complexity analysis\n", "print(f\"\\n🔧 MODEL COMPLEXITY ANALYSIS:\")\n", "print(f\"  • Number of trees: {best_model.n_estimators}\")\n", "print(f\"  • Max depth: {best_model.max_depth}\")\n", "print(f\"  • Min samples split: {best_model.min_samples_split}\")\n", "print(f\"  • Min samples leaf: {best_model.min_samples_leaf}\")\n", "print(f\"  • Max features: {best_model.max_features}\")\n", "\n", "# Calculate model size and complexity\n", "total_nodes = sum(tree.tree_.node_count for tree in best_model.estimators_)\n", "avg_depth = np.mean([tree.tree_.max_depth for tree in best_model.estimators_])\n", "\n", "print(f\"  • Total nodes: {total_nodes:,}\")\n", "print(f\"  • Average tree depth: {avg_depth:.1f}\")\n", "print(f\"  • Model complexity: {'High' if total_nodes > 10000 else 'Medium' if total_nodes > 5000 else 'Low'}\")\n", "\n", "# Cross-validation analysis\n", "print(f\"\\n🔄 CROSS-VALIDATION ANALYSIS:\")\n", "cv_scores = cross_val_score(best_model, X_train, y_train, cv=cv_strategy, scoring='f1_weighted')\n", "print(f\"  • CV F1 scores: {cv_scores}\")\n", "print(f\"  • Mean CV F1: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}\")\n", "print(f\"  • CV stability: {'Good' if cv_scores.std() < 0.05 else 'Moderate' if cv_scores.std() < 0.1 else 'Poor'}\")\n", "\n", "# Store best model results\n", "best_model_results = {\n", "    'model': best_model,\n", "    'method': best_method,\n", "    'params': best_params,\n", "    'cv_score': best_score,\n", "    'train_metrics': best_train_results,\n", "    'val_metrics': best_val_results,\n", "    'test_metrics': best_test_results,\n", "    'feature_importance': dict(zip(feature_names, best_feature_importance)),\n", "    'top_features': best_top_features,\n", "    'complexity': {\n", "        'total_nodes': total_nodes,\n", "        'avg_depth': avg_depth,\n", "        'n_estimators': best_model.n_estimators\n", "    },\n", "    'cv_analysis': {\n", "        'scores': cv_scores.tolist(),\n", "        'mean': cv_scores.mean(),\n", "        'std': cv_scores.std()\n", "    }\n", "}\n", "\n", "print(f\"\\n✅ Best model evaluation completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Visualization & Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualizations\n", "print(\"📊 VISUALIZATION & ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Create subplots for comprehensive analysis\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('Model Performance Comparison', 'Feature Importance (Top 15)',\n", "                   'Confusion Matrix Heatmap', 'Cross-Validation Scores'),\n", "    specs=[[{'type': 'bar'}, {'type': 'bar'}],\n", "           [{'type': 'heatmap'}, {'type': 'box'}]]\n", ")\n", "\n", "# 1. Model Performance Comparison\n", "metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']\n", "baseline_scores = [val_results['accuracy'], val_results['precision'], \n", "                  val_results['recall'], val_results['f1']]\n", "best_scores = [best_val_results['accuracy'], best_val_results['precision'],\n", "              best_val_results['recall'], best_val_results['f1']]\n", "\n", "fig.add_trace(\n", "    go.Bar(x=metrics, y=baseline_scores, name='Baseline', marker_color='lightblue'),\n", "    row=1, col=1\n", ")\n", "fig.add_trace(\n", "    go.Bar(x=metrics, y=best_scores, name='Best Model', marker_color='lightgreen'),\n", "    row=1, col=1\n", ")\n", "\n", "# 2. Feature Importance\n", "top_15_features = [f[0] for f in best_top_features[:15]]\n", "top_15_importance = [f[1] for f in best_top_features[:15]]\n", "\n", "fig.add_trace(\n", "    go.Bar(x=top_15_importance, y=top_15_features, orientation='h',\n", "           name='Importance', marker_color='lightcoral'),\n", "    row=1, col=2\n", ")\n", "\n", "# 3. Confusion Matrix\n", "fig.add_trace(\n", "    go.Heatmap(z=cm, x=class_names, y=class_names, \n", "               colorscale='Blues', name='Confusion Matrix'),\n", "    row=2, col=1\n", ")\n", "\n", "# 4. Cross-Validation Scores\n", "fig.add_trace(\n", "    go.Box(y=cv_scores, name='CV Scores', marker_color='lightyellow'),\n", "    row=2, col=2\n", ")\n", "\n", "fig.update_layout(height=800, showlegend=True, \n", "                 title_text=\"🌲 Random Forest Model Analysis\")\n", "fig.show()\n", "\n", "# Additional analysis plots\n", "print(f\"\\n📈 Additional Analysis:\")\n", "\n", "# Learning curve analysis (if dataset is large enough)\n", "if len(X_train) > 100:\n", "    from sklearn.model_selection import learning_curve\n", "    \n", "    print(f\"  • Generating learning curves...\")\n", "    train_sizes = np.linspace(0.1, 1.0, 10)\n", "    train_sizes_abs, train_scores, val_scores = learning_curve(\n", "        best_model, X_train, y_train, \n", "        train_sizes=train_sizes, cv=3, scoring='f1_weighted', n_jobs=-1\n", "    )\n", "    \n", "    # Plot learning curves\n", "    fig_learning = go.Figure()\n", "    \n", "    fig_learning.add_trace(go.<PERSON>(\n", "        x=train_sizes_abs, y=train_scores.mean(axis=1),\n", "        mode='lines+markers', name='Training Score',\n", "        error_y=dict(type='data', array=train_scores.std(axis=1))\n", "    ))\n", "    \n", "    fig_learning.add_trace(go.<PERSON>(\n", "        x=train_sizes_abs, y=val_scores.mean(axis=1),\n", "        mode='lines+markers', name='Validation Score',\n", "        error_y=dict(type='data', array=val_scores.std(axis=1))\n", "    ))\n", "    \n", "    fig_learning.update_layout(\n", "        title='Learning Curves',\n", "        xaxis_title='Training Set Size',\n", "        yaxis_title='F1 Score',\n", "        height=400\n", "    )\n", "    fig_learning.show()\n", "\n", "# ROC Curve for binary classification\n", "if len(unique_labels) == 2:\n", "    print(f\"  • Generating ROC curve...\")\n", "    fpr, tpr, _ = roc_curve(y_test, y_test_proba_best)\n", "    auc_score = roc_auc_score(y_test, y_test_proba_best)\n", "    \n", "    fig_roc = go.Figure()\n", "    fig_roc.add_trace(go.<PERSON>(\n", "        x=fpr, y=tpr, mode='lines',\n", "        name=f'ROC Curve (AUC = {auc_score:.3f})'\n", "    ))\n", "    fig_roc.add_trace(go.<PERSON>(\n", "        x=[0, 1], y=[0, 1], mode='lines',\n", "        name='Random Classifier', line=dict(dash='dash')\n", "    ))\n", "    \n", "    fig_roc.update_layout(\n", "        title='ROC Curve',\n", "        xaxis_title='False Positive Rate',\n", "        yaxis_title='True Positive Rate',\n", "        height=400\n", "    )\n", "    fig_roc.show()\n", "\n", "print(f\"\\n✅ Visualization and analysis completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save Random Forest Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all Random Forest results\n", "print(\"💾 SAVING RANDOM FOREST RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Create directories\n", "os.makedirs('models/random_forest', exist_ok=True)\n", "os.makedirs('data/results', exist_ok=True)\n", "os.makedirs('data/predictions', exist_ok=True)\n", "\n", "# Save best model\n", "best_model_file = 'models/random_forest/rf_best_model.pkl'\n", "joblib.dump(best_model, best_model_file)\n", "print(f\"✅ Best model saved: {best_model_file}\")\n", "\n", "# Save baseline model for comparison\n", "baseline_model_file = 'models/random_forest/rf_baseline_model.pkl'\n", "joblib.dump(rf_baseline, baseline_model_file)\n", "print(f\"✅ Baseline model saved: {baseline_model_file}\")\n", "\n", "# Save predictions\n", "predictions_data = {\n", "    'y_train_true': y_train.tolist(),\n", "    'y_train_pred': y_train_best.tolist(),\n", "    'y_val_true': y_val.tolist(),\n", "    'y_val_pred': y_val_best.tolist(),\n", "    'y_test_true': y_test.tolist(),\n", "    'y_test_pred': y_test_best.tolist()\n", "}\n", "\n", "if len(unique_labels) == 2:\n", "    predictions_data.update({\n", "        'y_train_proba': y_train_proba_best.tolist(),\n", "        'y_val_proba': y_val_proba_best.tolist(),\n", "        'y_test_proba': y_test_proba_best.tolist()\n", "    })\n", "\n", "predictions_file = 'data/predictions/rf_predictions.json'\n", "with open(predictions_file, 'w') as f:\n", "    json.dump(predictions_data, f, indent=2)\n", "print(f\"✅ Predictions saved: {predictions_file}\")\n", "\n", "# Save comprehensive results\n", "rf_results = {\n", "    'model_info': {\n", "        'algorithm': 'Random Forest',\n", "        'labeling_method': labeling_method,\n", "        'feature_config': feature_config,\n", "        'training_date': datetime.now().isoformat(),\n", "        'dataset_size': total_samples,\n", "        'feature_count': X_dense.shape[1]\n", "    },\n", "    'data_split': split_info,\n", "    'baseline_results': {\n", "        'params': baseline_params,\n", "        'training_time': training_time,\n", "        'metrics': {\n", "            'train': baseline_results['train_metrics'],\n", "            'val': baseline_results['val_metrics'],\n", "            'test': baseline_results['test_metrics']\n", "        }\n", "    },\n", "    'hyperparameter_tuning': tuning_results,\n", "    'best_model_results': {\n", "        'method': best_method,\n", "        'params': best_params,\n", "        'cv_score': best_score,\n", "        'metrics': {\n", "            'train': best_model_results['train_metrics'],\n", "            'val': best_model_results['val_metrics'],\n", "            'test': best_model_results['test_metrics']\n", "        },\n", "        'complexity': best_model_results['complexity'],\n", "        'cv_analysis': best_model_results['cv_analysis']\n", "    },\n", "    'feature_analysis': {\n", "        'top_features': best_top_features[:20],\n", "        'importance_stats': {\n", "            'mean': float(np.mean(best_feature_importance)),\n", "            'std': float(np.std(best_feature_importance)),\n", "            'max': float(np.max(best_feature_importance)),\n", "            'min': float(np.min(best_feature_importance))\n", "        }\n", "    },\n", "    'performance_summary': {\n", "        'best_test_accuracy': float(best_test_results['accuracy']),\n", "        'best_test_f1': float(best_test_results['f1']),\n", "        'improvement_over_baseline': {\n", "            'accuracy': float(best_val_results['accuracy'] - val_results['accuracy']),\n", "            'f1': float(best_val_results['f1'] - val_results['f1'])\n", "        },\n", "        'overfitting_check': {\n", "            'train_val_diff': float(best_train_results['accuracy'] - best_val_results['accuracy']),\n", "            'status': 'Good' if (best_train_results['accuracy'] - best_val_results['accuracy']) < 0.05 else 'Overfitting'\n", "        }\n", "    }\n", "}\n", "\n", "results_file = 'data/results/random_forest_results.json'\n", "with open(results_file, 'w') as f:\n", "    json.dump(rf_results, f, indent=2, default=str)\n", "print(f\"✅ Comprehensive results saved: {results_file}\")\n", "\n", "# Save feature importance as Excel for easy analysis\n", "feature_importance_df = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'importance': best_feature_importance,\n", "    'rank': range(1, len(feature_names) + 1)\n", "}).sort_values('importance', ascending=False).reset_index(drop=True)\n", "\n", "feature_importance_df['rank'] = range(1, len(feature_importance_df) + 1)\n", "\n", "feature_importance_file = 'data/results/rf_feature_importance.xlsx'\n", "feature_importance_df.to_excel(feature_importance_file, index=False)\n", "print(f\"✅ Feature importance saved: {feature_importance_file}\")\n", "\n", "# Save model artifacts for production use\n", "model_artifacts = {\n", "    'model': best_model,\n", "    'vectorizer': vectorizer,\n", "    'label_encoder': LabelEncoder().fit(y_labels),\n", "    'feature_names': feature_names.tolist(),\n", "    'class_names': unique_labels.tolist(),\n", "    'model_params': best_params,\n", "    'preprocessing_info': {\n", "        'labeling_method': labeling_method,\n", "        'feature_config': feature_config,\n", "        'use_class_weights': use_class_weights\n", "    },\n", "    'performance_metrics': best_test_results,\n", "    'creation_date': datetime.now().isoformat()\n", "}\n", "\n", "artifacts_file = 'models/random_forest/rf_production_artifacts.pkl'\n", "joblib.dump(model_artifacts, artifacts_file)\n", "print(f\"✅ Production artifacts saved: {artifacts_file}\")\n", "\n", "# Create utility functions for model usage\n", "rf_utils_code = '''\n", "import joblib\n", "import numpy as np\n", "import pandas as pd\n", "\n", "def load_rf_model(model_path='models/random_forest/rf_best_model.pkl'):\n", "    \"\"\"Load the trained Random Forest model\"\"\"\n", "    return joblib.load(model_path)\n", "\n", "def load_rf_artifacts(artifacts_path='models/random_forest/rf_production_artifacts.pkl'):\n", "    \"\"\"Load all production artifacts\"\"\"\n", "    return joblib.load(artifacts_path)\n", "\n", "def predict_sentiment_rf(texts, model_path='models/random_forest/rf_production_artifacts.pkl'):\n", "    \"\"\"Predict sentiment for new texts using Random Forest\"\"\"\n", "    artifacts = load_rf_artifacts(model_path)\n", "    \n", "    model = artifacts['model']\n", "    vectorizer = artifacts['vectorizer']\n", "    class_names = artifacts['class_names']\n", "    \n", "    # Vectorize texts\n", "    if isinstance(texts, str):\n", "        texts = [texts]\n", "    \n", "    X = vectorizer.transform(texts)\n", "    \n", "    # Make predictions\n", "    predictions = model.predict(X)\n", "    probabilities = model.predict_proba(X)\n", "    \n", "    # Convert to class names\n", "    predicted_labels = [class_names[pred] for pred in predictions]\n", "    \n", "    results = []\n", "    for i, text in enumerate(texts):\n", "        result = {\n", "            'text': text,\n", "            'predicted_sentiment': predicted_labels[i],\n", "            'confidence': float(np.max(probabilities[i])),\n", "            'probabilities': {class_names[j]: float(probabilities[i][j]) \n", "                           for j in range(len(class_names))}\n", "        }\n", "        results.append(result)\n", "    \n", "    return results if len(results) > 1 else results[0]\n", "\n", "def get_feature_importance_rf(top_n=20, artifacts_path='models/random_forest/rf_production_artifacts.pkl'):\n", "    \"\"\"Get top N most important features\"\"\"\n", "    artifacts = load_rf_artifacts(artifacts_path)\n", "    \n", "    model = artifacts['model']\n", "    feature_names = artifacts['feature_names']\n", "    \n", "    importance = model.feature_importances_\n", "    top_indices = np.argsort(importance)[-top_n:][::-1]\n", "    \n", "    return [(feature_names[i], importance[i]) for i in top_indices]\n", "'''\n", "\n", "with open('utils/random_forest_utils.py', 'w', encoding='utf-8') as f:\n", "    f.write(rf_utils_code)\n", "\n", "print(f\"\\n🔧 Utility functions saved: utils/random_forest_utils.py\")\n", "\n", "print(f\"\\n📊 FILES GENERATED SUMMARY:\")\n", "print(f\"  📁 models/random_forest/\")\n", "print(f\"     • rf_best_model.pkl - Best trained model\")\n", "print(f\"     • rf_baseline_model.pkl - Baseline model\")\n", "print(f\"     • rf_production_artifacts.pkl - Complete artifacts\")\n", "print(f\"  📁 data/results/\")\n", "print(f\"     • random_forest_results.json - Comprehensive results\")\n", "print(f\"     • rf_feature_importance.xlsx - Feature analysis\")\n", "print(f\"  📁 data/predictions/\")\n", "print(f\"     • rf_predictions.json - All predictions\")\n", "print(f\"  🔧 utils/random_forest_utils.py - Utility functions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 RANDOM FOREST TRAINING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n🌲 RANDOM FOREST MODEL TRAINING COMPLETED:\")\n", "print(f\"  • Dataset: {total_samples} samples\")\n", "print(f\"  • Features: {X_dense.shape[1]} TF-IDF features ({feature_config})\")\n", "print(f\"  • Labeling method: {labeling_method}\")\n", "print(f\"  • Classes: {len(unique_labels)} ({', '.join(map(str, unique_labels))})\")\n", "\n", "print(f\"\\n📊 DATA SPLIT:\")\n", "print(f\"  • Training: {len(X_train)} samples ({len(X_train)/total_samples*100:.1f}%)\")\n", "print(f\"  • Validation: {len(X_val)} samples ({len(X_val)/total_samples*100:.1f}%)\")\n", "print(f\"  • Test: {len(X_test)} samples ({len(X_test)/total_samples*100:.1f}%)\")\n", "\n", "print(f\"\\n🎯 HYPERPARAMETER TUNING:\")\n", "print(f\"  • Grid Search: {grid_search.best_score_:.4f} F1-score ({grid_time:.1f}s)\")\n", "print(f\"  • Random Search: {random_search.best_score_:.4f} F1-score ({random_time:.1f}s)\")\n", "print(f\"  • Best method: {best_method}\")\n", "print(f\"  • Best CV score: {best_score:.4f}\")\n", "\n", "print(f\"\\n🏆 BEST MODEL PERFORMANCE:\")\n", "print(f\"  • Test Accuracy: {best_test_results['accuracy']:.3f}\")\n", "print(f\"  • Test F1-Score: {best_test_results['f1']:.3f}\")\n", "print(f\"  • Test Precision: {best_test_results['precision']:.3f}\")\n", "print(f\"  • Test Recall: {best_test_results['recall']:.3f}\")\n", "print(f\"  • <PERSON>'s Kappa: {best_test_results['kappa']:.3f}\")\n", "print(f\"  • Matthews Correlation: {best_test_results['mcc']:.3f}\")\n", "\n", "if 'roc_auc' in best_test_results:\n", "    print(f\"  • ROC AUC: {best_test_results['roc_auc']:.3f}\")\n", "\n", "print(f\"\\n📈 IMPROVEMENT OVER BASELINE:\")\n", "acc_improvement = best_val_results['accuracy'] - val_results['accuracy']\n", "f1_improvement = best_val_results['f1'] - val_results['f1']\n", "print(f\"  • Accuracy: {acc_improvement:+.3f} ({acc_improvement/val_results['accuracy']*100:+.1f}%)\")\n", "print(f\"  • F1-Score: {f1_improvement:+.3f} ({f1_improvement/val_results['f1']*100:+.1f}%)\")\n", "\n", "print(f\"\\n🔧 MODEL CONFIGURATION:\")\n", "print(f\"  • Trees: {best_model.n_estimators}\")\n", "print(f\"  • Max depth: {best_model.max_depth}\")\n", "print(f\"  • Min samples split: {best_model.min_samples_split}\")\n", "print(f\"  • Min samples leaf: {best_model.min_samples_leaf}\")\n", "print(f\"  • Max features: {best_model.max_features}\")\n", "print(f\"  • Total nodes: {total_nodes:,}\")\n", "print(f\"  • Average depth: {avg_depth:.1f}\")\n", "\n", "print(f\"\\n🔍 TOP 5 MOST IMPORTANT FEATURES:\")\n", "for i, (feature, importance) in enumerate(best_top_features[:5]):\n", "    print(f\"  {i+1}. {feature:<25} : {importance:.4f}\")\n", "\n", "print(f\"\\n✅ QUALITY ASSESSMENT:\")\n", "overfitting_diff = best_train_results['accuracy'] - best_val_results['accuracy']\n", "print(f\"  • Overfitting check: {overfitting_diff:.3f} ({'Good' if overfitting_diff < 0.05 else 'Overfitting'})\")\n", "print(f\"  • CV stability: {cv_scores.std():.3f} ({'Good' if cv_scores.std() < 0.05 else 'Moderate'})\")\n", "print(f\"  • Model complexity: {'High' if total_nodes > 10000 else 'Medium' if total_nodes > 5000 else 'Low'}\")\n", "\n", "print(f\"\\n💾 OUTPUT FILES:\")\n", "print(f\"  • Best model: models/random_forest/rf_best_model.pkl\")\n", "print(f\"  • Production artifacts: models/random_forest/rf_production_artifacts.pkl\")\n", "print(f\"  • Results: data/results/random_forest_results.json\")\n", "print(f\"  • Feature importance: data/results/rf_feature_importance.xlsx\")\n", "print(f\"  • Predictions: data/predictions/rf_predictions.json\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. XGBoost model training (08_xgboost_models.ipynb)\")\n", "print(f\"  2. Model comparison and ensemble (09_model_evaluation.ipynb)\")\n", "print(f\"  3. Advanced analysis and topic modeling (10-12)\")\n", "print(f\"  4. Production deployment preparation\")\n", "\n", "print(f\"\\n✅ RANDOM FOREST TRAINING COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for XGBoost training phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}