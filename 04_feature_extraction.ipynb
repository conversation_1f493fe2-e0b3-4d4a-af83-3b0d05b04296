{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ⚙️ 04. Feature Extraction & Term Weighting\n", "\n", "**Tujuan**: Ekstraksi fitur dari teks yang telah dipreproses untuk machine learning\n", "\n", "**Key Features**:\n", "1. 📊 **TF-IDF Vectorization** - Term Frequency-Inverse Document Frequency\n", "2. 🔢 **N-gram Analysis** - <PERSON><PERSON><PERSON>, <PERSON>ram, Trigram\n", "3. 📈 **Feature Selection** - <PERSON><PERSON><PERSON><PERSON> fitur terbaik\n", "4. 🎯 **Dimensionality Analysis** - <PERSON><PERSON><PERSON> dimensi optimal\n", "5. 📊 **Feature Importance** - <PERSON><PERSON><PERSON> penting<PERSON> fitur\n", "6. 💾 **Vectorizer Persistence** - Simpan vectorizer untuk production\n", "\n", "**Input**: `gofood_advanced_preprocessed.xlsx`  \n", "**Output**: Feature matrices dan vectorizer objects\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import pickle\n", "import joblib\n", "from collections import Counter\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "from datetime import datetime\n", "\n", "# Scikit-learn for feature extraction\n", "from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer\n", "from sklearn.feature_selection import SelectKBest, chi2, mutual_info_classif\n", "from sklearn.decomposition import TruncatedSVD\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "\n", "print(\"⚙️ Feature extraction libraries imported successfully!\")\n", "print(f\"🕐 Feature extraction started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Load Preprocessed Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the preprocessed dataset\n", "try:\n", "    df = pd.read_excel('gofood_advanced_preprocessed.xlsx')\n", "    print(f\"✅ Preprocessed dataset loaded: {len(df)} reviews\")\n", "    print(f\"📋 Columns: {list(df.columns)}\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"⚠️ Advanced preprocessed file not found. Trying alternative...\")\n", "    try:\n", "        df = pd.read_excel('gofood_ulasan_preprocessed.xlsx')\n", "        print(f\"✅ Basic preprocessed dataset loaded: {len(df)} reviews\")\n", "        # Use existing preprocessed column\n", "        if 'ulasan_stemmed' in df.columns:\n", "            df['final_processed_text'] = df['ulasan_stemmed']\n", "        else:\n", "            df['final_processed_text'] = df.iloc[:, 0]  # Use first column\n", "    except FileNotFoundError:\n", "        print(\"❌ No preprocessed data found. Creating sample data...\")\n", "        sample_data = {\n", "            'final_processed_text': [\n", "                'makanan enak cepat sampai driver ramah',\n", "                'pelayanan buruk makanan dingin enak',\n", "                'lumayan agak lama tunggu oke',\n", "                'mantap gofood best recommended',\n", "                'aplikasi error susah order makanan'\n", "            ],\n", "            'nilai': [5, 1, 3, 5, 2]\n", "        }\n", "        df = pd.DataFrame(sample_data)\n", "        print(f\"📝 Sample dataset created with {len(df)} reviews\")\n", "\n", "# Identify text and target columns\n", "text_column = 'final_processed_text'\n", "if text_column not in df.columns:\n", "    # Find the best text column\n", "    text_candidates = [col for col in df.columns if 'text' in col.lower() or 'ulasan' in col.lower()]\n", "    if text_candidates:\n", "        text_column = text_candidates[-1]  # Use the last (most processed) text column\n", "    else:\n", "        text_column = df.columns[0]\n", "\n", "target_column = 'nilai' if 'nilai' in df.columns else None\n", "\n", "print(f\"📝 Using text column: {text_column}\")\n", "print(f\"🎯 Using target column: {target_column}\")\n", "\n", "# Basic data info\n", "print(f\"\\n📊 Dataset Overview:\")\n", "print(f\"  • Total reviews: {len(df)}\")\n", "print(f\"  • Text column: {text_column}\")\n", "print(f\"  • Target column: {target_column}\")\n", "\n", "# Check for empty texts\n", "empty_texts = df[text_column].isna().sum() + (df[text_column].astype(str).str.strip() == '').sum()\n", "print(f\"  • Empty texts: {empty_texts} ({empty_texts/len(df)*100:.1f}%)\")\n", "\n", "# Remove empty texts\n", "if empty_texts > 0:\n", "    df = df[df[text_column].notna() & (df[text_column].astype(str).str.strip() != '')]\n", "    print(f\"  • After removing empty texts: {len(df)} reviews\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Text Statistics & Vocabulary Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze text characteristics for feature extraction\n", "print(\"📊 TEXT STATISTICS FOR FEATURE EXTRACTION\")\n", "print(\"=\" * 50)\n", "\n", "# Combine all texts\n", "all_texts = df[text_column].astype(str).tolist()\n", "all_words = ' '.join(all_texts).split()\n", "unique_words = set(all_words)\n", "word_freq = Counter(all_words)\n", "\n", "# Basic statistics\n", "text_lengths = [len(text.split()) for text in all_texts]\n", "char_lengths = [len(text) for text in all_texts]\n", "\n", "print(f\"\\n📝 Text Characteristics:\")\n", "print(f\"  • Total documents: {len(all_texts):,}\")\n", "print(f\"  • Total words: {len(all_words):,}\")\n", "print(f\"  • Unique words (vocabulary): {len(unique_words):,}\")\n", "print(f\"  • Vocabulary richness: {len(unique_words)/len(all_words):.3f}\")\n", "\n", "print(f\"\\n📏 Length Statistics:\")\n", "print(f\"  • Avg words per document: {np.mean(text_lengths):.1f}\")\n", "print(f\"  • Median words per document: {np.median(text_lengths):.1f}\")\n", "print(f\"  • Min words: {min(text_lengths)}\")\n", "print(f\"  • Max words: {max(text_lengths)}\")\n", "print(f\"  • Avg characters per document: {np.mean(char_lengths):.1f}\")\n", "\n", "# Word frequency analysis\n", "print(f\"\\n🔝 Top 20 Most Frequent Words:\")\n", "for word, count in word_freq.most_common(20):\n", "    if word.strip() and len(word) > 1:\n", "        print(f\"  {word:<15} : {count:>4} times ({count/len(all_words)*100:.2f}%)\")\n", "\n", "# Rare words analysis\n", "rare_words = [word for word, count in word_freq.items() if count == 1]\n", "print(f\"\\n🔍 Rare Words Analysis:\")\n", "print(f\"  • Words appearing only once: {len(rare_words)} ({len(rare_words)/len(unique_words)*100:.1f}%)\")\n", "print(f\"  • Words appearing 2+ times: {len(unique_words) - len(rare_words)}\")\n", "\n", "# Document frequency analysis\n", "doc_freq = {}\n", "for text in all_texts:\n", "    words_in_doc = set(text.split())\n", "    for word in words_in_doc:\n", "        doc_freq[word] = doc_freq.get(word, 0) + 1\n", "\n", "# Words by document frequency\n", "very_common = sum(1 for freq in doc_freq.values() if freq > len(all_texts) * 0.8)\n", "common = sum(1 for freq in doc_freq.values() if len(all_texts) * 0.1 < freq <= len(all_texts) * 0.8)\n", "uncommon = sum(1 for freq in doc_freq.values() if freq <= len(all_texts) * 0.1)\n", "\n", "print(f\"\\n📈 Document Frequency Distribution:\")\n", "print(f\"  • Very common words (>80% docs): {very_common}\")\n", "print(f\"  • Common words (10-80% docs): {common}\")\n", "print(f\"  • Uncommon words (<10% docs): {uncommon}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔢 N-gram Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# N-gram analysis to understand text patterns\n", "print(\"🔢 N-GRAM ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "def extract_ngrams(texts, n=2):\n", "    \"\"\"Extract n-grams from texts\"\"\"\n", "    ngrams = []\n", "    for text in texts:\n", "        words = text.split()\n", "        for i in range(len(words) - n + 1):\n", "            ngram = ' '.join(words[i:i+n])\n", "            ngrams.append(ngram)\n", "    return ngrams\n", "\n", "# Analyze unigrams (already done above)\n", "print(f\"\\n1️⃣ UNIGRAMS (Single Words):\")\n", "print(f\"  • Total unigrams: {len(all_words):,}\")\n", "print(f\"  • Unique unigrams: {len(unique_words):,}\")\n", "\n", "# Analyze bigrams\n", "bigrams = extract_ngrams(all_texts, n=2)\n", "bigram_freq = Counter(bigrams)\n", "unique_bigrams = set(bigrams)\n", "\n", "print(f\"\\n2️⃣ BIGRAMS (Two-word phrases):\")\n", "print(f\"  • Total bigrams: {len(bigrams):,}\")\n", "print(f\"  • Unique bigrams: {len(unique_bigrams):,}\")\n", "print(f\"  • Top 10 bigrams:\")\n", "for bigram, count in bigram_freq.most_common(10):\n", "    print(f\"    '{bigram}' : {count} times\")\n", "\n", "# Analyze trigrams\n", "trigrams = extract_ngrams(all_texts, n=3)\n", "trigram_freq = Counter(trigrams)\n", "unique_trigrams = set(trigrams)\n", "\n", "print(f\"\\n3️⃣ TRIGRAMS (Three-word phrases):\")\n", "print(f\"  • Total trigrams: {len(trigrams):,}\")\n", "print(f\"  • Unique trigrams: {len(unique_trigrams):,}\")\n", "print(f\"  • Top 10 trigrams:\")\n", "for trigram, count in trigram_freq.most_common(10):\n", "    if count > 1:  # Only show trigrams that appear more than once\n", "        print(f\"    '{trigram}' : {count} times\")\n", "\n", "# N-gram statistics summary\n", "print(f\"\\n📊 N-gram Summary:\")\n", "print(f\"  • Unigram vocabulary: {len(unique_words):,}\")\n", "print(f\"  • Bigram vocabulary: {len(unique_bigrams):,}\")\n", "print(f\"  • Trigram vocabulary: {len(unique_trigrams):,}\")\n", "print(f\"  • Total n-gram features: {len(unique_words) + len(unique_bigrams) + len(unique_trigrams):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 TF-IDF Vectorization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TF-IDF Vectorization with different configurations\n", "print(\"📊 TF-IDF VECTORIZATION\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare texts\n", "texts = df[text_column].astype(str).tolist()\n", "\n", "# Configuration 1: Unigrams only\n", "print(\"\\n1️⃣ UNIGRAM TF-IDF:\")\n", "tfidf_unigram = TfidfVectorizer(\n", "    max_features=5000,\n", "    min_df=2,  # Word must appear in at least 2 documents\n", "    max_df=0.8,  # Word must appear in less than 80% of documents\n", "    ngram_range=(1, 1),\n", "    lowercase=True,\n", "    stop_words=None  # Already removed in preprocessing\n", ")\n", "\n", "X_unigram = tfidf_unigram.fit_transform(texts)\n", "print(f\"  • Feature matrix shape: {X_unigram.shape}\")\n", "print(f\"  • Vocabulary size: {len(tfidf_unigram.vocabulary_)}\")\n", "print(f\"  • Sparsity: {(1 - X_unigram.nnz / (X_unigram.shape[0] * X_unigram.shape[1]))*100:.2f}%\")\n", "\n", "# Configuration 2: Unigrams + Bigrams\n", "print(\"\\n2️⃣ UNIGRAM + BIGRAM TF-IDF:\")\n", "tfidf_bigram = TfidfVectorizer(\n", "    max_features=8000,\n", "    min_df=2,\n", "    max_df=0.8,\n", "    ngram_range=(1, 2),\n", "    lowercase=True,\n", "    stop_words=None\n", ")\n", "\n", "X_bigram = tfidf_bigram.fit_transform(texts)\n", "print(f\"  • Feature matrix shape: {X_bigram.shape}\")\n", "print(f\"  • Vocabulary size: {len(tfidf_bigram.vocabulary_)}\")\n", "print(f\"  • Sparsity: {(1 - X_bigram.nnz / (X_bigram.shape[0] * X_bigram.shape[1]))*100:.2f}%\")\n", "\n", "# Configuration 3: Unigrams + Bigrams + Trigrams\n", "print(\"\\n3️⃣ UNIGRAM + BIGRAM + TRIGRAM TF-IDF:\")\n", "tfidf_trigram = TfidfVectorizer(\n", "    max_features=10000,\n", "    min_df=2,\n", "    max_df=0.8,\n", "    ngram_range=(1, 3),\n", "    lowercase=True,\n", "    stop_words=None\n", ")\n", "\n", "X_trigram = tfidf_trigram.fit_transform(texts)\n", "print(f\"  • Feature matrix shape: {X_trigram.shape}\")\n", "print(f\"  • Vocabulary size: {len(tfidf_trigram.vocabulary_)}\")\n", "print(f\"  • Sparsity: {(1 - X_trigram.nnz / (X_trigram.shape[0] * X_trigram.shape[1]))*100:.2f}%\")\n", "\n", "# Configuration 4: Optimized for Indonesian (conservative)\n", "print(\"\\n4️⃣ OPTIMIZED TF-IDF (Recommended):\")\n", "tfidf_optimized = TfidfVectorizer(\n", "    max_features=3000,  # Conservative for small datasets\n", "    min_df=3,  # More conservative min_df\n", "    max_df=0.7,  # More conservative max_df\n", "    ngram_range=(1, 2),  # Unigrams + Bigrams\n", "    lowercase=True,\n", "    stop_words=None,\n", "    sublinear_tf=True,  # Apply sublinear tf scaling\n", "    norm='l2'  # L2 normalization\n", ")\n", "\n", "X_optimized = tfidf_optimized.fit_transform(texts)\n", "print(f\"  • Feature matrix shape: {X_optimized.shape}\")\n", "print(f\"  • Vocabulary size: {len(tfidf_optimized.vocabulary_)}\")\n", "print(f\"  • Sparsity: {(1 - X_optimized.nnz / (X_optimized.shape[0] * X_optimized.shape[1]))*100:.2f}%\")\n", "\n", "# Store configurations for comparison\n", "vectorizer_configs = {\n", "    'unigram': (tfidf_unigram, X_unigram),\n", "    'bigram': (tfidf_bigram, X_bigram),\n", "    'trigram': (tfidf_trigram, X_trigram),\n", "    'optimized': (tfidf_optimized, X_optimized)\n", "}\n", "\n", "print(f\"\\n✅ TF-IDF vectorization completed with 4 configurations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Feature Analysis & Selection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze feature importance and characteristics\n", "print(\"🎯 FEATURE ANALYSIS & SELECTION\")\n", "print(\"=\" * 50)\n", "\n", "# Use optimized configuration for detailed analysis\n", "vectorizer = tfidf_optimized\n", "X_features = X_optimized\n", "\n", "# Get feature names\n", "feature_names = vectorizer.get_feature_names_out()\n", "print(f\"\\n📊 Feature Analysis (Optimized TF-IDF):\")\n", "print(f\"  • Total features: {len(feature_names)}\")\n", "\n", "# Analyze feature types\n", "unigrams = [f for f in feature_names if len(f.split()) == 1]\n", "bigrams = [f for f in feature_names if len(f.split()) == 2]\n", "trigrams = [f for f in feature_names if len(f.split()) == 3]\n", "\n", "print(f\"  • Unigrams: {len(unigrams)} ({len(unigrams)/len(feature_names)*100:.1f}%)\")\n", "print(f\"  • Bigrams: {len(bigrams)} ({len(bigrams)/len(feature_names)*100:.1f}%)\")\n", "print(f\"  • Trigrams: {len(trigrams)} ({len(trigrams)/len(feature_names)*100:.1f}%)\")\n", "\n", "# Calculate feature statistics\n", "feature_sums = np.array(X_features.sum(axis=0)).flatten()\n", "feature_stats = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'total_tfidf': feature_sums,\n", "    'avg_tfidf': feature_sums / X_features.shape[0],\n", "    'n_words': [len(f.split()) for f in feature_names]\n", "})\n", "\n", "feature_stats = feature_stats.sort_values('total_tfidf', ascending=False)\n", "\n", "print(f\"\\n🔝 Top 20 Features by Total TF-IDF Score:\")\n", "for i, row in feature_stats.head(20).iterrows():\n", "    print(f\"  {row['feature']:<20} : {row['total_tfidf']:.3f} (avg: {row['avg_tfidf']:.4f})\")\n", "\n", "# Document frequency analysis\n", "doc_freq = np.array((X_features > 0).sum(axis=0)).flatten()\n", "feature_stats['doc_frequency'] = doc_freq\n", "feature_stats['doc_percentage'] = (doc_freq / X_features.shape[0]) * 100\n", "\n", "print(f\"\\n📈 Feature Document Frequency Analysis:\")\n", "print(f\"  • Features in >50% documents: {(feature_stats['doc_percentage'] > 50).sum()}\")\n", "print(f\"  • Features in 10-50% documents: {((feature_stats['doc_percentage'] >= 10) & (feature_stats['doc_percentage'] <= 50)).sum()}\")\n", "print(f\"  • Features in <10% documents: {(feature_stats['doc_percentage'] < 10).sum()}\")\n", "\n", "# Most discriminative features (high TF-IDF, moderate document frequency)\n", "discriminative = feature_stats[\n", "    (feature_stats['doc_percentage'] >= 5) & \n", "    (feature_stats['doc_percentage'] <= 60)\n", "].sort_values('total_tfidf', ascending=False)\n", "\n", "print(f\"\\n🎯 Most Discriminative Features (5-60% doc frequency):\")\n", "for i, row in discriminative.head(15).iterrows():\n", "    print(f\"  {row['feature']:<20} : TF-IDF={row['total_tfidf']:.3f}, Docs={row['doc_percentage']:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Visualization of Feature Characteristics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualizations\n", "fig = make_subplots(\n", "    rows=2, cols=2,\n", "    subplot_titles=('TF-IDF Configuration Comparison', 'Feature Type Distribution',\n", "                   'Top Features by TF-IDF Score', 'Document Frequency Distribution'),\n", "    specs=[[{'type': 'bar'}, {'type': 'pie'}],\n", "           [{'type': 'bar'}, {'type': 'histogram'}]]\n", ")\n", "\n", "# Configuration comparison\n", "config_names = list(vectorizer_configs.keys())\n", "config_features = [vectorizer_configs[name][1].shape[1] for name in config_names]\n", "config_sparsity = []\n", "\n", "for name in config_names:\n", "    X = vectorizer_configs[name][1]\n", "    sparsity = (1 - X.nnz / (X.shape[0] * X.shape[1])) * 100\n", "    config_sparsity.append(sparsity)\n", "\n", "fig.add_trace(\n", "    go.Bar(x=config_names, y=config_features, name='Features', \n", "           marker_color='lightblue'),\n", "    row=1, col=1\n", ")\n", "\n", "# Feature type distribution\n", "type_counts = [len(unigrams), len(bigrams), len(trigrams)]\n", "type_labels = ['Unigrams', 'Bigrams', 'Trigrams']\n", "\n", "fig.add_trace(\n", "    go.Pie(labels=type_labels, values=type_counts, name='Feature Types'),\n", "    row=1, col=2\n", ")\n", "\n", "# Top features\n", "top_features = feature_stats.head(15)\n", "fig.add_trace(\n", "    go.Bar(x=top_features['total_tfidf'], y=top_features['feature'], \n", "           orientation='h', name='TF-IDF Score', marker_color='lightgreen'),\n", "    row=2, col=1\n", ")\n", "\n", "# Document frequency distribution\n", "fig.add_trace(\n", "    go.Histogram(x=feature_stats['doc_percentage'], nbinsx=30, \n", "                name='Doc Frequency', marker_color='lightcoral'),\n", "    row=2, col=2\n", ")\n", "\n", "fig.update_layout(height=800, showlegend=False, \n", "                 title_text=\"📊 Feature Extraction Analysis\")\n", "fig.show()\n", "\n", "# Feature correlation analysis (sample)\n", "if X_features.shape[1] > 10:  # Only if we have enough features\n", "    print(f\"\\n🔗 Feature Correlation Analysis (Top 20 features):\")\n", "    \n", "    # Get top 20 features for correlation analysis\n", "    top_20_indices = feature_stats.head(20).index\n", "    X_top20 = X_features[:, top_20_indices].toarray()\n", "    top_20_names = feature_stats.head(20)['feature'].tolist()\n", "    \n", "    # Calculate correlation matrix\n", "    corr_matrix = np.corrcoef(X_top20.T)\n", "    \n", "    # Find highly correlated features\n", "    high_corr_pairs = []\n", "    for i in range(len(top_20_names)):\n", "        for j in range(i+1, len(top_20_names)):\n", "            if abs(corr_matrix[i, j]) > 0.5:\n", "                high_corr_pairs.append((\n", "                    top_20_names[i], \n", "                    top_20_names[j], \n", "                    corr_matrix[i, j]\n", "                ))\n", "    \n", "    if high_corr_pairs:\n", "        print(f\"  • High correlation pairs (|r| > 0.5):\")\n", "        for feat1, feat2, corr in sorted(high_corr_pairs, key=lambda x: abs(x[2]), reverse=True)[:10]:\n", "            print(f\"    '{feat1}' ↔ '{feat2}' : r = {corr:.3f}\")\n", "    else:\n", "        print(f\"  • No high correlations found among top features\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Dimensionality Reduction & Feature Selection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply dimensionality reduction techniques\n", "print(\"🎯 DIMENSIONALITY REDUCTION & FEATURE SELECTION\")\n", "print(\"=\" * 60)\n", "\n", "# Prepare target variable if available\n", "if target_column and target_column in df.columns:\n", "    # Create sentiment labels for feature selection\n", "    def create_sentiment_label(rating):\n", "        if rating <= 2:\n", "            return 0  # Negative\n", "        elif rating >= 4:\n", "            return 1  # Positive\n", "        else:\n", "            return 2  # Neutral\n", "    \n", "    y = df[target_column].apply(create_sentiment_label)\n", "    \n", "    # Remove neutral samples for binary classification\n", "    binary_mask = y != 2\n", "    X_binary = X_features[binary_mask]\n", "    y_binary = y[binary_mask]\n", "    \n", "    print(f\"\\n📊 Target Variable Analysis:\")\n", "    print(f\"  • Total samples: {len(y)}\")\n", "    print(f\"  • Negative (0): {(y == 0).sum()}\")\n", "    print(f\"  • Positive (1): {(y == 1).sum()}\")\n", "    print(f\"  • Neutral (2): {(y == 2).sum()}\")\n", "    print(f\"  • Binary classification samples: {len(y_binary)}\")\n", "    \n", "    # Chi-square feature selection\n", "    if len(y_binary) > 0:\n", "        print(f\"\\n🔍 CHI-SQUARE FEATURE SELECTION:\")\n", "        \n", "        # Select top 1000 features using chi-square\n", "        k_best = min(1000, X_binary.shape[1])\n", "        chi2_selector = SelectKBest(chi2, k=k_best)\n", "        X_chi2 = chi2_selector.fit_transform(X_binary, y_binary)\n", "        \n", "        # Get selected feature names\n", "        selected_features = chi2_selector.get_support()\n", "        selected_feature_names = [feature_names[i] for i in range(len(feature_names)) if selected_features[i]]\n", "        \n", "        print(f\"  • Selected features: {X_chi2.shape[1]} / {X_binary.shape[1]}\")\n", "        print(f\"  • Reduction: {(1 - X_chi2.shape[1]/X_binary.shape[1])*100:.1f}%\")\n", "        \n", "        # Get chi-square scores\n", "        chi2_scores = chi2_selector.scores_[selected_features]\n", "        chi2_features = pd.DataFrame({\n", "            'feature': selected_feature_names,\n", "            'chi2_score': chi2_scores\n", "        }).sort_values('chi2_score', ascending=False)\n", "        \n", "        print(f\"\\n🔝 Top 15 Features by Chi-square Score:\")\n", "        for i, row in chi2_features.head(15).iterrows():\n", "            print(f\"  {row['feature']:<20} : {row['chi2_score']:.2f}\")\n", "        \n", "        # Mutual information feature selection\n", "        print(f\"\\n🧠 MUTUAL INFORMATION FEATURE SELECTION:\")\n", "        \n", "        mi_selector = SelectKBest(mutual_info_classif, k=k_best)\n", "        X_mi = mi_selector.fit_transform(X_binary, y_binary)\n", "        \n", "        mi_selected_features = mi_selector.get_support()\n", "        mi_selected_names = [feature_names[i] for i in range(len(feature_names)) if mi_selected_features[i]]\n", "        \n", "        print(f\"  • Selected features: {X_mi.shape[1]} / {X_binary.shape[1]}\")\n", "        \n", "        # Get mutual information scores\n", "        mi_scores = mi_selector.scores_[mi_selected_features]\n", "        mi_features = pd.DataFrame({\n", "            'feature': mi_selected_names,\n", "            'mi_score': mi_scores\n", "        }).sort_values('mi_score', ascending=False)\n", "        \n", "        print(f\"\\n🔝 Top 15 Features by Mutual Information:\")\n", "        for i, row in mi_features.head(15).iterrows():\n", "            print(f\"  {row['feature']:<20} : {row['mi_score']:.4f}\")\n", "        \n", "        # Feature selection comparison\n", "        chi2_set = set(selected_feature_names)\n", "        mi_set = set(mi_selected_names)\n", "        overlap = chi2_set & mi_set\n", "        \n", "        print(f\"\\n🔄 Feature Selection Comparison:\")\n", "        print(f\"  • Chi-square selected: {len(chi2_set)}\")\n", "        print(f\"  • Mutual info selected: {len(mi_set)}\")\n", "        print(f\"  • Overlap: {len(overlap)} ({len(overlap)/min(len(chi2_set), len(mi_set))*100:.1f}%)\")\n", "        \n", "else:\n", "    print(f\"\\n⚠️ No target variable available for supervised feature selection\")\n", "    X_chi2 = X_features\n", "    X_mi = X_features\n", "    selected_feature_names = feature_names.tolist()\n", "\n", "# Truncated SVD for dimensionality reduction\n", "print(f\"\\n📉 TRUNCATED SVD DIMENSIONALITY REDUCTION:\")\n", "\n", "# Apply SVD with different components\n", "svd_components = [50, 100, 200, 300]\n", "svd_results = {}\n", "\n", "for n_comp in svd_components:\n", "    if n_comp < X_features.shape[1]:\n", "        svd = TruncatedSVD(n_components=n_comp, random_state=42)\n", "        X_svd = svd.fit_transform(X_features)\n", "        \n", "        explained_variance = svd.explained_variance_ratio_.sum()\n", "        svd_results[n_comp] = (svd, X_svd, explained_variance)\n", "        \n", "        print(f\"  • {n_comp} components: {explained_variance:.3f} variance explained\")\n", "\n", "# Choose optimal SVD configuration\n", "if svd_results:\n", "    # Choose configuration that explains >80% variance or best available\n", "    best_svd = None\n", "    for n_comp, (svd, X_svd, var_exp) in svd_results.items():\n", "        if var_exp >= 0.8 or best_svd is None:\n", "            best_svd = (n_comp, svd, X_svd, var_exp)\n", "        if var_exp >= 0.8:\n", "            break\n", "    \n", "    if best_svd:\n", "        n_comp, svd_model, X_svd_best, var_exp = best_svd\n", "        print(f\"\\n✅ Optimal SVD: {n_comp} components ({var_exp:.3f} variance)\")\n", "else:\n", "    print(f\"\\n⚠️ SVD not applicable (too few features)\")\n", "    X_svd_best = X_features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save Feature Extraction Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all feature extraction results\n", "print(\"💾 SAVING FEATURE EXTRACTION RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Create results directory\n", "import os\n", "os.makedirs('data/features', exist_ok=True)\n", "os.makedirs('models/vectorizers', exist_ok=True)\n", "\n", "# Save vectorizers\n", "print(f\"\\n🔧 Saving vectorizers:\")\n", "for name, (vectorizer_obj, _) in vectorizer_configs.items():\n", "    filename = f'models/vectorizers/tfidf_{name}.pkl'\n", "    joblib.dump(vectorizer_obj, filename)\n", "    print(f\"  • {filename}\")\n", "\n", "# Save feature matrices (sparse format)\n", "print(f\"\\n📊 Saving feature matrices:\")\n", "for name, (_, X_matrix) in vectorizer_configs.items():\n", "    filename = f'data/features/X_{name}.pkl'\n", "    joblib.dump(X_matrix, filename)\n", "    print(f\"  • {filename} - Shape: {X_matrix.shape}\")\n", "\n", "# Save feature selection results if available\n", "if 'X_chi2' in locals() and target_column:\n", "    print(f\"\\n🎯 Saving feature selection results:\")\n", "    joblib.dump(X_chi2, 'data/features/X_chi2_selected.pkl')\n", "    joblib.dump(X_mi, 'data/features/X_mi_selected.pkl')\n", "    joblib.dump(chi2_selector, 'models/vectorizers/chi2_selector.pkl')\n", "    joblib.dump(mi_selector, 'models/vectorizers/mi_selector.pkl')\n", "    print(f\"  • Chi-square selected features: {X_chi2.shape}\")\n", "    print(f\"  • Mutual info selected features: {X_mi.shape}\")\n", "\n", "# Save SVD results if available\n", "if 'X_svd_best' in locals() and svd_results:\n", "    print(f\"\\n📉 Saving SVD results:\")\n", "    joblib.dump(X_svd_best, 'data/features/X_svd_reduced.pkl')\n", "    joblib.dump(svd_model, 'models/vectorizers/svd_reducer.pkl')\n", "    print(f\"  • SVD reduced features: {X_svd_best.shape}\")\n", "\n", "# Save feature statistics\n", "print(f\"\\n📈 Saving feature statistics:\")\n", "feature_stats.to_excel('data/features/feature_statistics.xlsx', index=False)\n", "print(f\"  • Feature statistics: data/features/feature_statistics.xlsx\")\n", "\n", "if 'chi2_features' in locals():\n", "    chi2_features.to_excel('data/features/chi2_feature_scores.xlsx', index=False)\n", "    print(f\"  • Chi-square scores: data/features/chi2_feature_scores.xlsx\")\n", "\n", "if 'mi_features' in locals():\n", "    mi_features.to_excel('data/features/mi_feature_scores.xlsx', index=False)\n", "    print(f\"  • Mutual info scores: data/features/mi_feature_scores.xlsx\")\n", "\n", "# Create feature extraction summary\n", "summary = {\n", "    'extraction_date': datetime.now().isoformat(),\n", "    'dataset_size': len(df),\n", "    'text_column': text_column,\n", "    'target_column': target_column,\n", "    'vectorizer_configs': {\n", "        name: {\n", "            'features': X_matrix.shape[1],\n", "            'sparsity': (1 - X_matrix.nnz / (X_matrix.shape[0] * X_matrix.shape[1])) * 100\n", "        } for name, (_, X_matrix) in vectorizer_configs.items()\n", "    },\n", "    'recommended_config': 'optimized',\n", "    'feature_selection_applied': target_column is not None,\n", "    'dimensionality_reduction_applied': 'svd_results' in locals() and bool(svd_results)\n", "}\n", "\n", "import json\n", "with open('data/features/extraction_summary.json', 'w') as f:\n", "    json.dump(summary, f, indent=2)\n", "\n", "print(f\"\\n📋 Extraction summary: data/features/extraction_summary.json\")\n", "\n", "# Save utility functions\n", "feature_utils_code = '''\n", "import joblib\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "def load_vectorizer(config_name='optimized'):\n", "    \"\"\"Load a saved TF-IDF vectorizer\"\"\"\n", "    filename = f'models/vectorizers/tfidf_{config_name}.pkl'\n", "    return joblib.load(filename)\n", "\n", "def load_feature_matrix(config_name='optimized'):\n", "    \"\"\"Load a saved feature matrix\"\"\"\n", "    filename = f'data/features/X_{config_name}.pkl'\n", "    return joblib.load(filename)\n", "\n", "def transform_new_text(texts, vectorizer):\n", "    \"\"\"Transform new texts using a fitted vectorizer\"\"\"\n", "    if isinstance(texts, str):\n", "        texts = [texts]\n", "    return vectorizer.transform(texts)\n", "\n", "def get_feature_names(vectorizer):\n", "    \"\"\"Get feature names from vectorizer\"\"\"\n", "    return vectorizer.get_feature_names_out()\n", "\n", "def analyze_text_features(text, vectorizer, top_n=10):\n", "    \"\"\"Analyze top features for a given text\"\"\"\n", "    X = vectorizer.transform([text])\n", "    feature_names = vectorizer.get_feature_names_out()\n", "    \n", "    # Get non-zero features\n", "    nonzero_features = X.nonzero()[1]\n", "    feature_scores = [(feature_names[i], X[0, i]) for i in nonzero_features]\n", "    \n", "    # Sort by score\n", "    feature_scores.sort(key=lambda x: x[1], reverse=True)\n", "    \n", "    return feature_scores[:top_n]\n", "'''\n", "\n", "with open('utils/feature_extraction.py', 'w', encoding='utf-8') as f:\n", "    f.write(feature_utils_code)\n", "\n", "print(f\"\\n🔧 Utility functions: utils/feature_extraction.py\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 FEATURE EXTRACTION SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n⚙️ FEATURE EXTRACTION COMPLETED:\")\n", "print(f\"  • Dataset size: {len(df)} documents\")\n", "print(f\"  • Text column: {text_column}\")\n", "print(f\"  • Target column: {target_column}\")\n", "\n", "print(f\"\\n📊 VECTORIZATION CONFIGURATIONS:\")\n", "for name, (vectorizer_obj, X_matrix) in vectorizer_configs.items():\n", "    sparsity = (1 - X_matrix.nnz / (X_matrix.shape[0] * X_matrix.shape[1])) * 100\n", "    print(f\"  • {name.capitalize():<12}: {X_matrix.shape[1]:>5} features, {sparsity:>5.1f}% sparse\")\n", "\n", "print(f\"\\n🎯 RECOMMENDED CONFIGURATION: Optimized TF-IDF\")\n", "print(f\"  • Features: {X_optimized.shape[1]}\")\n", "print(f\"  • N-gram range: (1, 2)\")\n", "print(f\"  • Min document frequency: 3\")\n", "print(f\"  • Max document frequency: 70%\")\n", "print(f\"  • Sublinear TF scaling: Yes\")\n", "\n", "if target_column and 'X_chi2' in locals():\n", "    print(f\"\\n🔍 FEATURE SELECTION RESULTS:\")\n", "    print(f\"  • Chi-square selected: {X_chi2.shape[1]} features\")\n", "    print(f\"  • Mutual info selected: {X_mi.shape[1]} features\")\n", "    print(f\"  • Selection overlap: {len(overlap)} features\")\n", "\n", "if 'svd_results' in locals() and svd_results:\n", "    print(f\"\\n📉 DIMENSIONALITY REDUCTION:\")\n", "    print(f\"  • SVD components: {n_comp}\")\n", "    print(f\"  • <PERSON><PERSON><PERSON> explained: {var_exp:.3f}\")\n", "    print(f\"  • Dimension reduction: {X_features.shape[1]} → {X_svd_best.shape[1]}\")\n", "\n", "print(f\"\\n💾 FILES GENERATED:\")\n", "print(f\"  📁 models/vectorizers/ - TF-IDF vectorizers (.pkl)\")\n", "print(f\"  📁 data/features/ - Feature matrices (.pkl)\")\n", "print(f\"  📊 data/features/feature_statistics.xlsx\")\n", "print(f\"  📋 data/features/extraction_summary.json\")\n", "print(f\"  🔧 utils/feature_extraction.py\")\n", "\n", "print(f\"\\n📈 FEATURE QUALITY METRICS:\")\n", "print(f\"  • Vocabulary size: {len(unique_words):,} words\")\n", "print(f\"  • N-gram features: {len(unique_words) + len(unique_bigrams):,}\")\n", "print(f\"  • Average document length: {np.mean(text_lengths):.1f} words\")\n", "print(f\"  • Feature sparsity: {(1 - X_optimized.nnz / (X_optimized.shape[0] * X_optimized.shape[1]))*100:.1f}%\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. Score-based sentiment labeling (05_score_based_labeling.ipynb)\")\n", "print(f\"  2. Lexicon-based sentiment labeling (06_lexicon_based_labeling.ipynb)\")\n", "print(f\"  3. Random Forest model training (07_random_forest_models.ipynb)\")\n", "print(f\"  4. XGBoost model training (08_xgboost_models.ipynb)\")\n", "\n", "print(f\"\\n✅ FEATURE EXTRACTION COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for sentiment labeling phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}