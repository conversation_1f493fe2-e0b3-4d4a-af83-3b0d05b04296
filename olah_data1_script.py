# Olah Data 1 Script - Converted from Jupyter Notebook
# Analisis <PERSON>timen untuk Data GoBiz - Machine Learning

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import joblib
import warnings
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

print("Starting Olah Data 1 script...")
print("🔄 Importing libraries and setting up environment...")

# ==============================================================================
# SEL 1: MEMUAT DATA YANG SUDAH DIPREPROSES
# ==============================================================================

print("\n🔄 Loading preprocessed data...")

# Coba muat data yang sudah dipreproses
file_path = 'gofood_ulasan_preprocessed.xlsx'

try:
    df = pd.read_excel(file_path)
    print(f"✅ Data successfully loaded from: {file_path}")
    print(f"Data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    # Display first few rows
    print("\n--- First 5 rows of preprocessed data ---")
    print(df.head())
    
    # Check for required columns
    required_columns = ['ulasan_stemmed', 'nilai']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ Missing required columns: {missing_columns}")
        df = pd.DataFrame()
    else:
        print("✅ All required columns found.")
        
except FileNotFoundError:
    print(f"❌ Error: File '{file_path}' not found.")
    print("Please run the preprocessing script first.")
    df = pd.DataFrame()
except Exception as e:
    print(f"❌ Error loading data: {e}")
    df = pd.DataFrame()

# ==============================================================================
# SEL 2: PELABELAN BERBASIS RATING & TRANSFORMASI LABEL
# ==============================================================================

if not df.empty:
    print("\n🔄 Creating sentiment labels based on star ratings...")
    
    def create_sentiment_label(nilai):
        if nilai in [1, 2]:
            return 'Negatif'
        elif nilai in [4, 5]:
            return 'Positif'
        # Rating 3 will be ignored (return None)
        return None

    df['sentimen_kategorikal'] = df['nilai'].apply(create_sentiment_label)

    # Remove reviews with neutral sentiment (rating 3)
    df.dropna(subset=['sentimen_kategorikal'], inplace=True)
    print("Sentiment distribution after removing rating 3 (Neutral):")
    print(df['sentimen_kategorikal'].value_counts())

    # Convert categorical labels ('Positif', 'Negatif') to numeric (0, 1)
    print("\n🔄 Converting labels to numeric format...")
    le = LabelEncoder()
    df['sentimen'] = le.fit_transform(df['sentimen_kategorikal'])

    print("Label Mapping:")
    for index, label in enumerate(le.classes_):
        print(f"{label} -> {index}")

    print("\n✅ Data labeling completed.")
else:
    print("⚠️ Data not loaded. Process skipped.")

# ==============================================================================
# SEL 3: EKSTRAKSI FITUR (TF-IDF) & PEMBAGIAN DATA
# ==============================================================================

if not df.empty:
    X = df['ulasan_stemmed'].fillna('')
    y = df['sentimen']

    print("\n🔄 Converting text to numerical vectors with improved TF-IDF...")

    # Improved TF-IDF parameters
    vectorizer = TfidfVectorizer(
        max_features=3000,    # Reduce maximum features
        min_df=2,             # A word must appear in at least 2 reviews to be considered a feature
        ngram_range=(1, 2)    # Consider word pairs (bigrams) as well, e.g., "tidak bagus"
    )

    X_tfidf = vectorizer.fit_transform(X).toarray()
    joblib.dump(vectorizer, 'tfidf_vectorizer_tuned.pkl')
    print("Improved TF-IDF vectorizer has been saved.")

    print("\n🔄 Splitting data into 80% training and 20% testing...")
    X_train, X_test, y_train, y_test = train_test_split(
        X_tfidf, y, test_size=0.2, random_state=42, stratify=y
    )

    print(f"Training set size: {X_train.shape}")
    print(f"Testing set size: {X_test.shape}")
    print("\n✅ Feature extraction and data splitting completed.")
else:
    print("⚠️ Data not loaded. Process skipped.")

# ==============================================================================
# SEL 4: PELATIHAN & EVALUASI MODEL - RANDOM FOREST
# ==============================================================================

if 'X_train' in locals():
    print("\n🌳 Starting Random Forest Model Training...")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    rf_model.fit(X_train, y_train)
    print("Random Forest model successfully trained.")

    joblib.dump(rf_model, 'model_random_forest.pkl')
    print("Random Forest model saved.")

    print("\n🔬 Evaluating model performance on test data...")
    y_pred_rf = rf_model.predict(X_test)

    print("\nRandom Forest Evaluation Results:")
    print(f"Accuracy: {accuracy_score(y_test, y_pred_rf) * 100:.2f}%")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred_rf, target_names=le.classes_))
    
    # Confusion Matrix
    print("\nConfusion Matrix:")
    cm = confusion_matrix(y_test, y_pred_rf)
    print(cm)
    
else:
    print("⚠️ Training data not found.")

# ==============================================================================
# SEL 5: PELATIHAN MODEL LOGISTIC REGRESSION
# ==============================================================================

if 'X_train' in locals():
    print("\n📊 Starting Logistic Regression Model Training...")
    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train, y_train)
    print("Logistic Regression model successfully trained.")

    joblib.dump(lr_model, 'model_logistic_regression.pkl')
    print("Logistic Regression model saved.")

    print("\n🔬 Evaluating Logistic Regression performance...")
    y_pred_lr = lr_model.predict(X_test)

    print("\nLogistic Regression Evaluation Results:")
    print(f"Accuracy: {accuracy_score(y_test, y_pred_lr) * 100:.2f}%")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred_lr, target_names=le.classes_))
    
else:
    print("⚠️ Training data not found.")

# ==============================================================================
# SEL 6: PERBANDINGAN MODEL
# ==============================================================================

if 'X_train' in locals():
    print("\n📈 Model Comparison Summary:")
    print("="*50)
    
    # Random Forest
    rf_accuracy = accuracy_score(y_test, y_pred_rf) * 100
    print(f"Random Forest Accuracy: {rf_accuracy:.2f}%")
    
    # Logistic Regression
    lr_accuracy = accuracy_score(y_test, y_pred_lr) * 100
    print(f"Logistic Regression Accuracy: {lr_accuracy:.2f}%")
    
    # Determine best model
    if rf_accuracy > lr_accuracy:
        print(f"\n🏆 Best Model: Random Forest ({rf_accuracy:.2f}%)")
        best_model = rf_model
        best_model_name = "Random Forest"
    else:
        print(f"\n🏆 Best Model: Logistic Regression ({lr_accuracy:.2f}%)")
        best_model = lr_model
        best_model_name = "Logistic Regression"
    
    # Save best model
    joblib.dump(best_model, 'best_model.pkl')
    print(f"Best model ({best_model_name}) saved as 'best_model.pkl'")
    
else:
    print("⚠️ Training data not found.")

print("\n🎉 ALL MACHINE LEARNING PROCESSES COMPLETED!")
print("Models have been trained and saved for sentiment analysis.")
