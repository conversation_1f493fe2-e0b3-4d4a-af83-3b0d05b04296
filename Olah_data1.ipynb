# ==============================================================================
# SEL 1: INSTALASI, IMPOR, DAN MUAT DATA
# ==============================================================================
# Instalasi library yang dibutuhkan
print("⚙️ Menginstal library 'openpyxl' dan 'xgboost'...")
!pip install openpyxl xgboost -q

# Impor semua library
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import LabelEncoder

print("\n✅ Semua library siap digunakan.")

# --- Memuat file Excel Anda ---
file_path = 'gofood_ulasan_preprocessed.xlsx'
try:
    print(f"\n🔄 Memuat data dari '{file_path}'...")
    df = pd.read_excel(file_path, engine='openpyxl')
    df.dropna(subset=['ulasan_stemmed'], inplace=True)
    print("✅ Data berhasil dimuat.")
    print("Contoh data:")
    print(df[['ulasan', 'ulasan_stemmed', 'nilai']].head())

except FileNotFoundError:
    print(f"❌ Error: File '{file_path}' tidak ditemukan.")
    df = pd.DataFrame()
except Exception as e:
    print(f"❌ Terjadi error saat memuat data: {e}")
    df = pd.DataFrame()

# ==============================================================================
# SEL 2: PELABELAN BERBASIS RATING & TRANSFORMASI LABEL
# ==============================================================================

if not df.empty:
    # 1. Membuat label sentimen kategorikal berdasarkan nilai rating
    print("🔄 Membuat label sentimen berdasarkan rating bintang...")
    def create_sentiment_label(nilai):
        if nilai in [1, 2]:
            return 'Negatif'
        elif nilai in [4, 5]:
            return 'Positif'
        # Nilai 3 akan diabaikan (return None)
        return None

    df['sentimen_kategorikal'] = df['nilai'].apply(create_sentiment_label)

    # 2. Menghapus ulasan dengan sentimen netral (rating 3)
    df.dropna(subset=['sentimen_kategorikal'], inplace=True)
    print("Distribusi sentimen setelah menghapus rating 3 (Netral):")
    print(df['sentimen_kategorikal'].value_counts())

    # 3. Mengubah label kategorikal ('Positif', 'Negatif') menjadi numerik (0, 1)
    print("\n🔄 Mengubah label menjadi format numerik...")
    le = LabelEncoder()
    df['sentimen'] = le.fit_transform(df['sentimen_kategorikal'])

    print("Mapping Label:")
    for index, label in enumerate(le.classes_):
        print(f"{label} -> {index}")

    print("\n✅ Pelabelan data selesai.")
else:
    print("⚠️ Data tidak dimuat. Proses dilewati.")

# ==============================================================================
# SEL 3: EKSTRAKSI FITUR (TF-IDF) & PEMBAGIAN DATA (DENGAN FEATURE TUNING)
# ==============================================================================
if not df.empty:
    X = df['ulasan_stemmed'].fillna('')
    y = df['sentimen']

    print("🔄 Mengubah teks menjadi vektor numerik dengan TF-IDF yang disempurnakan...")

    # --- PERUBAHAN DI SINI ---
    vectorizer = TfidfVectorizer(
        max_features=3000,  # Kurangi jumlah fitur maksimal
        min_df=5,           # Sebuah kata harus muncul di minimal 5 ulasan untuk dianggap fitur
        ngram_range=(1, 2)    # Pertimbangkan juga pasangan kata (bigram), misal: "tidak bagus"
    )
    # --- Akhir Perubahan ---

    X_tfidf = vectorizer.fit_transform(X).toarray()
    joblib.dump(vectorizer, 'tfidf_vectorizer_tuned.pkl')
    print("Vectorizer TF-IDF yang disempurnakan telah disimpan.")

    print("\n🔄 Membagi data menjadi 80% data latih dan 20% data uji...")
    X_train, X_test, y_train, y_test = train_test_split(
        X_tfidf, y, test_size=0.2, random_state=42, stratify=y
    )

    print("\n✅ Ekstraksi fitur dan pembagian data selesai.")
else:
    print("⚠️ Data tidak dimuat. Proses dilewati.")

# ==============================================================================
# SEL 4: PELATIHAN & EVALUASI MODEL - RANDOM FOREST
# ==============================================================================
if 'X_train' in locals():
    print("🌳 Memulai Pelatihan Model Random Forest...")
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    rf_model.fit(X_train, y_train)
    print("Model Random Forest berhasil dilatih.")

    joblib.dump(rf_model, 'model_random_forest.pkl')

    print("\n🔬 Mengevaluasi kinerja model pada data uji...")
    y_pred_rf = rf_model.predict(X_test)

    print("\nHasil Evaluasi Random Forest:")
    print(f"Akurasi: {accuracy_score(y_test, y_pred_rf) * 100:.2f}%")
    print("\nLaporan Klasifikasi:")
    print(classification_report(y_test, y_pred_rf, target_names=le.classes_))
else:
    print("⚠️ Data latih tidak ditemukan.")

# ==============================================================================
# SEL 4 (VERSI CEPAT): OPTIMASI RANDOM FOREST DENGAN 20 TOTAL MODEL
# ==============================================================================
from sklearn.model_selection import RandomizedSearchCV

# Pastikan data latih (X_train) sudah ada dari sel sebelumnya
if 'X_train' in locals():
    print("🌳 Memulai Pencarian Hyperparameter (Versi Cepat) untuk Random Forest...")

    # 1. Tentukan rentang parameter yang sama seperti sebelumnya
    param_dist_rf = {
        'n_estimators': [100, 150, 200, 300],
        'max_depth': [20, 40, 60, 80, 100, None],
        'min_samples_leaf': [2, 5, 10],
        'min_samples_split': [5, 10, 15],
        'max_features': ['sqrt', 0.5]
    }

    # --- PERUBAHAN UTAMA DI SINI ---
    # 2. Inisialisasi RandomizedSearchCV dengan parameter yang lebih ringan
    # n_iter=10 dan cv=2 akan menghasilkan total 10 x 2 = 20 model untuk dilatih
    rf_base = RandomForestClassifier(random_state=42, n_jobs=-1)
    random_search_rf = RandomizedSearchCV(
        estimator=rf_base,
        param_distributions=param_dist_rf,
        n_iter=10,       # Coba 10 kombinasi acak saja
        cv=2,            # Gunakan 2-fold cross-validation
        verbose=2,       # Tetap tampilkan progress
        random_state=42,
        n_jobs=-1
    )
    # --- AKHIR PERUBAHAN ---

    print("Total model yang akan dilatih: 10 kombinasi x 2 validasi = 20 model.")

    # 3. Latih RandomizedSearchCV
    random_search_rf.fit(X_train, y_train)

    # 4. Ambil model terbaik dari hasil pencarian
    rf_model_best = random_search_rf.best_estimator_
    print("\nParameter terbaik yang ditemukan:")
    print(random_search_rf.best_params_)
    print("\nModel Random Forest terbaik berhasil dilatih.")
    joblib.dump(rf_model_best, 'model_random_forest_tuned.pkl')
    print("Model terbaik telah disimpan.")

    # 5. Evaluasi dan Cek Overfitting pada model TERBAIK
    print("\n🔬 Mengevaluasi kinerja model TERBAIK (Random Forest)...")
    y_pred_rf_best = rf_model_best.predict(X_test)
    test_accuracy_rf = accuracy_score(y_test, y_pred_rf_best)

    y_pred_train_rf_best = rf_model_best.predict(X_train)
    train_accuracy_rf = accuracy_score(y_train, y_pred_train_rf_best)

    print("\nHasil Evaluasi Random Forest (Setelah Tuning):")
    print(f"Akurasi pada Data Latih: {train_accuracy_rf * 100:.2f}%")
    print(f"Akurasi pada Data Uji:   {test_accuracy_rf * 100:.2f}%")
    print(f"Selisih Overfitting:      {(train_accuracy_rf - test_accuracy_rf) * 100:.2f}%")

    print("\nLaporan Klasifikasi:")
    print(classification_report(y_test, y_pred_rf_best, target_names=le.classes_))
else:
    print("⚠️ Data latih tidak ditemukan. Jalankan Sel 3 terlebih dahulu.")

# ==============================================================================
# SEL 5: PELATIHAN & EVALUASI MODEL - XGBOOST
# ==============================================================================
if 'X_train' in locals():
    print("🚀 Memulai Pelatihan Model XGBoost...")
    xgb_model = XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)
    xgb_model.fit(X_train, y_train)
    print("Model XGBoost berhasil dilatih.")

    joblib.dump(xgb_model, 'model_xgboost.pkl')

    print("\n🔬 Mengevaluasi kinerja model pada data uji...")
    y_pred_xgb = xgb_model.predict(X_test)

    print("\nHasil Evaluasi XGBoost:")
    print(f"Akurasi: {accuracy_score(y_test, y_pred_xgb) * 100:.2f}%")
    print("\nLaporan Klasifikasi:")
    print(classification_report(y_test, y_pred_xgb, target_names=le.classes_))
else:
    print("⚠️ Data latih tidak ditemukan.")

# ==============================================================================
# SEL OPTIMASI HYPERPARAMETER XGBOOST (DENGAN PERBAIKAN)
# ==============================================================================
from sklearn.model_selection import RandomizedSearchCV

# Pastikan data latih (X_train) sudah ada dari sel sebelumnya
if 'X_train' in locals():
    print("🚀 Memulai Pencarian Hyperparameter Terbaik untuk XGBoost...")

    # 1. Tentukan rentang parameter yang ingin diuji
    param_dist = {
        'n_estimators': [100, 200, 300],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [5, 10, 20, 30],
        'subsample': [0.7, 0.8, 0.9],
        'colsample_bytree': [0.7, 0.8, 0.9],
        'gamma': [0, 1, 5]
    }

    # 2. Inisialisasi model dasar dan RandomizedSearchCV
    xgb_base = XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)
    random_search = RandomizedSearchCV(
        estimator=xgb_base,
        param_distributions=param_dist,
        n_iter=25,
        cv=3,
        verbose=1,
        random_state=42,
        n_jobs=-1
    )

    # 3. Latih RandomizedSearchCV
    random_search.fit(X_train, y_train)

    # 4. Ambil model terbaik
    xgb_model_best = random_search.best_estimator_
    print("\nParameter terbaik yang ditemukan:")
    print(random_search.best_params_)
    print("\nModel XGBoost terbaik berhasil dilatih.")
    joblib.dump(xgb_model_best, 'model_xgboost_tuned.pkl')
    print("Model terbaik telah disimpan.")

    # 5. Evaluasi dan Cek Overfitting pada model TERBAIK
    print("\n🔬 Mengevaluasi kinerja model TERBAIK...")
    y_pred_xgb_best = xgb_model_best.predict(X_test)
    test_accuracy = accuracy_score(y_test, y_pred_xgb_best)

    y_pred_train_xgb_best = xgb_model_best.predict(X_train)
    train_accuracy = accuracy_score(y_train, y_pred_train_xgb_best)

    print("\nHasil Evaluasi XGBoost (Setelah Tuning):")
    print(f"Akurasi pada Data Latih: {train_accuracy * 100:.2f}%")
    print(f"Akurasi pada Data Uji:   {test_accuracy * 100:.2f}%")

    print("\nLaporan Klasifikasi:")
    print(classification_report(y_test, y_pred_xgb_best, target_names=le.classes_))

    # --- KODE YANG DITAMBAHKAN ---
    print("\n📊 Matriks Kebingungan (Confusion Matrix):")
    cm_xgb_best = confusion_matrix(y_test, y_pred_xgb_best)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm_xgb_best, annot=True, fmt='d', cmap='Blues',
                xticklabels=le.classes_, yticklabels=le.classes_)
    plt.xlabel('Prediksi Model')
    plt.ylabel('Label Aktual')
    plt.title('Confusion Matrix - XGBoost (Setelah Tuning)')
    plt.show()
    # --- AKHIR KODE TAMBAHAN ---

else:
    print("⚠️ Data latih tidak ditemukan. Jalankan sel sebelumnya terlebih dahulu.")

# ==============================================================================
# SEL UNTUK CEK OVERFITTING PADA MODEL TERBAIK (SETELAH TUNING)
# ==============================================================================

# Pastikan model terbaik sudah ada dari sel sebelumnya
# Ganti 'xgb_model_best' dengan nama variabel model terbaik Anda jika berbeda
# (misalnya 'rf_model_best' atau 'random_search.best_estimator_')
if 'xgb_model_best' in locals():
    print("🔬 Memeriksa Overfitting pada Model Terbaik...")

    # 1. Lakukan prediksi pada DATA LATIH
    y_pred_train = xgb_model_best.predict(X_train)
    train_accuracy = accuracy_score(y_train, y_pred_train)

    # 2. Lakukan prediksi pada DATA UJI
    y_pred_test = xgb_model_best.predict(X_test)
    test_accuracy = accuracy_score(y_test, y_pred_test)

    # 3. Tampilkan perbandingan
    print("\n--- Perbandingan Akurasi ---")
    print(f"Akurasi pada Data Latih: {train_accuracy * 100:.2f}%")
    print(f"Akurasi pada Data Uji:   {test_accuracy * 100:.2f}%")

    # 4. Berikan interpretasi
    selisih = train_accuracy - test_accuracy
    print(f"\nSelisih Performa: {selisih * 100:.2f}%")

    if selisih > 0.05: # Batas wajar selisih adalah sekitar 5%
        print("Kesimpulan: Terdapat indikasi overfitting, meskipun mungkin sudah berkurang.")
    else:
        print("Kesimpulan: ✅ Model memiliki generalisasi yang baik (tidak ada indikasi overfitting yang kuat).")

else:
    print("⚠️ Variabel model terbaik (misal: 'xgb_model_best') tidak ditemukan.")
    print("Pastikan Anda sudah menjalankan sel optimasi hyperparameter terlebih dahulu.")