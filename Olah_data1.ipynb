{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 26010, "status": "ok", "timestamp": 1753016786156, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "89zQBSqK7d49", "outputId": "3f9164c4-2037-4b92-ec45-9cd0ccb02be1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["⚙️ Menginstal library 'openpyxl' dan 'xgboost'...\n", "\n", "✅ Semua library siap digunakan.\n", "\n", "🔄 Memuat data dari 'gobiz_ulasan_preprocessed.xlsx'...\n", "✅ Data berhasil dimuat.\n", "Contoh data:\n", "                                              ulasan  \\\n", "0  Smntra bintang 2 aj msih pemula nanti kalau hs...   \n", "1  Makin kesini makin ga jela<PERSON>,lama nunggu instru...   \n", "2       <PERSON><PERSON><PERSON>,beg<PERSON><PERSON> aja,<PERSON> per<PERSON><PERSON>....jd Maless   \n", "3  <PERSON><PERSON> kalo buat promosi yang jelas konsep itu...   \n", "4  Makin di update makin jelek, gada notif tau² d...   \n", "\n", "                                      ulasan_stemmed  nilai  \n", "0  sm<PERSON>ra bintang aj msih mula hsilnya puas tamba...      2  \n", "1   kesini jelaslama nunggu instruksinyasistem kacau      1  \n", "2             stagnan<PERSON><PERSON><PERSON> a<PERSON> maless      2  \n", "3  tolong promosi konsep itung<PERSON>a maasak beban r...      3  \n", "4  update j<PERSON>k gada notif tau driver dtg ambil s...      1  \n"]}], "source": ["# ==============================================================================\n", "# SEL 1: INSTALASI, IMPOR, DAN MUAT DATA\n", "# ==============================================================================\n", "# Instalasi library yang dibutuhkan\n", "print(\"⚙️ Menginstal library 'openpyxl' dan 'xgboost'...\")\n", "!pip install openpyxl xgboost -q\n", "\n", "# Impor semua library\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import joblib\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "from xgboost import XGBClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "print(\"\\n✅ Semua library siap digunakan.\")\n", "\n", "# --- Memuat file Excel Anda ---\n", "file_path = 'gofood_ulasan_preprocessed.xlsx'\n", "try:\n", "    print(f\"\\n🔄 Memuat data dari '{file_path}'...\")\n", "    df = pd.read_excel(file_path, engine='openpyxl')\n", "    df.dropna(subset=['ulasan_stemmed'], inplace=True)\n", "    print(\"✅ Data berhasil dimuat.\")\n", "    print(\"Contoh data:\")\n", "    print(df[['ulasan', 'ulasan_stemmed', 'nilai']].head())\n", "\n", "except FileNotFoundError:\n", "    print(f\"❌ Error: File '{file_path}' tidak ditemukan.\")\n", "    df = pd.DataFrame()\n", "except Exception as e:\n", "    print(f\"❌ Terjadi error saat memuat data: {e}\")\n", "    df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 89, "status": "ok", "timestamp": 1753016786261, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "vfdJTNvq8EpM", "outputId": "971c28f8-cb98-451a-cf9f-2b34a9edb6f4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔄 Membuat label sentimen berdasarkan rating bintang...\n", "Distribusi sentimen setelah menghapus rating 3 (Netral):\n", "sentimen_kategorikal\n", "Positif    7916\n", "Negatif    6559\n", "Name: count, dtype: int64\n", "\n", "🔄 Mengubah label menjadi format numerik...\n", "Mapping Label:\n", "Negatif -> 0\n", "Positif -> 1\n", "\n", "✅ Pelabelan data selesai.\n"]}], "source": ["# ==============================================================================\n", "# SEL 2: PELABELAN BERBASIS RATING & TRANSFORMASI LABEL\n", "# ==============================================================================\n", "\n", "if not df.empty:\n", "    # 1. Membuat label sentimen kategorikal berdasarkan nilai rating\n", "    print(\"🔄 Membuat label sentimen berdasarkan rating bintang...\")\n", "    def create_sentiment_label(nilai):\n", "        if nilai in [1, 2]:\n", "            return 'Negatif'\n", "        <PERSON><PERSON> in [4, 5]:\n", "            return 'Positif'\n", "        # <PERSON><PERSON> 3 akan <PERSON> (return None)\n", "        return None\n", "\n", "    df['sentimen_kategorikal'] = df['nilai'].apply(create_sentiment_label)\n", "\n", "    # 2. <PERSON><PERSON><PERSON><PERSON> ulasan dengan sentimen netral (rating 3)\n", "    df.dropna(subset=['sentimen_kategorikal'], inplace=True)\n", "    print(\"Distribusi sentimen setelah menghapus rating 3 (Netral):\")\n", "    print(df['sentimen_kategorikal'].value_counts())\n", "\n", "    # 3. Mengubah label kategorikal ('<PERSON>si<PERSON><PERSON>', 'Negatif') menjadi numerik (0, 1)\n", "    print(\"\\n🔄 Mengubah label menjadi format numerik...\")\n", "    le = LabelEncoder()\n", "    df['sentimen'] = le.fit_transform(df['sentimen_kategorikal'])\n", "\n", "    print(\"Mapping Label:\")\n", "    for index, label in enumerate(le.classes_):\n", "        print(f\"{label} -> {index}\")\n", "\n", "    print(\"\\n✅ Pelabelan data selesai.\")\n", "else:\n", "    print(\"⚠️ Data tidak dimuat. Proses dilewati.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1183, "status": "ok", "timestamp": 1753016787452, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "DXWjOBSh8JkN", "outputId": "747e4696-058b-4292-b782-3361da7ef86a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔄 Mengubah teks menjadi vektor numerik dengan TF-IDF yang disempurnakan...\n", "Vectorizer TF-IDF yang disempurnakan telah disimpan.\n", "\n", "🔄 Membagi data menjadi 80% data latih dan 20% data uji...\n", "\n", "✅ Ekstraksi fitur dan pembagian data selesai.\n"]}], "source": ["# ==============================================================================\n", "# SEL 3: EKSTRAKSI FITUR (TF-IDF) & PEMBAGIAN DATA (DENGAN FEATURE TUNING)\n", "# ==============================================================================\n", "if not df.empty:\n", "    X = df['ulasan_stemmed'].fillna('')\n", "    y = df['sentimen']\n", "\n", "    print(\"🔄 Mengubah teks menjadi vektor numerik dengan TF-IDF yang disempurnakan...\")\n", "\n", "    # --- PERUBAHAN DI SINI ---\n", "    vectorizer = TfidfVectorizer(\n", "        max_features=3000,  # <PERSON><PERSON><PERSON> jumlah fitur maksimal\n", "        min_df=5,           # <PERSON><PERSON><PERSON> kata harus muncul di minimal 5 ulasan untuk dianggap fitur\n", "        ngram_range=(1, 2)    # Pertimbangkan juga pasangan kata (bigram), misal: \"tidak bagus\"\n", "    )\n", "    # --- <PERSON><PERSON><PERSON> ---\n", "\n", "    X_tfidf = vectorizer.fit_transform(X).toarray()\n", "    joblib.dump(vectorizer, 'tfidf_vectorizer_tuned.pkl')\n", "    print(\"Vectorizer TF-IDF yang disempurnakan telah disimpan.\")\n", "\n", "    print(\"\\n🔄 Membagi data menjadi 80% data latih dan 20% data uji...\")\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X_tfidf, y, test_size=0.2, random_state=42, stratify=y\n", "    )\n", "\n", "    print(\"\\n✅ Ekstraksi fitur dan pembagian data selesai.\")\n", "else:\n", "    print(\"⚠️ Data tidak dimuat. Proses dilewati.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 45132, "status": "ok", "timestamp": 1753016832599, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "rLEBCFL68O-1", "outputId": "27113f4b-1def-45c1-c277-fcbf8f0fd8a8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🌳 <PERSON><PERSON><PERSON> Model Random Forest...\n", "Model Random Forest berhasil dilatih.\n", "\n", "🔬 Mengevaluasi kinerja model pada data uji...\n", "\n", "<PERSON><PERSON>:\n", "Akurasi: 84.59%\n", "\n", "<PERSON><PERSON><PERSON>:\n", "              precision    recall  f1-score   support\n", "\n", "     Negatif       0.80      0.88      0.84      1312\n", "     Positif       0.89      0.82      0.85      1583\n", "\n", "    accuracy                           0.85      2895\n", "   macro avg       0.85      0.85      0.85      2895\n", "weighted avg       0.85      0.85      0.85      2895\n", "\n"]}], "source": ["# ==============================================================================\n", "# SEL 4: PELATIHAN & EVALUASI MODEL - RANDOM FOREST\n", "# ==============================================================================\n", "if 'X_train' in locals():\n", "    print(\"🌳 <PERSON><PERSON><PERSON> Model Random Forest...\")\n", "    rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)\n", "    rf_model.fit(X_train, y_train)\n", "    print(\"Model Random Forest berhasil dilatih.\")\n", "\n", "    joblib.dump(rf_model, 'model_random_forest.pkl')\n", "\n", "    print(\"\\n🔬 Mengevaluasi kinerja model pada data uji...\")\n", "    y_pred_rf = rf_model.predict(X_test)\n", "\n", "    print(\"\\nHasil Evaluasi Random Forest:\")\n", "    print(f\"Akurasi: {accuracy_score(y_test, y_pred_rf) * 100:.2f}%\")\n", "    print(\"\\nLaporan Klasifikasi:\")\n", "    print(classification_report(y_test, y_pred_rf, target_names=le.classes_))\n", "else:\n", "    print(\"⚠️ Data latih tidak ditemukan.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L1R_743rYT3N", "executionInfo": {"status": "ok", "timestamp": 1753019092145, "user_tz": -420, "elapsed": 2259547, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}}, "outputId": "f8f28ee4-67c8-46e9-86f4-ccbd4c85c5d1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🌳 <PERSON><PERSON><PERSON> Hyperparameter (Versi <PERSON>t) untuk Random Forest...\n", "Total model yang akan dilatih: 10 kombinasi x 2 validasi = 20 model.\n", "Fitting 2 folds for each of 10 candidates, totalling 20 fits\n", "\n", "Parameter terbaik yang ditemukan:\n", "{'n_estimators': 200, 'min_samples_split': 15, 'min_samples_leaf': 2, 'max_features': 'sqrt', 'max_depth': 60}\n", "\n", "Model Random Forest terbaik berhasil dilatih.\n", "Model terb<PERSON><PERSON> telah disimpan.\n", "\n", "🔬 Mengevaluasi kinerja model TERBAIK (Random Forest)...\n", "\n", "<PERSON><PERSON> (<PERSON><PERSON><PERSON>):\n", "Akurasi pada Data Latih: 87.45%\n", "Akurasi pada Data Uji:   84.08%\n", "<PERSON><PERSON><PERSON> Overfitting:      3.38%\n", "\n", "<PERSON><PERSON><PERSON>:\n", "              precision    recall  f1-score   support\n", "\n", "     Negatif       0.82      0.83      0.83      1312\n", "     Positif       0.86      0.85      0.85      1583\n", "\n", "    accuracy                           0.84      2895\n", "   macro avg       0.84      0.84      0.84      2895\n", "weighted avg       0.84      0.84      0.84      2895\n", "\n"]}], "source": ["# ==============================================================================\n", "# SEL 4 (VERSI CEPAT): OPTIMASI RANDOM FOREST DENGAN 20 TOTAL MODEL\n", "# ==============================================================================\n", "from sklearn.model_selection import RandomizedSearchCV\n", "\n", "# Pastikan data latih (X_train) sudah ada dari sel sebelumnya\n", "if 'X_train' in locals():\n", "    print(\"🌳 Me<PERSON>lai <PERSON>ian Hyperparameter (Versi Cepat) untuk Random Forest...\")\n", "\n", "    # 1. Tentukan rentang parameter yang sama seperti sebelumnya\n", "    param_dist_rf = {\n", "        'n_estimators': [100, 150, 200, 300],\n", "        'max_depth': [20, 40, 60, 80, 100, None],\n", "        'min_samples_leaf': [2, 5, 10],\n", "        'min_samples_split': [5, 10, 15],\n", "        'max_features': ['sqrt', 0.5]\n", "    }\n", "\n", "    # --- PERUBAHAN UTAMA DI SINI ---\n", "    # 2. Inisialisasi RandomizedSearchCV dengan parameter yang lebih ringan\n", "    # n_iter=10 dan cv=2 akan <PERSON><PERSON><PERSON><PERSON> total 10 x 2 = 20 model untuk dilatih\n", "    rf_base = RandomForestClassifier(random_state=42, n_jobs=-1)\n", "    random_search_rf = RandomizedSearchCV(\n", "        estimator=rf_base,\n", "        param_distributions=param_dist_rf,\n", "        n_iter=10,       # Coba 10 kombinasi acak saja\n", "        cv=2,            # <PERSON><PERSON><PERSON> 2-fold cross-validation\n", "        verbose=2,       # Tetap tampilkan progress\n", "        random_state=42,\n", "        n_jobs=-1\n", "    )\n", "    # --- AKHIR PERUBAHAN ---\n", "\n", "    print(\"Total model yang akan dilatih: 10 kombinasi x 2 validasi = 20 model.\")\n", "\n", "    # 3. <PERSON><PERSON><PERSON> RandomizedSearchCV\n", "    random_search_rf.fit(X_train, y_train)\n", "\n", "    # 4. Ambil model te<PERSON><PERSON><PERSON> dari hasil pencarian\n", "    rf_model_best = random_search_rf.best_estimator_\n", "    print(\"\\nParameter terbaik yang ditemukan:\")\n", "    print(random_search_rf.best_params_)\n", "    print(\"\\nModel Random Forest terbaik berhasil dilatih.\")\n", "    joblib.dump(rf_model_best, 'model_random_forest_tuned.pkl')\n", "    print(\"Model terbaik telah disimpan.\")\n", "\n", "    # 5. <PERSON><PERSON><PERSON>n <PERSON> Overfitting pada model TERBAIK\n", "    print(\"\\n🔬 Mengevaluasi kinerja model TERBAIK (Random Forest)...\")\n", "    y_pred_rf_best = rf_model_best.predict(X_test)\n", "    test_accuracy_rf = accuracy_score(y_test, y_pred_rf_best)\n", "\n", "    y_pred_train_rf_best = rf_model_best.predict(X_train)\n", "    train_accuracy_rf = accuracy_score(y_train, y_pred_train_rf_best)\n", "\n", "    print(\"\\nHasil Evaluasi <PERSON> Forest (Setelah Tuning):\")\n", "    print(f\"Akurasi pada Data Latih: {train_accuracy_rf * 100:.2f}%\")\n", "    print(f\"Akurasi pada Data Uji:   {test_accuracy_rf * 100:.2f}%\")\n", "    print(f\"Selisih Overfitting:      {(train_accuracy_rf - test_accuracy_rf) * 100:.2f}%\")\n", "\n", "    print(\"\\nLaporan Klasifikasi:\")\n", "    print(classification_report(y_test, y_pred_rf_best, target_names=le.classes_))\n", "else:\n", "    print(\"⚠️ Data latih tidak ditemukan. Jalankan Sel 3 terlebih dahulu.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 14642, "status": "ok", "timestamp": 1753019106793, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "L3WfpMINFxik", "outputId": "cf0a8033-f698-445f-f498-561cd24bb5dd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 <PERSON><PERSON><PERSON> Model XGBoost...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/xgboost/core.py:158: UserWarning: [13:44:54] WARNING: /workspace/src/learner.cc:740: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  warnings.warn(smsg, UserWarning)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Model XGBoost berhasil dilatih.\n", "\n", "🔬 Mengevaluasi kinerja model pada data uji...\n", "\n", "<PERSON><PERSON>:\n", "Akurasi: 84.18%\n", "\n", "<PERSON><PERSON><PERSON>:\n", "              precision    recall  f1-score   support\n", "\n", "     Negatif       0.83      0.82      0.82      1312\n", "     Positif       0.85      0.86      0.86      1583\n", "\n", "    accuracy                           0.84      2895\n", "   macro avg       0.84      0.84      0.84      2895\n", "weighted avg       0.84      0.84      0.84      2895\n", "\n"]}], "source": ["# ==============================================================================\n", "# SEL 5: PELATIHAN & EVALUASI MODEL - XGBOOST\n", "# ==============================================================================\n", "if 'X_train' in locals():\n", "    print(\"🚀 <PERSON><PERSON><PERSON> Model XGBoost...\")\n", "    xgb_model = XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)\n", "    xgb_model.fit(X_train, y_train)\n", "    print(\"Model XGBoost berhasil dilatih.\")\n", "\n", "    joblib.dump(xgb_model, 'model_xgboost.pkl')\n", "\n", "    print(\"\\n🔬 Mengevaluasi kinerja model pada data uji...\")\n", "    y_pred_xgb = xgb_model.predict(X_test)\n", "\n", "    print(\"\\nHasil Evaluasi XGBoost:\")\n", "    print(f\"Akurasi: {accuracy_score(y_test, y_pred_xgb) * 100:.2f}%\")\n", "    print(\"\\nLaporan Klasifikasi:\")\n", "    print(classification_report(y_test, y_pred_xgb, target_names=le.classes_))\n", "else:\n", "    print(\"⚠️ Data latih tidak ditemukan.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "executionInfo": {"elapsed": 1326972, "status": "ok", "timestamp": 1753022006040, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "E47M_pWZGptv", "outputId": "7a983d4f-89bb-46a0-c8b8-faa64b46c6c6"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 <PERSON><PERSON><PERSON> Hyperparameter Terbaik untuk XGBoost...\n", "Fitting 3 folds for each of 25 candidates, totalling 75 fits\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/joblib/externals/loky/process_executor.py:782: UserWarning: A worker stopped while some jobs were given to the executor. This can be caused by a too short worker timeout or by a memory leak.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/xgboost/core.py:158: UserWarning: [14:32:34] WARNING: /workspace/src/learner.cc:740: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  warnings.warn(smsg, UserWarning)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Parameter terbaik yang ditemukan:\n", "{'subsample': 0.7, 'n_estimators': 200, 'max_depth': 30, 'learning_rate': 0.1, 'gamma': 0, 'colsample_bytree': 0.9}\n", "\n", "Model XGBoost terbaik berhasil dilatih.\n", "Model terb<PERSON><PERSON> telah disimpan.\n", "\n", "🔬 Mengevaluasi kinerja model TERBAIK...\n", "\n", "<PERSON><PERSON> (<PERSON><PERSON>h <PERSON>):\n", "Akurasi pada Data Latih: 95.22%\n", "Akurasi pada Data Uji:   84.35%\n", "\n", "<PERSON><PERSON><PERSON>:\n", "              precision    recall  f1-score   support\n", "\n", "     Negatif       0.82      0.83      0.83      1312\n", "     Positif       0.86      0.85      0.86      1583\n", "\n", "    accuracy                           0.84      2895\n", "   macro avg       0.84      0.84      0.84      2895\n", "weighted avg       0.84      0.84      0.84      2895\n", "\n", "\n", "📊 <PERSON><PERSON><PERSON> (Confusion Matrix):\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAApIAAAIjCAYAAACwHvu2AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAY+9JREFUeJzt3Xd8VNXWxvFnkpBCIA0IIQghEHoRAcXQhShdEBRRlNCMIEUEFbhekCJFkBZEEOkYFQuioCBIkSq9CUgvggk9RHrKef/gZa5jAiTHTCZkfl8/cy+zz54965wUFmvvs8diGIYhAAAAIINcHB0AAAAAHkwkkgAAADCFRBIAAACmkEgCAADAFBJJAAAAmEIiCQAAAFNIJAEAAGAKiSQAAABMIZEEAACAKSSSsJtDhw7pqaeekq+vrywWixYuXJip4x8/flwWi0WzZ8/O1HEfZPXq1VO9evUcHQYy4LXXXtOTTz7p6DDuq0OHDipWrJip11osFvXo0SNzA7Kj2bNny2Kx6Pjx43Z9n8cff1xvv/22Xd8DsDcSyRzuyJEjevXVV1W8eHF5enrKx8dHNWvW1MSJE3X9+nW7vndkZKT27Nmj4cOHa968eapWrZpd3y8rdejQQRaLRT4+Pmlex0OHDslischiseiDDz7I8Ph//vmnBg8erJ07d2ZCtPa3b98+ubu7q2PHjqmOxcfHq1ChQqpevbpSUlJsju3evVsdO3ZUaGioPD09lSdPHlWuXFlvv/22jh49atP3zjW/83Bzc1ORIkXUtm1b7du3z67nlx779u3T4MGDM5R8HDt2TNOnT9d//vMfm/Zz587p9ddfV5kyZeTl5aXAwEA99thj6tevn65cuZLh2DZs2KDBgwcrPj4+w691pHr16tl8ze/2GDx4sKNDNaVfv36aPHmy4uLiHB0KYJqbowOA/fzwww967rnn5OHhofbt26tChQq6deuW1q1bp7feekt79+7VtGnT7PLe169f18aNG/XOO+/YrRIREhKi69evK1euXHYZ/37c3Nx07do1LVq0SG3atLE5FhMTI09PT924ccPU2H/++aeGDBmiYsWKqXLlyul+3bJly0y9379Vrlw5vfXWWxoxYoQ6dOigunXrWo/1799f586d05IlS+Ti8r9/u37yySfq1q2b8ufPr3bt2qlMmTJKSkrSb7/9prlz52rChAm6fv26XF1dra/x8PDQ9OnTJUlJSUk6cuSIpk6dqqVLl2rfvn0KDg7OupP+h3379mnIkCGqV69euit3EydOVGhoqJ544glr28WLF1WtWjUlJCSoU6dOKlOmjC5cuKDdu3drypQp6tatm/LkyZOh2DZs2KAhQ4aoQ4cO8vPzy9BrHemdd95Rly5drM+3bNmi6Oho/ec//1HZsmWt7ZUqVcrU93355ZfVtm1beXh4ZOq4/9SiRQv5+Pjoo48+0tChQ+36XoC9kEjmUMeOHVPbtm0VEhKilStXqlChQtZj3bt31+HDh/XDDz/Y7f3PnTsnSXb9S8tiscjT09Nu49+Ph4eHatasqc8//zxVIvnZZ5+padOm+uabb7IklmvXril37txyd3fPkvdLy8CBAzV//ny9+uqr2r17t9zd3bVx40ZNmzZNb7zxhk1CvGHDBnXr1k01a9bU4sWLlTdvXpuxxo4dq+HDh6d6Dzc3N7300ks2bY8//riaNWumH374Qa+88opdzs0eEhMTFRMTo65du9q0z5gxQydPntT69etVo0YNm2MJCQkO/RpntX9O+Xt6eio6OlpPPvmkXZdwuLq62vwDxl5cXFz07LPPau7cuRoyZIgsFovd3xPIdAZypK5duxqSjPXr16erf2JiojF06FCjePHihru7uxESEmIMGDDAuHHjhk2/kJAQo2nTpsbatWuNRx991PDw8DBCQ0ONOXPmWPu8++67hiSbR0hIiGEYhhEZGWn989/dec3fLVu2zKhZs6bh6+treHt7G6VKlTIGDBhgPX7s2DFDkjFr1iyb161YscKoVauWkTt3bsPX19d4+umnjX379qX5focOHTIiIyMNX19fw8fHx+jQoYNx9erV+16vyMhIw9vb25g9e7bh4eFhXLp0yXps8+bNhiTjm2++MSQZY8aMsR67cOGC0bdvX6NChQqGt7e3kTdvXqNRo0bGzp07rX1WrVqV6vr9/Tzr1q1rlC9f3ti6datRu3Ztw8vLy3j99detx+rWrWsdq3379oaHh0eq83/qqacMPz8/4/Tp0/c914xYtmyZIckYPHiwcevWLaNChQpG0aJFjStXrqR6fzc3N+OPP/5I99h3rvk/bd261ZBkzJw506b9yJEjxrPPPmv4+/sbXl5eRvXq1Y3Fixenev2ZM2eMTp06GYGBgYaHh4dRqVIlY/bs2an6ff7550aVKlWMPHnyGHnz5jUqVKhgTJgwwTAMw5g1a1aaX7NVq1bd9XxWrlxpSDJWr15t0/7qq68arq6uRnJycnoui/Hrr78aDRs2NHx8fAwvLy+jTp06xrp166zH0/p5lGQcO3bM2mfevHlGlSpVDE9PT8Pf3994/vnnjZMnT9q8T1o/u2PGjDHCw8ONgIAAw9PT06hSpYrx1VdfpYpRktG9e3fj22+/NcqXL2+4u7sb5cqVM5YsWZKuc7zjq6++SnVdM/I7Jb1x3Pl6/v0aped33x27du0y6tSpY3h6ehqFCxc2hg0bZsycOTPVmIZhGN99950hydi+fXuGrgWQXbBGModatGiRihcvnqqicTddunTRoEGDVKVKFY0fP15169bVyJEj1bZt21R9Dx8+rGeffVZPPvmkxo4dK39/f3Xo0EF79+6VJLVq1Urjx4+XJL3wwguaN2+eJkyYkKH49+7dq2bNmunmzZsaOnSoxo4dq6efflrr16+/5+t+/vlnNWzYUGfPntXgwYPVp08fbdiwQTVr1kxz7VqbNm30119/aeTIkWrTpo1mz56tIUOGpDvOVq1ayWKxaMGCBda2zz77TGXKlFGVKlVS9T969KgWLlyoZs2aady4cXrrrbe0Z88e1a1bV3/++ackqWzZstZprqioKM2bN0/z5s1TnTp1rONcuHBBjRs3VuXKlTVhwgSbqdG/mzhxogoUKKDIyEglJydLkj7++GMtW7ZMkyZNyvSp4CeffFIvvPCCRo4cqR49eui3337TpEmT5O3tbe1z7do1rVy5UvXq1dNDDz2U4fc4f/68zp8/rzNnzmjjxo164403lC9fPjVr1sza58yZM6pRo4Z++uknvfbaaxo+fLhu3Lihp59+Wt9++6213/Xr11WvXj3NmzdP7dq105gxY+Tr66sOHTpo4sSJ1n7Lly/XCy+8IH9/f73//vsaNWqU6tWrZ/1+rFOnjnr16iVJ+s9//mP9mv19+vWfNmzYIIvFokceecSmPSQkRMnJyZo3b959r8XKlStVp04dJSQk6N1339WIESMUHx+v+vXra/PmzZJuf4++8MILkqTx48dbYytQoIAkafjw4Wrfvr1KliypcePGqXfv3lqxYoXq1Klz3zWVEydO1COPPKKhQ4dqxIgRcnNz03PPPZfmbMe6dev02muvqW3btho9erRu3Lih1q1b68KFC/c9z8z0b+K43+8+STp9+rSeeOIJ7d27VwMGDNAbb7yhmJgYm++nv6tataok3fd3G5BtOTqTRea7fPmyIclo0aJFuvrv3LnTkGR06dLFpv3NN980JBkrV660toWEhBiSjDVr1ljbzp49a3h4eBh9+/a1tt2pFv69GmcY6a8ejB8/3pBknDt37q5xp1WRrFy5shEYGGhcuHDB2rZr1y7DxcXFaN++far369Spk82YzzzzjJEvX767vuffz+NOdezZZ581GjRoYBiGYSQnJxtBQUHGkCFD0rwGN27cSFVpOnbsmOHh4WEMHTrU2rZly5Y0q62GcbvqKMmYOnVqmsf+XpE0DMP46aefDEnGe++9Zxw9etTIkyeP0bJly/ueo1lxcXGGv7+/ISnN99m1a5chyejdu3eqYxcuXDDOnTtnfdy8edN6LDIyMs3KWuHChY1t27bZjNO7d29DkrF27Vpr219//WWEhoYaxYoVs34NJkyYYEgyPv30U2u/W7duGeHh4UaePHmMhIQEwzAM4/XXXzd8fHyMpKSku553WtWye3nppZfS/F6Li4szChQoYEgyypQpY3Tt2tX47LPPjPj4eJt+KSkpRsmSJY2GDRsaKSkp1vZr164ZoaGhxpNPPmltGzNmTJrVsOPHjxuurq7G8OHDbdr37NljuLm52bSn9bN77do1m+d3qtD169e3aZdkuLu7G4cPH7a23fk+mDRpUhpXJ22ZUZFMTxx3q0im53dfz549DYvFYuzYscPaduHCBSMgICDNr4FhGIa7u7vRrVu3dFwBIPuhIpkDJSQkSFKqdWd38+OPP0qS+vTpY9Pet29fSUpVXShXrpxq165tfV6gQAGVLl061V22/8adtZXfffddqjt97yY2NlY7d+5Uhw4dFBAQYG2vVKmSnnzySet5/t0/16fVrl1bFy5csF7D9HjxxRe1evVqxcXFaeXKlYqLi9OLL76YZl8PDw/rDSfJycm6cOGC8uTJo9KlS2v79u3pfk8PD48075BOy1NPPaVXX31VQ4cOVatWreTp6amPP/443e+VUblz51bu3Lmt7/1Pd65tWjeMFC9eXAUKFLA+vv/+e5vjnp6eWr58uZYvX66ffvpJH3/8sfLkyaMmTZro4MGD1n4//vijHnvsMdWqVcvalidPHkVFRen48ePWu7x//PFHBQUFWSt2kpQrVy716tVLV65c0S+//CLp9vfj1atXtXz5crOXJZULFy7I398/VXvBggW1a9cude3aVZcuXdLUqVP14osvKjAwUMOGDZNhGJKknTt36tChQ3rxxRd14cIFa6X26tWratCggdasWXPfn50FCxYoJSVFbdq0sb7+/PnzCgoKUsmSJbVq1ap7vt7Ly8v650uXLuny5cuqXbt2mt/LERERKlGihPV5pUqV5OPjk6m/N9Lj38SRnt99S5cuVXh4uM2a4ICAALVr1+6u4/r7++v8+fMZPBMgeyCRzIF8fHwkSX/99Ve6+p84cUIuLi4KCwuzaQ8KCpKfn59OnDhh0160aNFUY/j7++vSpUsmI07t+eefV82aNdWlSxcVLFhQbdu21ZdffnnPvxjvxFm6dOlUx8qWLWv9S/bv/nkud/5iz8i5NGnSRHnz5tX8+fMVExOjRx99NNW1vCMlJUXjx49XyZIl5eHhofz586tAgQLavXu3Ll++nO73LFy4cIZuuvjggw8UEBCgnTt3Kjo6WoGBgfd9zblz5xQXF2d9pHfbmXfeeUdxcXEqW7as3n333VTX8s4/cNIa77vvvtPy5cvvumWSq6urIiIiFBERoaeeekpRUVH6+eefdfnyZQ0YMMDa78SJE3f9Prhz/M7/lyxZ0uZu8rT6vfbaaypVqpQaN26shx56SJ06ddLSpUvTdT3u5U5S+E+FChXSlClTFBsbqwMHDig6OloFChTQoEGDNGPGDEm3t5iSbm+z9ffku0CBApo+fbpu3rx53++pQ4cOyTAMlSxZMtUY+/fv19mzZ+/5+sWLF+vxxx+Xp6enAgICVKBAAU2ZMiXN982K3xvp8W/iSM9rT5w4kebP/91+J0i3vw+40QYPKu7azoF8fHwUHBys3377LUOvS+8vsrvdzXi3vxTT8x531u/d4eXlpTVr1mjVqlX64YcftHTpUs2fP1/169fXsmXLMu2Oyn9zLnd4eHioVatWmjNnjo4ePXrPPe1GjBihgQMHqlOnTho2bJgCAgLk4uKi3r17p7vyKtlWgtJjx44d1qRgz549NhW4u3n00Udt/hHx7rvv3ne/vq1bt2ry5Mnq1auXOnbsqKpVq6pfv34220yFhYXJzc0tze/PO9sGubml/1fTQw89pNKlS2vNmjXpfk1GBQYGaufOnfrpp5+0ZMkSLVmyRLNmzVL79u01Z84cU2Pmy5fvvsmLxWJRqVKlVKpUKTVt2lQlS5ZUTEyMunTpYv1+GTNmzF23iLrfNkEpKSmyWCxasmRJmj8L93r92rVr9fTTT6tOnTr66KOPVKhQIeXKlUuzZs3SZ599lqp/ZvyspSW9v1MyIw57nUN8fLzy58//r8YAHIVEModq1qyZpk2bpo0bNyo8PPyefUNCQpSSkqJDhw7Z3Bxw5swZxcfHKyQkJNPi8vf3T3MB/z+rntLtrTEaNGigBg0aaNy4cRoxYoTeeecdrVq1ShEREWmehyQdOHAg1bHff/9d+fPnt7npIzO9+OKLmjlzplxcXNK8QemOr7/+Wk888YS1qnTHP/8iyczqxNWrV9WxY0eVK1dONWrU0OjRo/XMM8/o0UcfvefrYmJibDZbL168+D37JycnKyoqSsHBwRo6dKjy5s2r119/XePGjVPHjh2t34fe3t6qV6+efvnlF50+fVqFCxf+1+eYlJRkU+EMCQm56/fBneN3/n/37t1KSUmxqUr+s58kubu7q3nz5mrevLlSUlL02muv6eOPP9bAgQMVFhaW4a9ZmTJlFBMTo8uXL8vX1/e+/YsXLy5/f3/FxsZKknV61sfHJ82fh7+7W2wlSpSQYRgKDQ1VqVKlMhT/N998I09PT/300082+y3OmjUrQ+P8Wxn5nZIVQkJCdPjw4VTtabVJt2/OuXXr1j1vzAKyM6a2c6i3335b3t7e6tKli86cOZPq+JEjR6x3ETZp0kSSUt1ZPW7cOElS06ZNMy2uEiVK6PLly9q9e7e1LTY21uZOWun2psz/dKfqcvPmzTTHLlSokCpXrqw5c+bY/MXy22+/admyZdbztIcnnnhCw4YN04cffqigoKC79nN1dU1Vvfjqq690+vRpm7Y7CW9mfBJJv379dPLkSc2ZM0fjxo1TsWLFFBkZedfreEfNmjWt08gRERH3TSSjo6O1Y8cORUdHW6evhwwZooceekhdu3ZVUlKSte+gQYOUnJysl156Kc0p7oxUeA4ePKgDBw7o4YcftrY1adJEmzdv1saNG61tV69e1bRp01SsWDGVK1fO2i8uLk7z58+39ktKStKkSZOUJ08ea4X0n3f0uri4WDfBvnMdM/o1Cw8Pl2EY2rZtm037pk2bUi3BkKTNmzfrwoUL1in7qlWrqkSJEvrggw/SvIZ39nK9V2ytWrWSq6urhgwZkuqaG4ZxzzuZXV1dZbFYbCp/x48fz/SPQr2f9P5OySoNGzbUxo0bbT6V6uLFi4qJiUmz/52vf3p32ACyGyqSOVSJEiX02Wef6fnnn1fZsmVtPtlmw4YN+uqrr9ShQwdJ0sMPP6zIyEhNmzZN8fHxqlu3rjZv3qw5c+aoZcuWd91axoy2bduqX79+euaZZ9SrVy9du3ZNU6ZMUalSpWwW6A8dOlRr1qxR06ZNFRISorNnz+qjjz7SQw89ZHMDxT+NGTNGjRs3Vnh4uDp37qzr169r0qRJ8vX1tevHqLm4uOi///3vffs1a9ZMQ4cOVceOHVWjRg3t2bNHMTExqZK0EiVKyM/PT1OnTlXevHnl7e2t6tWrKzQ0NENxrVy5Uh999JHeffdd63ZEs2bNUr169TRw4ECNHj06Q+PdzR9//KFBgwapefPmeuaZZ6zt3t7emjhxolq1aqWJEydab+CqXbu2PvzwQ/Xs2VMlS5a0frLNrVu3dPDgQcXExMjd3T1VUp6UlKRPP/1U0u1p2ePHj2vq1KlKSUnRu+++a+3Xv39/ff7552rcuLF69eqlgIAAzZkzR8eOHdM333xjrT5GRUXp448/VocOHbRt2zYVK1ZMX3/9tdavX68JEyZYE+IuXbro4sWLql+/vh566CGdOHFCkyZNUuXKla2VpMqVK8vV1VXvv/++Ll++LA8PD9WvX/+u61Fr1aqlfPny6eeff1b9+vWt7fPmzVNMTIyeeeYZVa1aVe7u7tq/f79mzpwpT09P68cpuri4aPr06WrcuLHKly+vjh07qnDhwjp9+rRWrVolHx8fLVq0SNL/tph555131LZtW+XKlUvNmzdXiRIl9N5772nAgAE6fvy4WrZsqbx58+rYsWP69ttvFRUVpTfffDPN+Js2bapx48apUaNGevHFF3X27FlNnjxZYWFhNkmdvaX3d0pWefvtt/Xpp5/qySefVM+ePeXt7a3p06eraNGiunjxYqrq8PLly1W0aNFU20ABDwwH3CmOLHTw4EHjlVdeMYoVK2a4u7sbefPmNWrWrGlMmjTJZrPxxMREY8iQIUZoaKiRK1cuo0iRIvfckPyf/rntzN22/zGM25tWV6hQwXB3dzdKly5tfPrpp6m26lixYoXRokULIzg42HB3dzeCg4ONF154wTh48GCq9/jnFjk///yzUbNmTcPLy8vw8fExmjdvftcNyf+5vVBa236k5W6bY//d3bb/6du3r1GoUCHDy8vLqFmzprFx48Y0t+357rvvjHLlyhlubm5pbkielr+Pk5CQYISEhBhVqlQxEhMTbfq98cYbhouLi7Fx48Z7nkN6tWjRwvD29jZOnDiR5vFmzZoZefLkSbXJ9Y4dO4z27dsbRYsWNdzd3Q1vb2+jUqVKRt++fW22aDGMtLf/8fHxMRo0aGD8/PPPqd7zzobkfn5+hqenp/HYY4/ddUPyjh07Gvnz5zfc3d2NihUrpvqe+vrrr42nnnrKCAwMNNzd3Y2iRYsar776qhEbG2vT75NPPjGKFy9uuLq6pmsroF69ehlhYWE2bbt37zbeeusto0qVKkZAQIDh5uZmFCpUyHjuuefS3LR6x44dRqtWrYx8+fIZHh4eRkhIiNGmTRtjxYoVNv2GDRtmFC5c2HBxcUn1Pf7NN98YtWrVMry9vQ1vb2+jTJkyRvfu3Y0DBw5Y+6S1zc6MGTOMkiVLGh4eHkaZMmWMWbNm3XMj8H8KCQkxIiMj73mN/u5uWyyl53dKRuK414bk/5TWz+6OHTuM2rVrGx4eHsZDDz1kjBw50oiOjjYkGXFxcdZ+ycnJRqFChYz//ve/6b4GQHZjMYx/uUoYAGDK0aNHVaZMGS1ZskQNGjRwdDiwo969e+vjjz/WlStXrDftLFy4UC+++KKOHDli8zG2wIOERBIAHKhbt246fPhwpu5RCce6fv26zc4KFy5cUKlSpVSlShWbr3N4eLhq166daUtMAEcgkQQAIBNVrlxZ9erVU9myZXXmzBnNmDFDf/75p/WjJ4GchJttAADIRE2aNNHXX3+tadOmyWKxqEqVKpoxYwZJJHIkKpIAAAAwhX0kAQAAYAqJJAAAAEwhkQQAAIApOfJmmyLdv3N0CADs5NDEFo4OAYCdeDowK/F6pIfdxr6+40O7je1oVCQBAABgSo6sSAIAAGSIhdqaGSSSAAAAFoujI3ggkX4DAADAFCqSAAAATG2bwlUDAACAKVQkAQAAWCNpChVJAAAAmEJFEgAAgDWSpnDVAAAAYAoVSQAAANZImkIiCQAAwNS2KVw1AAAAmEJFEgAAgKltU6hIAgAAwBQqkgAAAKyRNIWrBgAAAFOoSAIAALBG0hQqkgAAADCFiiQAAABrJE0hkQQAAGBq2xTSbwAAAJhCRRIAAICpbVO4agAAADCFiiQAAAAVSVO4agAAADCFiiQAAIALd22bQUUSAAAAplCRBAAAYI2kKSSSAAAAbEhuCuk3AAAATKEiCQAAwNS2KVw1AAAAmEJFEgAAgDWSplCRBAAAgClUJAEAAFgjaQpXDQAAAKZQkQQAAGCNpCkkkgAAAExtm8JVAwAAgClUJAEAAJjaNoWKJAAAAEyhIgkAAMAaSVO4agAAADCFiiQAAABrJE2hIgkAAABTqEgCAACwRtIUEkkAAAASSVO4agAAADCFiiQAAAA325hCRRIAAACmUJEEAABgjaQpXDUAAACYQkUSAACANZKmUJEEAACAKSSSAAAAFhf7PTJozZo1at68uYKDg2WxWLRw4ULrscTERPXr108VK1aUt7e3goOD1b59e/355582Y1y8eFHt2rWTj4+P/Pz81LlzZ125csWmz+7du1W7dm15enqqSJEiGj16dIZjJZEEAACwWOz3yKCrV6/q4Ycf1uTJk1Mdu3btmrZv366BAwdq+/btWrBggQ4cOKCnn37apl+7du20d+9eLV++XIsXL9aaNWsUFRVlPZ6QkKCnnnpKISEh2rZtm8aMGaPBgwdr2rRpGbtshmEYGT7DbK5I9+8cHQIAOzk0sYWjQwBgJ54OvHPDq9UMu419fUFn06+1WCz69ttv1bJly7v22bJlix577DGdOHFCRYsW1f79+1WuXDlt2bJF1apVkyQtXbpUTZo00alTpxQcHKwpU6bonXfeUVxcnNzd3SVJ/fv318KFC/X777+nOz4qkgAAwOlZLBa7PW7evKmEhASbx82bNzMt9suXL8tiscjPz0+StHHjRvn5+VmTSEmKiIiQi4uLNm3aZO1Tp04daxIpSQ0bNtSBAwd06dKldL83iSQAAIAdjRw5Ur6+vjaPkSNHZsrYN27cUL9+/fTCCy/Ix8dHkhQXF6fAwECbfm5ubgoICFBcXJy1T8GCBW363Hl+p096sP0PAABwehY7bv8zYMAA9enTx6bNw8PjX4+bmJioNm3ayDAMTZky5V+PZwaJJAAAgB15eHhkSuL4d3eSyBMnTmjlypXWaqQkBQUF6ezZszb9k5KSdPHiRQUFBVn7nDlzxqbPned3+qQHU9sAAAAWOz4y2Z0k8tChQ/r555+VL18+m+Ph4eGKj4/Xtm3brG0rV65USkqKqlevbu2zZs0aJSYmWvssX75cpUuXlr+/f7pjIZEEAADIRq5cuaKdO3dq586dkqRjx45p586dOnnypBITE/Xss89q69atiomJUXJysuLi4hQXF6dbt25JksqWLatGjRrplVde0ebNm7V+/Xr16NFDbdu2VXBwsCTpxRdflLu7uzp37qy9e/dq/vz5mjhxYqop+Pth+x8ADxS2/wFyLkdu/5OnzWy7jX3lyw4Z6r969Wo98cQTqdojIyM1ePBghYaGpvm6VatWqV69epJub0jeo0cPLVq0SC4uLmrdurWio6OVJ08ea//du3ere/fu2rJli/Lnz6+ePXuqX79+GYqVNZIAAMDp2fNmm4yqV6+e7lXnS08NMCAgQJ999tk9+1SqVElr167NcHx/x9Q2AAAATKEiCQAAnF52qkg+SKhIAgAAwBQqkgAAwOlRkTSHiiQAAABMoSIJAABAQdIUKpIAAAAwhYokAABweqyRNIeKJAAAAEyhIgkAAJweFUlzSCQBAIDTI5E0h6ltAAAAmEJFEgAAOD0qkuZQkQQAAIApVCQBAAAoSJpCRRIAAACmUJEEAABOjzWS5lCRBAAAgClUJAEAgNOjImkOiSQAAHB6JJLmMLUNAAAAU6hIAgAAUJA0hYokAAAATKEiCQAAnB5rJM2hIgkAAABTqEgCAACnR0XSHIdVJAMCAnT+/HlJUqdOnfTXX385KhQAAACY4LBE8tatW0pISJAkzZkzRzdu3HBUKAAAwMlZLBa7PXIyh01th4eHq2XLlqpataoMw1CvXr3k5eWVZt+ZM2dmcXQAAMCZ5PSEz14clkh++umnGj9+vI4cOSKLxaLLly9TlQQAAHiAOCyRLFiwoEaNGiVJCg0N1bx585QvXz5HhQMAAJwZBUlTssVd28eOHXN0CAAAAMgghyWS0dHRioqKkqenp6Kjo+/Zt1evXlkUFQAAcEaskTTHYYnk+PHj1a5dO3l6emr8+PF37WexWEgkAQAAsiGHJZJ/n85mahsAADgSFUlzssVHJA4dOlTXrl1L1X79+nUNHTrUAREBAADgfrJFIjlkyBBduXIlVfu1a9c0ZMgQB0QEAACcCRuSm5Mt7to2DCPNC71r1y4FBAQ4ICIAAOBUcna+ZzcOTST9/f2t2XqpUqVsksnk5GRduXJFXbt2dWCEAAAAuBuHJpITJkyQYRjq1KmThgwZIl9fX+sxd3d3FStWTOHh4Q6MEAAAOIOcPgVtLw5NJCMjIyXd/mSbGjVqKFeuXI4MBwAAABmQLdZI1q1b1/rnGzdu6NatWzbHfXx8sjokAADgRKhImpMt7tq+du2aevToocDAQHl7e8vf39/mAQAAgOwnW1Qk33rrLa1atUpTpkzRyy+/rMmTJ+v06dP6+OOPNWrUKEeHBzurHpZPr0aEqVIRPxX081SXjzfpp91xNn36Ni2jF2qGyNcrl7Ycvaj/fLFLx89dtR6vUMRXA1qU08Mh/kpJMfTjzj81dMFvunYz2drnj8ktUr1395lb9f220/Y7OQA2ZnzysVYsX6Zjx47Kw9NTlSs/ot593lSx0OLWPl9/OV9Lflys/fv26urVq1q7cYvNzNSWzZvUpWP7NMeP+eIrVahYye7ngZyHiqQ52SKRXLRokebOnat69eqpY8eOql27tsLCwhQSEqKYmBi1a9fO0SHCjrzcXbX/1GV9ufGkPol6LNXxbk+GqWO94uozb7tOnr+mt5qX0ac9wtVg2ErdTEpRQV9Pfd6zhhZtP62BX+5RXk83vftsBY17uYq6Tt9iM1afedu1et9Z6/OEa4l2Pz8A/7N1y2Y9/0I7la9YUclJyZo0cZy6vtJZC77/Qblz55Yk3bhxXTVq1laNmrUVPWFsqjEqV35EK1avs2mbPGmiNm3aqPIVKmbJeQC4LVskkhcvXlTx4rf/Nerj46OLFy9KkmrVqqVu3bo5MjRkgdX7ztokd//U+YkSmrT0gJb9f5Wy95zt2j6qkRo+XEjfbzutBhUKKjE5Re/M3y3DuP2a/3yxS8vfqa9iBbxtKpcJ1xJ1LuGmXc8HwN1NmTbD5vnQ4aP0RO1w7d+3V1WrPSpJeql9B0m3K49pyeXurvwFClifJyYmatWqFXrhxZeoKsE0vnfMyRZrJIsXL279vO0yZcroyy+/lHS7Uunn5+fAyOBoRfPlVkFfT609cM7a9teNJO08fklVQm+vn3V3c1FismFNIiXpRmKKJOnRErYb2r/3fCXter+RFr1VR8+HF7X/CQC4pyt//SVJ8vnb9m8Z9cuqlbocH6+Wz7TOrLDgjCx2fORg2aIi2bFjR+3atUt169ZV//791bx5c3344YdKTEzUuHHj7vnamzdv6uZN2wqTkZwoiytbCeUEBXw8JEnn/1FFPPfXTQX6eEqSNhw8r0GtK+jViDDNXHVEud3d1L9FOUmy9pGkDxbt1/qD53X9VrLqlC2g956vpNwebpq1+mgWnQ2Av0tJSdHo90eo8iNVVLJkKdPjfLvga9WoWUsFg4IyMToA6ZEtEsk33njD+ueIiAj9/vvv2rZtm8LCwlSp0r0XTY8cOTLV53Hnrfa8fB97wS6xIvs5GPuX+szdroGtK6j/02WVnGJo1i9HdTbhhlL+VqacuPSg9c97T11Wbnc3dY0II5EEHGTEe0N05NAhzZ73mekxzsTFacP6dRozdkLmBQanxNS2OdkikfynkJAQhYSEpKvvgAED1KdPH5u2cm8vs0dYcIA76xnz+3jo7N+qkgXyemjvqcvW5wu3ntbCraeVP6+Hrt1KkmFIr9QP08nz1+469o7jl9S7SWm5u7noVlKK/U4CQCoj3huqNb+s1sw5n/6rSuLCb7+Rr5+f6j5RPxOjA5Be2SKRjI6OTrPdYrHI09NTYWFhqlOnjlxdXVP18fDwkIeHh+3rmNbOMU5euKYzl2+oVukC2ncqQZKUx9NNlYv5a97a46n6n//rdrL5fHhR3UxM1trf734TT/mHfBV/9RZJJJCFDMPQyOHDtHLFcs2YPU8PPVTkX4313cIFav50Sz4ZDf8aFUlzskUiOX78eJ07d07Xrl2zbkB+6dIl5c6dW3ny5NHZs2dVvHhxrVq1SkWKmP+lg+wpt4erihXwtj4vki+3yj3ko/irifrz0nXNWHVEPRuV0rGzV/XHhat6s1lZnbl8Qz/tirW+JrJuqLYdvairN5NUp0yg3nmmnEZ+t18J15MkSREVCiq/j4d2HLukm0kpql2mgHo0LKmPVxzO8vMFnNmIYUO05MfFmjDpI3nn9tb5c7dvpMuTN688PW+vaT5/7pzOnz+vP06elCQdPnRQuXN7q1ChQvL92w2Ymzf9qtOnTqlV62ez/DwA3GYxjL/f6+oYn3/+uaZNm6bp06erRIkSkqTDhw/r1VdfVVRUlGrWrKm2bdsqKChIX3/99X3HK9L9O3uHjEz0eMl8+qp3rVTtX/16Un3m7ZB0e0PyF2uFyMcrl7Ycuah35u/SsbP/29ZnfPsqalC+oHJ7uOrImSv6eMVhLdh8ynq8XrlA9Xu6rIoVyCOLRTp+7qrmrT2mz9afkON/ApARhyam3lgeD46Hy5dOs33oeyPV4plWkqQpkydp6kcf3rOPJPV/q69i/zytOTFf2CdYZDlPB5a3wt5cYrexD3/Q2G5jO1q2SCRLlCihb775RpUrV7Zp37Fjh1q3bq2jR49qw4YNat26tWJjY9Me5G9IJIGci0QSyLlIJB882WJqOzY2VklJSanak5KSFBd3exPq4OBg/fX/+40BAABkJtZImpMtNiR/4okn9Oqrr2rHjh3Wth07dqhbt26qX//2nXh79uxRaGioo0IEAAA5mMViv0dOli0SyRkzZiggIEBVq1a13oVdrVo1BQQEaMaM2x+nlSdPHo0dm/ozVwEAAOAY2WJqOygoSMuXL9fvv/+ugwdvbxpdunRplS79v0XZTzzxhKPCAwAAORxT2+Zki0TyjuLFi8tisahEiRJyc8tWoQEAAOAfssXU9rVr19S5c2flzp1b5cuX18n/3zusZ8+eGjVqlIOjAwAAOR1rJM3JFonkgAEDtGvXLq1evdq6Ia10+3O358+f78DIAAAAcDfZYv544cKFmj9/vh5//HGbNQrly5fXkSNHHBgZAABwBi4uObx0aCfZoiJ57tw5BQYGpmq/evUqi18BAACyqWyRSFarVk0//PCD9fmd5HH69OkKDw93VFgAAMBJsEbSnGwxtT1ixAg1btxY+/btU1JSkiZOnKh9+/Zpw4YN+uWXXxwdHgAAyOGYATUnW1Qka9WqpZ07dyopKUkVK1bUsmXLFBgYqI0bN6pq1aqODg8AAABpyBYVSUkqUaKEPvnkE0eHAQAAnBAFSXMcmki6uLjct5RssViUlJSURREBAAAgvRyaSH777bd3PbZx40ZFR0crJSUlCyMCAADOiDWS5jg0kWzRokWqtgMHDqh///5atGiR2rVrp6FDhzogMgAAANxPtrjZRpL+/PNPvfLKK6pYsaKSkpK0c+dOzZkzRyEhIY4ODQAA5HAWi8Vuj5zM4Ynk5cuX1a9fP4WFhWnv3r1asWKFFi1apAoVKjg6NAAAgCy3Zs0aNW/eXMHBwbJYLFq4cKHNccMwNGjQIBUqVEheXl6KiIjQoUOHbPpcvHhR7dq1k4+Pj/z8/NS5c2dduXLFps/u3btVu3ZteXp6qkiRIho9enSGY3VoIjl69GgVL15cixcv1ueff64NGzaodu3ajgwJAAA4oey0IfnVq1f18MMPa/LkyWkeHz16tKKjozV16lRt2rRJ3t7eatiwoW7cuGHt065dO+3du1fLly/X4sWLtWbNGkVFRVmPJyQk6KmnnlJISIi2bdumMWPGaPDgwZo2bVrGrpthGEbGTzFzuLi4WDNpV1fXu/ZbsGBBhsYt0v27fxsagGzq0MTUa6sB5AyeDrxz45EhK+029o5365t+rcVi0bfffquWLVtKul2NDA4OVt++ffXmm29Kuj27W7BgQc2ePVtt27bV/v37Va5cOW3ZskXVqlWTJC1dulRNmjTRqVOnFBwcrClTpuidd95RXFyc3N3dJUn9+/fXwoUL9fvvv6c7PodWJNu3b682bdooICBAvr6+d30AAAA8qG7evKmEhASbx82bN02NdezYMcXFxSkiIsLa5uvrq+rVq2vjxo2Sbu984+fnZ00iJSkiIkIuLi7atGmTtU+dOnWsSaQkNWzYUAcOHNClS5fSHY9D79qePXu2I98eAABAkn03JB85cqSGDBli0/buu+9q8ODBGR4rLi5OklSwYEGb9oIFC1qPxcXFKTAw0Oa4m5ubAgICbPqEhoamGuPOMX9//3TFk20+2QYAACAnGjBggPr06WPT5uHh4aBoMheJJAAAcHr23KbHw8Mj0xLHoKAgSdKZM2dUqFAha/uZM2dUuXJla5+zZ8/avC4pKUkXL160vj4oKEhnzpyx6XPn+Z0+6eHw7X8AAACQPqGhoQoKCtKKFSusbQkJCdq0aZPCw8MlSeHh4YqPj9e2bdusfVauXKmUlBRVr17d2mfNmjVKTEy09lm+fLlKly6d7mltiUQSAAAgW23/c+XKFe3cuVM7d+6UdPsGm507d+rkyZOyWCzq3bu33nvvPX3//ffas2eP2rdvr+DgYOud3WXLllWjRo30yiuvaPPmzVq/fr169Oihtm3bKjg4WJL04osvyt3dXZ07d9bevXs1f/58TZw4MdUU/P0wtQ0AAJCNbN26VU888YT1+Z3kLjIyUrNnz9bbb7+tq1evKioqSvHx8apVq5aWLl0qT09P62tiYmLUo0cPNWjQQC4uLmrdurWio6Otx319fbVs2TJ1795dVatWVf78+TVo0CCbvSbTw6H7SNoL+0gCORf7SAI5lyP3kXx0+Gq7jb3lnXp2G9vRmNoGAACAKUxtAwAAp2fPfSRzMhJJAADg9Oy5/U9OxtQ2AAAATKEiCQAAnB4FSXOoSAIAAMAUKpIAAMDpsUbSHCqSAAAAMIWKJAAAcHoUJM2hIgkAAABTqEgCAACnxxpJc0gkAQCA0yOPNIepbQAAAJhCRRIAADg9prbNoSIJAAAAU6hIAgAAp0dF0hwqkgAAADCFiiQAAHB6FCTNoSIJAAAAU6hIAgAAp8caSXNIJAEAgNMjjzSHqW0AAACYQkUSAAA4Paa2zaEiCQAAAFOoSAIAAKdHQdIcKpIAAAAwhYokAABwei6UJE2hIgkAAABTqEgCAACnR0HSHBJJAADg9Nj+xxymtgEAAGAKFUkAAOD0XChImkJFEgAAAKZQkQQAAE6PNZLmUJEEAACAKVQkAQCA06MgaQ4VSQAAAJhCRRIAADg9iyhJmkEiCQAAnB7b/5jD1DYAAABMoSIJAACcHtv/mJOuRDIhISHdA/r4+JgOBgAAAA+OdCWSfn5+983UDcOQxWJRcnJypgQGAACQVShImpOuRHLVqlX2jgMAAAAPmHQlknXr1rV3HAAAAA7jQknSFNM321y7dk0nT57UrVu3bNorVar0r4MCAABA9pfhRPLcuXPq2LGjlixZkuZx1kgCAIAHDQVJczK8j2Tv3r0VHx+vTZs2ycvLS0uXLtWcOXNUsmRJff/99/aIEQAAwK4sFovdHjlZhiuSK1eu1Hfffadq1arJxcVFISEhevLJJ+Xj46ORI0eqadOm9ogTAAAA2UyGK5JXr15VYGCgJMnf31/nzp2TJFWsWFHbt2/P3OgAAACygMViv0dOluFEsnTp0jpw4IAk6eGHH9bHH3+s06dPa+rUqSpUqFCmBwgAAIDsKcNT26+//rpiY2MlSe+++64aNWqkmJgYubu7a/bs2ZkdHwAAgN2x/Y85GU4kX3rpJeufq1atqhMnTuj3339X0aJFlT9//kwNDgAAANmX6X0k78idO7eqVKmSGbEAAAA4BPVIczKcSHbq1Omex2fOnGk6GAAAADw4MpxIXrp0yeZ5YmKifvvtN8XHx6t+/fqZFhgAAEBWyen7PdpLhhPJb7/9NlVbSkqKunXrphIlSmRKUAAAAFnJhTzSlAxv/5PmIC4u6tOnj8aPH58ZwwEAAOAB8K9vtrnjyJEjSkpKyqzhAAAAsgxT2+ZkOJHs06ePzXPDMBQbG6sffvhBkZGRmRYYAAAAsrcMJ5I7duywee7i4qICBQpo7Nix972jGwAAIDuiIGlOhhPJVatW2SMOAAAAPGAyfLNN/fr1FR8fn6o9ISGB7X8AAMADyWKx2O2Rk2U4kVy9erVu3bqVqv3GjRtau3ZtpgQFAACA7C/dU9u7d++2/nnfvn2Ki4uzPk9OTtbSpUtVuHDhzI0OAAAgC7CPpDnpTiQrV65sLdGmNYXt5eWlSZMmZWpwAAAAWSGnT0HbS7oTyWPHjskwDBUvXlybN29WgQIFrMfc3d0VGBgoV1dXuwQJAACA7CfdiWRISIik25+17evrm2afw4cPKywsLHMiAwAAyCLUI83J8M02zZo1082bN1O1HzhwQPXq1cuMmAAAAPAAyHAimSdPHj3zzDM2H4e4f/9+1atXT61bt87U4AAAALKCi8Vit0dOluFEcsGCBbp8+bLatWsnwzD022+/qV69enrhhRc0ceJEe8QIAADgFJKTkzVw4ECFhobKy8tLJUqU0LBhw2QYhrWPYRgaNGiQChUqJC8vL0VEROjQoUM241y8eFHt2rWTj4+P/Pz81LlzZ125ciXT481wIunl5aUffvhBBw4cUJs2bdSgQQO1b99e48aNy/TgAAAAsoLFYr9HRrz//vuaMmWKPvzwQ+3fv1/vv/++Ro8ebbMzzujRoxUdHa2pU6dq06ZN8vb2VsOGDXXjxg1rn3bt2mnv3r1avny5Fi9erDVr1igqKiqzLpeVxfh7insXCQkJqdpiY2P15JNPqlmzZho1apS13cfHJ3MjNKFI9+8cHQIAOzk0sYWjQwBgJ54Z/uDmzPPKl7/ZbexP2lRId99mzZqpYMGCmjFjhrWtdevW8vLy0qeffirDMBQcHKy+ffvqzTfflCRdvnxZBQsW1OzZs9W2bVvt379f5cqV05YtW1StWjVJ0tKlS9WkSROdOnVKwcHBmXZu6apI+vn5yd/f3+ZRrlw5nTp1SlOnTpW/v7+1DwAAwIPGnh+RePPmTSUkJNg80rpxWZJq1KihFStW6ODBg5KkXbt2ad26dWrcuLGk29sxxsXFKSIiwvoaX19fVa9eXRs3bpQkbdy4UX5+ftYkUpIiIiLk4uKiTZs2Zep1S1fuv2rVqkx9UwAAAGcxcuRIDRkyxKbt3Xff1eDBg1P17d+/vxISElSmTBm5uroqOTlZw4cPV7t27STJ+smCBQsWtHldwYIFrcfi4uIUGBhoc9zNzU0BAQE2n0yYGdKVSNatWzddg/32m/3KwgAAAPZiz5urBwwYoD59+ti0eXh4pNn3yy+/VExMjD777DOVL19eO3fuVO/evRUcHKzIyEj7BWnSv16N8Ndff+nzzz/X9OnTtW3bNiUnJ2dGXAAAAFnGntv0eHh43DVx/Ke33npL/fv3V9u2bSVJFStW1IkTJzRy5EhFRkYqKChIknTmzBkVKlTI+rozZ86ocuXKkqSgoCCdPXvWZtykpCRdvHjR+vrMkuG7tu9Ys2aNIiMjVahQIX3wwQeqX7++fv3118yMDQAAwKlcu3ZNLi626Zmrq6tSUlIkSaGhoQoKCtKKFSusxxMSErRp0yaFh4dLksLDwxUfH69t27ZZ+6xcuVIpKSmqXr16psaboYpkXFycZs+erRkzZighIUFt2rTRzZs3tXDhQpUrVy5TAwMAAMgq2WXf8ObNm2v48OEqWrSoypcvrx07dmjcuHHq1KmTpNs3BfXu3VvvvfeeSpYsqdDQUA0cOFDBwcFq2bKlJKls2bJq1KiRXnnlFU2dOlWJiYnq0aOH2rZtm6l3bEsZSCSbN2+uNWvWqGnTppowYYIaNWokV1dXTZ06NVMDAgAAcFaTJk3SwIED9dprr+ns2bMKDg7Wq6++qkGDBln7vP3227p69aqioqIUHx+vWrVqaenSpfL09LT2iYmJUY8ePdSgQQO5uLiodevWio6OzvR407WPpHT7bp9evXqpW7duKlmypLU9V65c2rVrV7aqSLKPJJBzsY8kkHM5ch/J7t/ut9vYk58pa7exHS3dayTXrVunv/76S1WrVlX16tX14Ycf6vz58/aMDQAAANlYuiuSd1y9elXz58/XzJkztXnzZiUnJ1vn7vPmzWuvODPk6q0MnRKAB0j+6j0dHQIAO7m+40OHvXdPO1YkJ1GR/B9vb2916tRJ69at0549e9S3b1+NGjVKgYGBevrpp+0RIwAAALIh09v/SFLp0qU1evRonTp1Sp9//nlmxQQAAJCl7PkRiTlZpixrdXV1VcuWLa23nQMAADxIXHJ2vmc3/6oiCQAAAOflwBvtAQAAsgcqkuZQkQQAAIApVCQBAIDTy+k3xdhLuhLJ77//Pt0DsgUQAACAc0hXIpneu7EtFouSk5P/TTwAAABZjjWS5qQrkUxJSbF3HAAAAHjA/Ks1kjdu3JCnp2dmxQIAAOAQLJE0J8N3bScnJ2vYsGEqXLiw8uTJo6NHj0qSBg4cqBkzZmR6gAAAAPbmYrHY7ZGTZTiRHD58uGbPnq3Ro0fL3d3d2l6hQgVNnz49U4MDAABA9pXhRHLu3LmaNm2a2rVrJ1dXV2v7ww8/rN9//z1TgwMAAMgKLnZ85GQZPr/Tp08rLCwsVXtKSooSExMzJSgAAABkfxlOJMuVK6e1a9emav/666/1yCOPZEpQAAAAWclisd8jJ8vwXduDBg1SZGSkTp8+rZSUFC1YsEAHDhzQ3LlztXjxYnvECAAAgGwowxXJFi1aaNGiRfr555/l7e2tQYMGaf/+/Vq0aJGefPJJe8QIAABgV9y1bY6pfSRr166t5cuXZ3YsAAAAeICY3pB869at2r9/v6Tb6yarVq2aaUEBAABkpRxeOLSbDCeSp06d0gsvvKD169fLz89PkhQfH68aNWroiy++0EMPPZTZMQIAANgVn7VtTobXSHbp0kWJiYnav3+/Ll68qIsXL2r//v1KSUlRly5d7BEjAAAAsqEMVyR/+eUXbdiwQaVLl7a2lS5dWpMmTVLt2rUzNTgAAICskNNvirGXDFckixQpkubG48nJyQoODs6UoAAAAJD9ZTiRHDNmjHr27KmtW7da27Zu3arXX39dH3zwQaYGBwAAkBXYkNycdE1t+/v7y/K3K3H16lVVr15dbm63X56UlCQ3Nzd16tRJLVu2tEugAAAAyF7SlUhOmDDBzmEAAAA4Dndtm5OuRDIyMtLecQAAAOABY3pDckm6ceOGbt26ZdPm4+PzrwICAADIahZRkjQjw4nk1atX1a9fP3355Ze6cOFCquPJycmZEhgAAEBWYWrbnAzftf32229r5cqVmjJlijw8PDR9+nQNGTJEwcHBmjt3rj1iBAAAQDaU4YrkokWLNHfuXNWrV08dO3ZU7dq1FRYWppCQEMXExKhdu3b2iBMAAMBuqEiak+GK5MWLF1W8eHFJt9dDXrx4UZJUq1YtrVmzJnOjAwAAQLaV4USyePHiOnbsmCSpTJky+vLLLyXdrlT6+fllanAAAABZwWKx2O2Rk2U4kezYsaN27dolSerfv78mT54sT09PvfHGG3rrrbcyPUAAAABkTxleI/nGG29Y/xwREaHff/9d27ZtU1hYmCpVqpSpwQEAAGQF1kiak+GK5D+FhISoVatWCggIUFRUVGbEBAAAgAfAv04k77hw4YJmzJiRWcMBAABkGYvFfo+c7F99sg0AAEBO4JLTMz47ybSKJAAAAJwLFUkAAOD0uNnGnHQnkq1atbrn8fj4+H8bCwAAAB4g6U4kfX1973u8ffv2/zogAACArMYSSXPSnUjOmjXLnnEAAADgAcMaSQAA4PRcREnSDO7aBgAAgClUJAEAgNNjjaQ5JJIAAMDpsf2POUxtAwAAwBQqkgAAwOnxEYnmUJEEAACAKVQkAQCA06MgaQ4VSQAAAJhCRRIAADg91kiaQ0USAAAAplCRBAAATo+CpDkkkgAAwOkxRWsO1w0AAACmUJEEAABOz8LctilUJAEAAGAKFUkAAOD0qEeaQ0USAAAAplCRBAAATo8Nyc2hIgkAAABTqEgCAACnRz3SHBJJAADg9JjZNoepbQAAAJhCRRIAADg9NiQ3h4okAAAATCGRBAAATs/Fjo+MOn36tF566SXly5dPXl5eqlixorZu3Wo9bhiGBg0apEKFCsnLy0sRERE6dOiQzRgXL15Uu3bt5OPjIz8/P3Xu3FlXrlwxEc29kUgCAABkE5cuXVLNmjWVK1cuLVmyRPv27dPYsWPl7+9v7TN69GhFR0dr6tSp2rRpk7y9vdWwYUPduHHD2qddu3bau3evli9frsWLF2vNmjWKiorK9HgthmEYmT6qg129leNOCcD/y1+9p6NDAGAn13d86LD3/nLnn3Ybu03l4HT37d+/v9avX6+1a9emedwwDAUHB6tv37568803JUmXL19WwYIFNXv2bLVt21b79+9XuXLltGXLFlWrVk2StHTpUjVp0kSnTp1ScHD647kfKpIAAAB2dPPmTSUkJNg8bt68mWbf77//XtWqVdNzzz2nwMBAPfLII/rkk0+sx48dO6a4uDhFRERY23x9fVW9enVt3LhRkrRx40b5+flZk0hJioiIkIuLizZt2pSp50YiCQAAnJ7Fjo+RI0fK19fX5jFy5Mg04zh69KimTJmikiVL6qefflK3bt3Uq1cvzZkzR5IUFxcnSSpYsKDN6woWLGg9FhcXp8DAQJvjbm5uCggIsPbJLGz/AwAAYEcDBgxQnz59bNo8PDzS7JuSkqJq1appxIgRkqRHHnlEv/32m6ZOnarIyEi7x5pRVCQBAIDTs1gsdnt4eHjIx8fH5nG3RLJQoUIqV66cTVvZsmV18uRJSVJQUJAk6cyZMzZ9zpw5Yz0WFBSks2fP2hxPSkrSxYsXrX0yC4kkAABwetll+5+aNWvqwIEDNm0HDx5USEiIJCk0NFRBQUFasWKF9XhCQoI2bdqk8PBwSVJ4eLji4+O1bds2a5+VK1cqJSVF1atXz2BE98bUNgAAQDbxxhtvqEaNGhoxYoTatGmjzZs3a9q0aZo2bZqk25XT3r1767333lPJkiUVGhqqgQMHKjg4WC1btpR0u4LZqFEjvfLKK5o6daoSExPVo0cPtW3bNlPv2JZIJAEAALLNRyQ++uij+vbbbzVgwAANHTpUoaGhmjBhgtq1a2ft8/bbb+vq1auKiopSfHy8atWqpaVLl8rT09PaJyYmRj169FCDBg3k4uKi1q1bKzo6OtPjZR9JAA8U9pEEci5H7iP57e7MvZv5756plLnrErMTKpIAAMDpZY965IOHm20AAABgChVJAADg9LLJEskHDhVJAAAAmEJFEgAAOD0XVkmaQiIJAACcHlPb5jC1DQAAAFOoSAIAAKdnYWrbFCqSAAAAMIWKJAAAcHqskTSHiiQAAABMoSIJAACcHtv/mENFEgAAAKZQkQQAAE6PNZLmkEgCAACnRyJpDlPbAAAAMIWKJAAAcHpsSG6OwyqSAQEBOn/+vCSpU6dO+uuvvxwVCgAAAExwWCJ569YtJSQkSJLmzJmjGzduOCoUAADg5Fws9nvkZA6b2g4PD1fLli1VtWpVGYahXr16ycvLK82+M2fOzOLoAAAAcD8OSyQ//fRTjR8/XkeOHJHFYtHly5epSgIAAIdgjaQ5DkskCxYsqFGjRkmSQkNDNW/ePOXLl89R4QAAACCDssVd28eOHXN0CAAAwImxj6Q5Dksko6OjFRUVJU9PT0VHR9+zb69evbIoKgAA4IyY2jbHYhiG4Yg3Dg0N1datW5UvXz6FhobetZ/FYtHRo0czNPbVWw45JQBZIH/1no4OAYCdXN/xocPee/WBi3Ybu17pALuN7WgOq0j+fTqbqW0AAOBIOX2bHnvJFh+ROHToUF27di1V+/Xr1zV06FAHRAQAAID7yRaJ5JAhQ3TlypVU7deuXdOQIUMcEBEAAHAmFjv+l5Nli0TSMAxZ0rhdateuXQoIyLnrCgAAAB5kDt3+x9/fXxaLRRaLRaVKlbJJJpOTk3XlyhV17drVgRHCEWZO/1grf16u48eOysPTUw8//Ih6vdFXxUKLW/u8N2SQNv+6UefOnZVX7tz/3+dNhRYvbjPW9wsX6NO5s3XyxHF558mjiCcbacB/B2X1KQFOq2aVEnqjfYSqlCuqQgV81eaNaVq0erf1+DuvNtFzDavooSB/3UpM1o79JzX4w0Xa8tuJVGO553LTmnlv6uHSD6n68yO1++Bp6xj/7dokVf+r128qf42+9js55Chs/2OOQxPJCRMmyDAMderUSUOGDJGvr6/1mLu7u4oVK6bw8HAHRghH2LZ1i9q0fVHlK1RUcnKyPpw4Xq+92kXfLFwsr9y5JUlly5VX46bNVahQIV2+fFkfT/lQ3V/trEVLf5arq6sk6dM5szRv7iz17vOWKlR6WNevXVfsn6cdeWqA0/H28tCeg6c197uNmj8uKtXxwyfO6o33v9KxU+fl5ZFLPV+qr0Uf9VCFFkN0/pLtkqcRvVso9txlPVz6IZv2CXN/1vSv19q0/fhxL23bmzoZBZC5HJpIRkZGSrq9FVCNGjWUK1cuR4aDbGLy1Ok2z4e8N1IN6tbQvn17VbXao5Kk1s89bz0eXPghvdajt9o+20J//nlaRYoUVcLly/row4kaP2mKqj/+v3+MlCpdOmtOAoAkadn6fVq2ft9dj89futXmeb+xC9TxmRqqUDJYqzcftLY/VbOcGjxeVi+8NV2NapW3ec3V67d09fot6/OKpQqrXIlC6jX8i0w6CzgDCpLmOCyRTEhIkI+PjyTpkUce0fXr13X9+vU0+97pB+f015W/JMmmYv13169d0/cLF6hw4YcUFBQkSfp14walpKTo3NkzavV0E127elUPV35Eb7zVT0FBhbIsdgDpl8vNVZ1b1VT8X9e05+D/Zg8CA/Lqo4EvqE2fT3Ttbwnj3XR8poYOHj+j9TuO2DNc5DAuzG2b4rBE0t/fX7GxsQoMDJSfn1+aN9vcuQknOTn5ruPcvHlTN2/etGlLsrjLw8Mj02NG1ktJSdEH749Q5UeqKKxkKZtjX37xmSaO+0DXr19TsWKh+uiTmcqVy12SdPrUH0pJMTTzk4/1Zv//KE+evPpo0kS99konzV/wnbUfAMdrXLuC5o7qqNyeuRR3PkHNun6oC/FXrcenDX1Jn3y9Ttv3nVTRQve+AdPD3U3PN66msbOW2ztsAHJgIrly5UrrHdmrVq0yPc7IkSNTbRE04L+D9M7Awf8mPGQTo4YP1ZHDhzRzzmepjjVu2lyPh9fQuXPnNG/OTPXr21uz5n0uDw8PpRgpSkpK1FsD3lF4jVqSpJGjx+rJJ2ppy+ZNqlGzdlafCoC7+GXLQVVvO1L5/fKoY6sa+nR0J9V5+QOdu3RFr71QV3lze2rMzGXpGqtF/YeVN7enPl20yc5RI6ehHmmOwxLJunXrpvnnjBowYID69Olj05ZkodqUE4waPlRrf1mt6bM/VcH/n7L+u7x58ypv3rwqGlJMlR5+WHVrVteqFcvVqEkz5c9fQJJUvHiYtb9/QID8/PwVFxubZecA4P6u3bilo3+c19E/zmvznuPa890gRT5TQx/MXKZ6j5ZS9Uqhurxpgs1r1se8rS+WbNUrg+bZtHdoWUNL1v6msxf/ysIzAJyXQ2+2uWPp0qXKkyePatW6XTmaPHmyPvnkE5UrV06TJ0+Wv7//XV/r4eGRahqbz9p+sBmGofdHDNOqlT/rk5lzVfihh9Lxmtv/c+vW7fVTlR+pIkk6fvyYNQm9fDle8fGXVCg42G6xA/j3XCwWeeS6/ddT39Ffa/DkxdZjhQr4avGUHnq5/yxt2XPc5nUhwflU99GSerb3tKwMFzkFJUlTssWG5G+99ZYSEhIkSXv27FGfPn3UpEkTHTt2LFW1ETnfqOFD9eMPizRi1AfK7e2t8+fP6fz5c7px44Yk6dQff2jm9I+1b+9vio39U7t2btfbfV+Xh4eHatW+Xd0OKRaqek800Afvj9Cundt1+NBBDXqnv4qFFle1R6s78vQAp+Lt5a5KpQqrUqnCkqRihfOpUqnCKhLkr9ye7hrSo7keq1hMRQv565GyRTT13XYKDvTTguXbJUl/xF3SviOx1sehE2clSUf/OKfTZ+Nt3iuy5eOKO5+gn9bvzdJzBJxZtqhIHjt2TOXKlZMkffPNN2revLlGjBih7du3q0mT1JvMImf7av7nkqRXOrW3aR88bISebtlKHh7u2rFtmz6bN1cJCQnKly+fqlStplnzPldAvnzW/kNHvK+xo0eq12td5eJiUZVqj+nDqZ+wzRSQhaqUC9Gy6a9bn49+s7Ukad73v6rn8C9UulhBvdS8uvL5eevi5WvauveEIjqN1/6jcRl6H4vFopebP655329SSgqzUsi4nP5RhvZiMQzD4T9xAQEBWrduncqVK6datWqpffv2ioqK0vHjx1WuXDldu3YtQ+MxtQ3kXPmr93R0CADs5PqODx323puOXLbb2NVLpL19XU6QLSqStWrVUp8+fVSzZk1t3rxZ8+fPlyQdPHhQD6VjfRwAAMC/wTaS5mSLNZIffvih3Nzc9PXXX2vKlCkqXPj2WpolS5aoUaNGDo4OAADkdBY7PnKybDG1ndmY2gZyLqa2gZzLkVPbW47ab2r70eJMbdtdcnKyFi5cqP3790uSypcvr6efflqurq4OjgwAAOR4Ob10aCfZIpE8fPiwmjRpotOnT6t06dKSbn9iTZEiRfTDDz+oRIkSDo4QAAAA/5Qt1kj26tVLJUqU0B9//KHt27dr+/btOnnypEJDQ9WrVy9HhwcAAHI4ix3/y8myRUXyl19+0a+//mr97G1Jypcvn0aNGqWaNWs6MDIAAADcTbZIJD08PPTXX6k/F/XKlStyd+dzswEAgH2x/Y852WJqu1mzZoqKitKmTZtkGIYMw9Cvv/6qrl276umnn3Z0eAAAAEhDtkgko6OjFRYWpho1asjT01Oenp6qWbOmwsLCNHHiREeHBwAAcjj2kTTHoVPbKSkpGjNmjL7//nvdunVLLVu2VGRkpCwWi8qWLauwsDBHhgcAAJxFTs/47MShieTw4cM1ePBgRUREyMvLSz/++KN8fX01c+ZMR4YFAACAdHDo1PbcuXP10Ucf6aefftLChQu1aNEixcTEKCUlxZFhAQAAJ8P2P+Y4NJE8efKkmjRpYn0eEREhi8WiP//804FRAQAAID0cOrWdlJQkT09Pm7ZcuXIpMTHRQREBAABnxPY/5jg0kTQMQx06dJCHh4e17caNG+ratau8vb2tbQsWLHBEeAAAALgHhyaSkZGRqdpeeuklB0QCAACcGQVJcxyaSM6aNcuRbw8AAIB/IVt8RCIAAIBDUZI0hUQSAAA4vZy+TY+9ZIuPSAQAAMCDh4okAABwemz/Yw4VSQAAAJhCRRIAADg9CpLmUJEEAACAKVQkAQAAKEmaQkUSAAAAplCRBAAATo99JM2hIgkAAABTqEgCAACnxz6S5pBIAgAAp0ceaQ5T2wAAANnUqFGjZLFY1Lt3b2vbjRs31L17d+XLl0958uRR69atdebMGZvXnTx5Uk2bNlXu3LkVGBiot956S0lJSZkeH4kkAACAxY4Pk7Zs2aKPP/5YlSpVsml/4403tGjRIn311Vf65Zdf9Oeff6pVq1bW48nJyWratKlu3bqlDRs2aM6cOZo9e7YGDRpkPpi7IJEEAADIZq5cuaJ27drpk08+kb+/v7X98uXLmjFjhsaNG6f69euratWqmjVrljZs2KBff/1VkrRs2TLt27dPn376qSpXrqzGjRtr2LBhmjx5sm7dupWpcZJIAgAAp2ex4383b95UQkKCzePmzZv3jKd79+5q2rSpIiIibNq3bdumxMREm/YyZcqoaNGi2rhxoyRp48aNqlixogoWLGjt07BhQyUkJGjv3r2ZeNVIJAEAAOxq5MiR8vX1tXmMHDnyrv2/+OILbd++Pc0+cXFxcnd3l5+fn017wYIFFRcXZ+3z9yTyzvE7xzITd20DAACnZ8/tfwYMGKA+ffrYtHl4eKTZ948//tDrr7+u5cuXy9PT035BZRIqkgAAAHbk4eEhHx8fm8fdEslt27bp7NmzqlKlitzc3OTm5qZffvlF0dHRcnNzU8GCBXXr1i3Fx8fbvO7MmTMKCgqSJAUFBaW6i/vO8zt9MguJJAAAcHrZ5abtBg0aaM+ePdq5c6f1Ua1aNbVr187651y5cmnFihXW1xw4cEAnT55UeHi4JCk8PFx79uzR2bNnrX2WL18uHx8flStXLoMR3RtT2wAAANlkR/K8efOqQoUKNm3e3t7Kly+ftb1z587q06ePAgIC5OPjo549eyo8PFyPP/64JOmpp55SuXLl9PLLL2v06NGKi4vTf//7X3Xv3v2ulVCzSCQBAAAeIOPHj5eLi4tat26tmzdvqmHDhvroo4+sx11dXbV48WJ169ZN4eHh8vb2VmRkpIYOHZrpsVgMwzAyfVQHu3orx50SgP+Xv3pPR4cAwE6u7/jQYe996Mx1u41dsqCX3cZ2NNZIAgAAwBSmtgEAgNOz5/Y/ORkVSQAAAJhCRRIAADg9CpLmUJEEAACAKVQkAQAAKEmaQiIJAACcnoVM0hSmtgEAAGAKFUkAAOD02P7HHCqSAAAAMIWKJAAAcHoUJM2hIgkAAABTqEgCAABQkjSFiiQAAABMoSIJAACcHvtImkMiCQAAnB7b/5jD1DYAAABMoSIJAACcHgVJc6hIAgAAwBQqkgAAwOmxRtIcKpIAAAAwhYokAAAAqyRNoSIJAAAAU6hIAgAAp8caSXNIJAEAgNMjjzSHqW0AAACYQkUSAAA4Paa2zaEiCQAAAFOoSAIAAKdnYZWkKVQkAQAAYAoVSQAAAAqSplCRBAAAgClUJAEAgNOjIGkOiSQAAHB6bP9jDlPbAAAAMIWKJAAAcHps/2MOFUkAAACYQkUSAACAgqQpVCQBAABgChVJAADg9ChImkNFEgAAAKZQkQQAAE6PfSTNIZEEAABOj+1/zGFqGwAAAKZQkQQAAE6PqW1zqEgCAADAFBJJAAAAmEIiCQAAAFNYIwkAAJweayTNoSIJAAAAU6hIAgAAp8c+kuaQSAIAAKfH1LY5TG0DAADAFCqSAADA6VGQNIeKJAAAAEyhIgkAAEBJ0hQqkgAAADCFiiQAAHB6bP9jDhVJAAAAmEJFEgAAOD32kTSHiiQAAABMoSIJAACcHgVJc0gkAQAAyCRNYWobAAAAplCRBAAATo/tf8yhIgkAAABTqEgCAACnx/Y/5lCRBAAAgCkWwzAMRwcBmHXz5k2NHDlSAwYMkIeHh6PDAZCJ+PkGsj8SSTzQEhIS5Ovrq8uXL8vHx8fR4QDIRPx8A9kfU9sAAAAwhUQSAAAAppBIAgAAwBQSSTzQPDw89O6777IQH8iB+PkGsj9utgEAAIApVCQBAABgCokkAAAATCGRBAAAgCkkkoCkYsWKacKECdbncXFxevLJJ+Xt7S0/Pz+HxQXgf1avXi2LxaL4+Ph79uPnGcg6JJKwuw4dOshisWjUqFE27QsXLpTFYsnSWGbPnp3mXyRbtmxRVFSU9fn48eMVGxurnTt36uDBg1kYIfDgu/Mzb7FY5O7urrCwMA0dOlRJSUn/atwaNWooNjZWvr6+kvh5BrIDEklkCU9PT73//vu6dOmSo0NJU4ECBZQ7d27r8yNHjqhq1aoqWbKkAgMDHRgZ8GBq1KiRYmNjdejQIfXt21eDBw/WmDFj/tWY7u7uCgoKuu8/QPl5BrIOiSSyREREhIKCgjRy5Mi79lm3bp1q164tLy8vFSlSRL169dLVq1etx2NjY9W0aVN5eXkpNDRUn332WaoprHHjxqlixYry9vZWkSJF9Nprr+nKlSuSbk+LdezYUZcvX7ZWSwYPHizJdiqsWLFi+uabbzR37lxZLBZ16NAhsy8HkON5eHgoKChIISEh6tatmyIiIvT999/r0qVLat++vfz9/ZU7d241btxYhw4dsr7uxIkTat68ufz9/eXt7a3y5cvrxx9/lGQ7tc3PM5A9kEgiS7i6umrEiBGaNGmSTp06ler4kSNH1KhRI7Vu3Vq7d+/W/PnztW7dOvXo0cPap3379vrzzz+1evVqffPNN5o2bZrOnj1rM46Li4uio6O1d+9ezZkzRytXrtTbb78t6fa02IQJE+Tj46PY2FjFxsbqzTffTBXLli1b1KhRI7Vp00axsbGaOHFiJl8NwPl4eXnp1q1b6tChg7Zu3arvv/9eGzdulGEYatKkiRITEyVJ3bt3182bN7VmzRrt2bNH77//vvLkyZNqPH6egezBzdEBwHk888wzqly5st59913NmDHD5tjIkSPVrl079e7dW5JUsmRJRUdHq27dupoyZYqOHz+un3/+WVu2bFG1atUkSdOnT1fJkiVtxrnzeul2JeK9995T165d9dFHH8nd3V2+vr6yWCwKCgq6a5wFChSQh4eHvLy87tkPwP0ZhqEVK1bop59+UuPGjbVw4UKtX79eNWrUkCTFxMSoSJEiWrhwoZ577jmdPHlSrVu3VsWKFSVJxYsXT3Ncfp6B7IFEElnq/fffV/369VNVDnbt2qXdu3crJibG2mYYhlJSUnTs2DEdPHhQbm5uqlKlivV4WFiY/P39bcb5+eefNXLkSP3+++9KSEhQUlKSbty4oWvXrtmsmQJgX4sXL1aePHmUmJiolJQUvfjii2rVqpUWL16s6tWrW/vly5dPpUuX1v79+yVJvXr1Urdu3bRs2TJFRESodevWqlSpkqNOA8B9MLWNLFWnTh01bNhQAwYMsGm/cuWKXn31Ve3cudP62LVrlw4dOqQSJUqka+zjx4+rWbNmqlSpkr755htt27ZNkydPliTdunUr088FwN098cQT2rlzpw4dOqTr169rzpw56dqloUuXLjp69Khefvll7dmzR9WqVdOkSZOyIGIAZlCRRJYbNWqUKleurNKlS1vbqlSpon379iksLCzN15QuXVpJSUnasWOHqlatKkk6fPiwzV3g27ZtU0pKisaOHSsXl9v/Rvryyy9txnF3d1dycnJmnxKAf/D29k7181y2bFklJSVp06ZN1qntCxcu6MCBAypXrpy1X5EiRdS1a1d17dpVAwYM0CeffKKePXumeg9+ngHHoyKJLFexYkW1a9dO0dHR1rZ+/fppw4YN6tGjh7WK8d1331lvtilTpowiIiIUFRWlzZs3a8eOHYqKipKXl5e1yhEWFqbExERNmjRJR48e1bx58zR16lSb9y5WrJiuXLmiFStW6Pz587p27VrWnTjg5EqWLKkWLVrolVde0bp167Rr1y699NJLKly4sFq0aCHp9jrnn376SceOHdP27du1atUqlS1bNs3x+HkGHI9EEg4xdOhQpaSkWJ9XqlRJv/zyiw4ePKjatWvrkUce0aBBgxQcHGztM3fuXBUsWFB16tTRM888o1deeUV58+aVp6enJOnhhx/WuHHj9P7776tChQqKiYlJtd1QjRo11LVrVz3//PMqUKCARo8enTUnDECSNGvWLFWtWlXNmjVTeHi4DMPQjz/+qFy5ckmSkpOT1b17d5UtW1aNGjVSqVKl9NFHH6U5Fj/PgONZDMMwHB0EYMapU6dUpEgR/fzzz2rQoIGjwwEAwOmQSOKBsXLlSl25ckUVK1ZUbGys3n77bZ0+fVoHDx60VjMAAEDW4WYbPDASExP1n//8R0ePHlXevHlVo0YNxcTEkEQCAOAgVCQBAABgCjfbAAAAwBQSSQAAAJhCIgkAAABTSCQBAABgCokkAAAATCGRBGAXHTp0UMuWLa3P69Wrp969e1ufFytWTBMmTMiUsR0lo+cwePBgVa5c2W7xAEBWI5EEnEiHDh1ksVhksVjk7u6usLAwDR06VElJSXZ/7wULFmjYsGGZMtbEiRM1e/bsux5fvXq1LBaL/P39dePGDZtjW7ZssV4DAMC/QyIJOJlGjRopNjZWhw4dUt++fTV48GCNGTMmzb63bt3KtPcNCAhQ3rx5M2UsX19f+fn53bdf3rx59e2339q0zZgxQ0WLFs2UOADA2ZFIAk7Gw8NDQUFBCgkJUbdu3RQREaHvv/9e0v+mjIcPH67g4GCVLl1akvTHH3+oTZs28vPzU0BAgFq0aKHjx49bx0xOTlafPn3k5+enfPny6e2339Y/P+vgn1Pb/zR9+nT5+flpxYoVkqSvv/5aFStWlJeXl/Lly6eIiAhdvXrVJs77iYyM1MyZM63Pr1+/ri+++EKRkZGp+n7zzTcqX768PDw8VKxYMY0dO9bm+NmzZ9W8eXN5eXkpNDRUMTExqcaIj49Xly5dVKBAAfn4+Kh+/fratWvXfeMEgAcViSTg5Ly8vGwqjytWrNCBAwe0fPlyLV68WImJiWrYsKHy5s2rtWvXav369cqTJ48aNWpkfd3YsWM1e/ZszZw5U+vWrdPFixdTVQLvZfTo0erfv7+WLVumBg0aKDY2Vi+88II6deqk/fv3a/Xq1WrVqlWq5PR+Xn75Za1du1YnT56UdDtZLFasmKpUqWLTb9u2bWrTpo3atm2rPXv2aPDgwRo4cKDN9HmHDh30xx9/aNWqVfr666/10Ucf6ezZszbjPPfcczp79qyWLFmibdu2qUqVKmrQoIEuXryYobgB4EHBZ20DTsowDK1YsUI//fSTevbsaW339vbW9OnT5e7uLkn69NNPlZKSounTp1vXFc6aNUt+fn5avXq1nnrqKU2YMEEDBgxQq1atJElTp07VTz/9lK44+vXrp3nz5umXX35R+fLlJUmxsbFKSkpSq1atFBISIkmqWLFihs8xMDBQjRs31uzZszVo0CDNnDlTnTp1StVv3LhxatCggQYOHChJKlWqlPbt26cxY8aoQ4cOOnjwoJYsWaLNmzfr0UcflXR7irxs2bLWMdatW6fNmzfr7Nmz8vDwkCR98MEHWrhwob7++mtFRUVlOH4AyO5IJAEns3jxYuXJk0eJiYlKSUnRiy++qMGDB1uPV6xY0ZpEStKuXbt0+PDhVOsbb9y4oSNHjujy5cuKjY1V9erVrcfc3NxUrVq1+1YQx44dq6tXr2rr1q0qXry4tf3hhx9WgwYNVLFiRTVs2FBPPfWUnn32Wfn7+2f4fDt16qTXX39dL730kjZu3KivvvpKa9eutemzf/9+tWjRwqatZs2amjBhgpKTk7V//365ubmpatWq1uNlypSxWae5a9cuXblyRfny5bMZ5/r16zpy5EiG4waABwGJJOBknnjiCU2ZMkXu7u4KDg6Wm5vtrwFvb2+b51euXFHVqlXTXBNYoECBfxVL7dq19cMPP+jLL79U//79re2urq5avny5NmzYoGXLlmnSpEl65513tGnTJoWGhmboPRo3bqyoqCh17txZzZs3T5XoZZYrV66oUKFCWr16dapj6bkxCAAeRKyRBJyMt7e3wsLCVLRo0VRJZFqqVKmiQ4cOKTAwUGFhYTYPX19f+fr6qlChQtq0aZP1NUlJSdq2bdt9x37ssce0ZMkSjRgxQh988IHNMYvFopo1a2rIkCHasWOH3N3dM7Tu8g43Nze1b99eq1evTnNaW5LKli2r9evX27StX79epUqVkqurq8qUKZPqnA4cOKD4+Hjr8ypVqiguLk5ubm6prlP+/PkzHDcAPAhIJAHcU7t27ZQ/f361aNFCa9eu1bFjx7R69Wr16tVLp06dkiS9/vrrGjVqlBYuXKjff/9dr732mk2SdS81atTQjz/+qCFDhlg39960aZNGjBihrVu36uTJk1qwYIHOnTtnsyYxI4YNG6Zz586pYcOGaR7v27evVqxYoWHDhungwYOaM2eOPvzwQ7355puSpNKlS6tRo0Z69dVXtWnTJm3btk1dunSRl5eXdYyIiAiFh4erZcuWWrZsmY4fP64NGzbonXfe0datW03FDQDZHYkkgHvKnTu31qxZo6JFi6pVq1YqW7asOnfurBs3bsjHx0fS7UTs5ZdfVmRkpMLDw5U3b14988wz6X6PWrVq6YcfftB///tfTZo0ST4+PlqzZo2aNGmiUqVK6b///a/Gjh2rxo0bmzoHd3d35c+f/66bkFepUkVffvmlvvjiC1WoUEGDBg3S0KFD1aFDB2ufWbNmKTg4WHXr1lWrVq0UFRWlwMBA63GLxaIff/xRderUUceOHVWqVCm1bdtWJ06cUMGCBU3FDQDZncXI6H4aAAAAgKhIAgAAwCQSSQAAAJhCIgkAAABTSCQBAABgCokkAAAATCGRBAAAgCkkkgAAADCFRBIAAACmkEgCAADAFBJJAAAAmEIiCQAAAFP+D1bqIufmo9qLAAAAAElFTkSuQmCC\n"}, "metadata": {}}], "source": ["# ==============================================================================\n", "# SEL OPTIMASI HYPERPARAMETER XGBOOST (DENGAN PERBAIKAN)\n", "# ==============================================================================\n", "from sklearn.model_selection import RandomizedSearchCV\n", "\n", "# Pastikan data latih (X_train) sudah ada dari sel sebelumnya\n", "if 'X_train' in locals():\n", "    print(\"🚀 Me<PERSON>lai <PERSON>ian Hyperparameter Terbaik untuk XGBoost...\")\n", "\n", "    # 1. Tentukan rentang parameter yang ingin diuji\n", "    param_dist = {\n", "        'n_estimators': [100, 200, 300],\n", "        'learning_rate': [0.01, 0.1, 0.2],\n", "        'max_depth': [5, 10, 20, 30],\n", "        'subsample': [0.7, 0.8, 0.9],\n", "        'colsample_bytree': [0.7, 0.8, 0.9],\n", "        'gamma': [0, 1, 5]\n", "    }\n", "\n", "    # 2. Inisialisasi model dasar dan RandomizedSearchCV\n", "    xgb_base = XGBClassifier(use_label_encoder=False, eval_metric='logloss', random_state=42)\n", "    random_search = RandomizedSearchCV(\n", "        estimator=xgb_base,\n", "        param_distributions=param_dist,\n", "        n_iter=25,\n", "        cv=3,\n", "        verbose=1,\n", "        random_state=42,\n", "        n_jobs=-1\n", "    )\n", "\n", "    # 3. <PERSON><PERSON><PERSON> RandomizedSearchCV\n", "    random_search.fit(X_train, y_train)\n", "\n", "    # 4. Ambil model terb<PERSON>k\n", "    xgb_model_best = random_search.best_estimator_\n", "    print(\"\\nParameter terbaik yang ditemukan:\")\n", "    print(random_search.best_params_)\n", "    print(\"\\nModel XGBoost terbaik berhasil dilatih.\")\n", "    joblib.dump(xgb_model_best, 'model_xgboost_tuned.pkl')\n", "    print(\"Model terbaik telah disimpan.\")\n", "\n", "    # 5. <PERSON><PERSON><PERSON>n <PERSON> Overfitting pada model TERBAIK\n", "    print(\"\\n🔬 Mengevaluasi kinerja model TERBAIK...\")\n", "    y_pred_xgb_best = xgb_model_best.predict(X_test)\n", "    test_accuracy = accuracy_score(y_test, y_pred_xgb_best)\n", "\n", "    y_pred_train_xgb_best = xgb_model_best.predict(X_train)\n", "    train_accuracy = accuracy_score(y_train, y_pred_train_xgb_best)\n", "\n", "    print(\"\\nH<PERSON>l Evaluasi XGBoost (Setelah Tuning):\")\n", "    print(f\"Akurasi pada Data Latih: {train_accuracy * 100:.2f}%\")\n", "    print(f\"Akurasi pada Data Uji:   {test_accuracy * 100:.2f}%\")\n", "\n", "    print(\"\\nLaporan Klasifikasi:\")\n", "    print(classification_report(y_test, y_pred_xgb_best, target_names=le.classes_))\n", "\n", "    # --- <PERSON><PERSON><PERSON> YANG DITAMBAHKAN ---\n", "    print(\"\\n📊 <PERSON> (Confusion Matrix):\")\n", "    cm_xgb_best = confusion_matrix(y_test, y_pred_xgb_best)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm_xgb_best, annot=True, fmt='d', cmap='Blues',\n", "                xticklabels=le.classes_, yticklabels=le.classes_)\n", "    plt.xlabel('Prediksi Model')\n", "    plt.ylabel('Label Aktual')\n", "    plt.title('Confusion Matrix - XGBoost (<PERSON><PERSON><PERSON>)')\n", "    plt.show()\n", "    # --- <PERSON><PERSON><PERSON> KODE TAMBAHAN ---\n", "\n", "else:\n", "    print(\"⚠️ Data latih tidak ditemukan. Jalankan sel sebelumnya terlebih dahulu.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 988, "status": "ok", "timestamp": 1753022124584, "user": {"displayName": "<PERSON><PERSON>", "userId": "16161035047161334135"}, "user_tz": -420}, "id": "RAVjkKf7LH6n", "outputId": "98121bbe-fc10-4308-de4a-0956514b8e6a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🔬 Memeriksa Overfitting pada Model Terbaik...\n", "\n", "--- <PERSON><PERSON><PERSON><PERSON> ---\n", "Akurasi pada Data Latih: 95.22%\n", "Akurasi pada Data Uji:   84.35%\n", "\n", "<PERSON><PERSON><PERSON>: 10.87%\n", "Kesimpulan: <PERSON><PERSON><PERSON><PERSON> indikasi overfitting, meskipun mungkin sudah berkurang.\n"]}], "source": ["# ==============================================================================\n", "# SEL UNTUK CEK OVERFITTING PADA MODEL TERBAIK (SETELAH TUNING)\n", "# ==============================================================================\n", "\n", "# Pastikan model te<PERSON><PERSON><PERSON> sudah ada dari sel sebelumnya\n", "# Ganti 'xgb_model_best' dengan nama variabel model terb<PERSON><PERSON> <PERSON><PERSON> jika berbeda\n", "# (misalnya 'rf_model_best' atau 'random_search.best_estimator_')\n", "if 'xgb_model_best' in locals():\n", "    print(\"🔬 Memeriksa Overfitting pada Model Terbaik...\")\n", "\n", "    # 1. La<PERSON><PERSON> prediksi pada DATA LATIH\n", "    y_pred_train = xgb_model_best.predict(X_train)\n", "    train_accuracy = accuracy_score(y_train, y_pred_train)\n", "\n", "    # 2. <PERSON><PERSON><PERSON> prediksi pada DATA UJI\n", "    y_pred_test = xgb_model_best.predict(X_test)\n", "    test_accuracy = accuracy_score(y_test, y_pred_test)\n", "\n", "    # 3. <PERSON><PERSON><PERSON><PERSON>\n", "    print(\"\\n--- <PERSON><PERSON><PERSON><PERSON> ---\")\n", "    print(f\"Akurasi pada Data Latih: {train_accuracy * 100:.2f}%\")\n", "    print(f\"Akurasi pada Data Uji:   {test_accuracy * 100:.2f}%\")\n", "\n", "    # 4. <PERSON><PERSON><PERSON> interpretasi\n", "    selisih = train_accuracy - test_accuracy\n", "    print(f\"\\nSelisih Performa: {selisih * 100:.2f}%\")\n", "\n", "    if selisih > 0.05: # <PERSON>as wajar selisih adalah sekitar 5%\n", "        print(\"Kesimpulan: Terdapat indikasi overfitting, meskipun mungkin sudah berkurang.\")\n", "    else:\n", "        print(\"Kesimpulan: ✅ Model memiliki general<PERSON>si yang baik (tidak ada indikasi overfitting yang kuat).\")\n", "\n", "else:\n", "    print(\"⚠️ Variabel model terbaik (misal: 'xgb_model_best') tidak ditemukan.\")\n", "    print(\"Pastikan Anda sudah menjalankan sel optimasi hyperparameter terlebih dahulu.\")"]}], "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyO7Ugf7D4tbOnsjIdvJxy1B"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}