# Test Model Script - Testing Sentiment Analysis Model
# Script untuk menguji model analisis sentimen yang sudah dilatih

import pandas as pd
import joblib
import re
from Sastrawi.Stemmer.StemmerFactory import StemmerFactory
import nltk
from nltk.corpus import stopwords
import warnings

warnings.filterwarnings('ignore')

print("🧪 Starting Model Testing Script...")

# ==============================================================================
# LOAD MODEL DAN VECTORIZER
# ==============================================================================

try:
    # Load model terbaik
    model = joblib.load('best_model.pkl')
    print("✅ Best model loaded successfully")
    
    # Load TF-IDF vectorizer
    vectorizer = joblib.load('tfidf_vectorizer_tuned.pkl')
    print("✅ TF-IDF vectorizer loaded successfully")
    
except FileNotFoundError as e:
    print(f"❌ Error: Required model files not found: {e}")
    print("Please run the olah_data1_script.py first to train the models.")
    exit()

# ==============================================================================
# SETUP PREPROCESSING FUNCTIONS
# ==============================================================================

# Setup stemmer
factory = StemmerFactory()
stemmer = factory.create_stemmer()

# Setup stopwords
try:
    list_stopwords = set(stopwords.words('indonesian'))
except LookupError:
    print("Downloading NLTK stopwords...")
    nltk.download('stopwords')
    list_stopwords = set(stopwords.words('indonesian'))

# Text normalization dictionary
kamus_normalisasi = {
    'yg': 'yang', 'ga': 'tidak', 'gak': 'tidak', 'kalo': 'kalau', 'udah': 'sudah',
    'bgt': 'banget', 'banget': 'sekali', 'jg': 'juga', 'aja': 'saja', 'ok': 'oke',
    'bikin': 'membuat', 'gini': 'begini', 'gitu': 'begitu', 'byk': 'banyak',
    'udh': 'sudah', 'krn': 'karena', 'dlm': 'dalam', 'hrs': 'harus', 'jd': 'jadi',
    'bbrp': 'beberapa', 'sy': 'saya', 'brg': 'barang', 'hrsnya': 'seharusnya',
    'tks': 'terima kasih', 'mksh': 'terima kasih', 'trmksh': 'terima kasih',
    'dg': 'dengan', 'sdh': 'sudah', 'tdk': 'tidak', 'jgk': 'juga', 'mnrt': 'menurut',
    'tp': 'tapi', 'sm': 'sama', 'gk': 'tidak', 'gmn': 'bagaimana', 'gt': 'begitu',
    'bkn': 'bukan', 'srg': 'sering', 'pdhl': 'padahal', 'ato': 'atau', 'btw': 'omong-omong',
    'gpp': 'tidak apa-apa', 'blg': 'bilang', 'mlh': 'malah',
    'sbnrnya': 'sebenarnya', 'tgl': 'tanggal', 'dgn': 'dengan', 'ktnya': 'katanya',
    'skrg': 'sekarang', 'bknnya': 'bukannya', 'klo': 'kalau', 'bgs': 'bagus', 'bnyk': 'banyak',
    'bgtu': 'begitu', 'tlt': 'telat', 'kl': 'kalau', 'd': 'di', 'dr': 'dari',
    'trs': 'terus', 'mntp': 'mantap', 'bgus': 'bagus',
    'lgsg': 'langsung', 'pake': 'pakai', 'msh': 'masih',
    'cpt': 'cepat', 'lbih': 'lebih', 'brmasalah': 'bermasalah',
    'aplikasinya': 'aplikasi', 'gobiznya': 'gobiz',
    'akunnya': 'akun', 'orderannya': 'order', 'fitur': 'fitur',
    'utk': 'untuk', 'hr': 'hari'
}

def preprocess_text_complete(text):
    """Complete text preprocessing pipeline"""
    if not text or pd.isna(text):
        return ""
    
    # 1. Case Folding (Convert to lowercase)
    text = text.lower()
    
    # 2. Remove usernames (@username)
    text = re.sub(r'@[A-Za-z0-9_]+', '', text)
    
    # 3. Remove URLs
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    
    # 4. Remove hashtags (#hashtag)
    text = re.sub(r'#\w+', '', text)
    
    # 5. Remove non-alphabetic characters (except letters) and numbers
    text = re.sub(r'[^a-zA-Z\s]', '', text)  # Only keep letters and spaces
    
    # 6. Remove extra spaces
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 7. Text normalization
    words = text.split()
    normalized_words = [kamus_normalisasi.get(word, word) for word in words]
    text = ' '.join(normalized_words)
    
    # 8. Remove stopwords
    words = text.split()
    filtered_words = [word for word in words if word not in list_stopwords]
    text = ' '.join(filtered_words)
    
    # 9. Stemming
    if text:
        text = stemmer.stem(text)
    
    return text

def predict_sentiment(text):
    """Predict sentiment for a given text"""
    # Preprocess the text
    processed_text = preprocess_text_complete(text)
    
    if not processed_text:
        return "Unknown", 0.0
    
    # Transform to TF-IDF vector
    text_vector = vectorizer.transform([processed_text]).toarray()
    
    # Predict
    prediction = model.predict(text_vector)[0]
    probability = model.predict_proba(text_vector)[0]
    
    # Convert prediction to label
    sentiment_label = "Positif" if prediction == 1 else "Negatif"
    confidence = max(probability) * 100
    
    return sentiment_label, confidence

# ==============================================================================
# TEST DENGAN CONTOH TEKS
# ==============================================================================

print("\n🧪 Testing model with sample texts...")
print("="*60)

# Sample test texts
test_texts = [
    "Aplikasi GoFood sangat bagus dan mudah digunakan!",
    "Pelayanan buruk sekali, saya tidak puas",
    "Makanan enak tapi pengiriman lambat",
    "Terima kasih GoFood, orderan cepat sampai",
    "Aplikasi sering error dan tidak bisa login",
    "Sangat rekomended untuk semua orang",
    "Harga mahal tapi kualitas bagus",
    "Tidak ada notifikasi ketika pesanan datang",
    "Driver ramah dan makanan masih hangat",
    "Sistem pembayaran bermasalah terus"
]

print("Testing sentiment analysis on sample texts:\n")

for i, text in enumerate(test_texts, 1):
    sentiment, confidence = predict_sentiment(text)
    print(f"{i:2d}. Text: {text}")
    print(f"    Prediction: {sentiment} (Confidence: {confidence:.1f}%)")
    print()

# ==============================================================================
# INTERACTIVE TESTING
# ==============================================================================

print("\n🎯 Interactive Testing Mode")
print("="*60)
print("Enter your own text to test the sentiment analysis model.")
print("Type 'quit' to exit.\n")

while True:
    user_input = input("Enter text to analyze: ").strip()
    
    if user_input.lower() in ['quit', 'exit', 'q']:
        print("👋 Goodbye!")
        break
    
    if not user_input:
        print("Please enter some text.\n")
        continue
    
    sentiment, confidence = predict_sentiment(user_input)
    print(f"Sentiment: {sentiment} (Confidence: {confidence:.1f}%)\n")

print("\n🎉 Model testing completed!")
