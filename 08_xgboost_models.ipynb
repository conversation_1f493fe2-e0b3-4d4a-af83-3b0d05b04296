{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚀 08. XGBoost Model Training & Optimization\n", "\n", "**Tujuan**: Training dan optimasi XGBoost models untuk sentiment analysis\n", "\n", "**Key Features**:\n", "1. 🔄 **Data Integration** - Load features dan labels\n", "2. 🚀 **XGBoost Training** - Multiple configurations\n", "3. 🎯 **Advanced Tuning** - Bayesian optimization & early stopping\n", "4. 📊 **Model Evaluation** - Comprehensive metrics\n", "5. 🔍 **Feature Importance** - SHAP values & gain analysis\n", "6. ⚖️ **Model Comparison** - vs Random Forest\n", "\n", "**Input**: Feature matrices + labeled datasets  \n", "**Output**: Optimized XGBoost models dengan evaluasi\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "from datetime import datetime\n", "import json\n", "import joblib\n", "import os\n", "\n", "# XGBoost and machine learning libraries\n", "import xgboost as xgb\n", "from sklearn.model_selection import (\n", "    train_test_split, GridSearchCV, RandomizedSearchCV, \n", "    cross_val_score, StratifiedKFold\n", ")\n", "from sklearn.metrics import (\n", "    classification_report, confusion_matrix, accuracy_score,\n", "    precision_recall_fscore_support, roc_auc_score, roc_curve,\n", "    precision_recall_curve, matthews_corrcoef, cohen_kappa_score\n", ")\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.utils.class_weight import compute_class_weight\n", "import scipy.stats as stats\n", "\n", "# Advanced optimization\n", "try:\n", "    from skopt import BayesSearchCV\n", "    from skopt.space import Real, Integer, Categorical\n", "    BAYESIAN_AVAILABLE = True\n", "except ImportError:\n", "    print(\"⚠️ scikit-optimize not available. Using standard optimization.\")\n", "    BAYESIAN_AVAILABLE = False\n", "\n", "# SHAP for feature importance (optional)\n", "try:\n", "    import shap\n", "    SHAP_AVAILABLE = True\n", "except ImportError:\n", "    print(\"⚠️ SHAP not available. Using standard feature importance.\")\n", "    SHAP_AVAILABLE = False\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "np.random.seed(42)\n", "\n", "print(f\"🚀 XGBoost training libraries imported successfully!\")\n", "print(f\"  • XGBoost version: {xgb.__version__}\")\n", "print(f\"  • Bayesian optimization: {'Available' if BAYESIAN_AVAILABLE else 'Not available'}\")\n", "print(f\"  • SHAP analysis: {'Available' if SHAP_AVAILABLE else 'Not available'}\")\n", "print(f\"🕐 XGBoost training started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Load Data & Features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load feature matrices and labels (reuse from Random Forest)\n", "print(\"📂 LOADING DATA & FEATURES\")\n", "print(\"=\" * 50)\n", "\n", "# Load TF-IDF features\n", "try:\n", "    X_features = joblib.load('data/features/X_optimized.pkl')\n", "    vectorizer = joblib.load('models/vectorizers/tfidf_optimized.pkl')\n", "    print(f\"✅ Optimized TF-IDF features loaded: {X_features.shape}\")\n", "    feature_config = 'optimized'\n", "except FileNotFoundError:\n", "    try:\n", "        X_features = joblib.load('data/features/X_unigram.pkl')\n", "        vectorizer = joblib.load('models/vectorizers/tfidf_unigram.pkl')\n", "        print(f\"✅ Unigram TF-IDF features loaded: {X_features.shape}\")\n", "        feature_config = 'unigram'\n", "    except FileNotFoundError:\n", "        print(\"❌ No feature matrices found. Please run feature extraction first.\")\n", "        raise FileNotFoundError(\"Feature matrices required for model training\")\n", "\n", "# Load labeled datasets\n", "score_based_data = None\n", "lexicon_based_data = None\n", "\n", "try:\n", "    score_based_data = pd.read_excel('data/labeled/gofood_score_based_labeled.xlsx')\n", "    print(f\"✅ Score-based labels loaded: {len(score_based_data)} samples\")\n", "except FileNotFoundError:\n", "    print(\"⚠️ Score-based labels not found\")\n", "\n", "try:\n", "    lexicon_based_data = pd.read_excel('data/labeled/gofood_lexicon_based_labeled.xlsx')\n", "    print(f\"✅ Lexicon-based labels loaded: {len(lexicon_based_data)} samples\")\n", "except FileNotFoundError:\n", "    print(\"⚠️ Lexicon-based labels not found\")\n", "\n", "# Determine labeling method (same logic as <PERSON> Forest)\n", "if score_based_data is not None and len(score_based_data) == X_features.shape[0]:\n", "    primary_data = score_based_data\n", "    label_column = 'sentiment_label'\n", "    numeric_column = 'sentiment_numeric'\n", "    labeling_method = 'score_based'\n", "    print(f\"🎯 Using score-based labels as primary\")\n", "elif lexicon_based_data is not None and len(lexicon_based_data) == X_features.shape[0]:\n", "    primary_data = lexicon_based_data\n", "    label_column = 'lexicon_sentiment_label'\n", "    numeric_column = 'lexicon_sentiment_numeric'\n", "    labeling_method = 'lexicon_based'\n", "    print(f\"🎯 Using lexicon-based labels as primary\")\n", "else:\n", "    # Create sample data\n", "    print(\"📝 Creating sample labeled data...\")\n", "    n_samples = X_features.shape[0]\n", "    sample_labels = np.random.choice(['Positive', 'Negative', 'Neutral'], n_samples)\n", "    le = LabelEncoder()\n", "    sample_numeric = le.fit_transform(sample_labels)\n", "    \n", "    primary_data = pd.DataFrame({\n", "        'sentiment_label': sample_labels,\n", "        'sentiment_numeric': sample_numeric\n", "    })\n", "    label_column = 'sentiment_label'\n", "    numeric_column = 'sentiment_numeric'\n", "    labeling_method = 'sample'\n", "    print(f\"📝 Sample data created: {len(primary_data)} samples\")\n", "\n", "# Extract labels\n", "y_labels = primary_data[label_column].values\n", "y_numeric = primary_data[numeric_column].values\n", "\n", "# Convert sparse matrix to dense for XGBoost\n", "if hasattr(X_features, 'toarray'):\n", "    X_dense = X_features.toarray()\n", "else:\n", "    X_dense = X_features\n", "\n", "print(f\"\\n📊 Dataset Summary:\")\n", "print(f\"  • Features shape: {X_dense.shape}\")\n", "print(f\"  • Labels: {len(y_labels)}\")\n", "print(f\"  • Labeling method: {labeling_method}\")\n", "print(f\"  • Feature config: {feature_config}\")\n", "print(f\"  • Classes: {np.unique(y_labels)}\")\n", "print(f\"  • Class distribution: {dict(zip(*np.unique(y_labels, return_counts=True)))}\")\n", "\n", "# Use same data split as Random Forest for fair comparison\n", "try:\n", "    # Try to load Random Forest results for consistent splitting\n", "    with open('data/results/random_forest_results.json', 'r') as f:\n", "        rf_results = json.load(f)\n", "    \n", "    # Use same random state and split ratios\n", "    X_temp, X_test, y_temp, y_test = train_test_split(\n", "        X_dense, y_numeric, test_size=0.2, random_state=42, stratify=y_numeric\n", "    )\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp\n", "    )\n", "    print(f\"✅ Using consistent data split with Random Forest\")\n", "    \n", "except FileNotFoundError:\n", "    # Create new split\n", "    X_temp, X_test, y_temp, y_test = train_test_split(\n", "        X_dense, y_numeric, test_size=0.2, random_state=42, stratify=y_numeric\n", "    )\n", "    X_train, X_val, y_train, y_val = train_test_split(\n", "        X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp\n", "    )\n", "    print(f\"✅ Created new data split\")\n", "\n", "print(f\"\\n🔀 Data Split:\")\n", "print(f\"  • Training: {X_train.shape[0]} samples\")\n", "print(f\"  • Validation: {X_val.shape[0]} samples\")\n", "print(f\"  • Test: {X_test.shape[0]} samples\")\n", "\n", "# Analyze class distribution for XGBoost configuration\n", "unique_labels, label_counts = np.unique(y_labels, return_counts=True)\n", "class_distribution = dict(zip(unique_labels, label_counts))\n", "imbalance_ratio = max(label_counts) / min(label_counts)\n", "use_class_weights = imbalance_ratio > 2.0\n", "\n", "print(f\"\\n⚖️ Class Balance Analysis:\")\n", "print(f\"  • Imbalance ratio: {imbalance_ratio:.2f}:1\")\n", "print(f\"  • Use scale_pos_weight: {'Yes' if use_class_weights else 'No'}\")\n", "\n", "if use_class_weights and len(unique_labels) == 2:\n", "    # Calculate scale_pos_weight for binary classification\n", "    neg_count = (y_train == 0).sum()\n", "    pos_count = (y_train == 1).sum()\n", "    scale_pos_weight = neg_count / pos_count if pos_count > 0 else 1.0\n", "    print(f\"  • Scale pos weight: {scale_pos_weight:.2f}\")\n", "else:\n", "    scale_pos_weight = 1.0\n", "\n", "print(f\"\\n✅ Data preparation completed for XGBoost!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 Baseline XGBoost Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train baseline XGBoost model\n", "print(\"🚀 BASELINE XGBOOST MODEL\")\n", "print(\"=\" * 50)\n", "\n", "# Define baseline parameters\n", "baseline_params = {\n", "    'objective': 'multi:softprob' if len(unique_labels) > 2 else 'binary:logistic',\n", "    'num_class': len(unique_labels) if len(unique_labels) > 2 else None,\n", "    'max_depth': 6,\n", "    'learning_rate': 0.1,\n", "    'n_estimators': 100,\n", "    'subsample': 0.8,\n", "    'colsample_bytree': 0.8,\n", "    'random_state': 42,\n", "    'n_jobs': -1,\n", "    'eval_metric': 'mlogloss' if len(unique_labels) > 2 else 'logloss',\n", "    'scale_pos_weight': scale_pos_weight if len(unique_labels) == 2 else None\n", "}\n", "\n", "# Remove None values\n", "baseline_params = {k: v for k, v in baseline_params.items() if v is not None}\n", "\n", "print(f\"\\n🔧 Baseline Parameters:\")\n", "for param, value in baseline_params.items():\n", "    print(f\"  • {param}: {value}\")\n", "\n", "# Train baseline model with early stopping\n", "print(f\"\\n🔄 Training baseline model with early stopping...\")\n", "start_time = datetime.now()\n", "\n", "xgb_baseline = xgb.XGBClassifier(**baseline_params)\n", "\n", "# Fit with early stopping\n", "xgb_baseline.fit(\n", "    X_train, y_train,\n", "    eval_set=[(X_train, y_train), (X_val, y_val)],\n", "    eval_names=['train', 'val'],\n", "    early_stopping_rounds=10,\n", "    verbose=False\n", ")\n", "\n", "training_time = (datetime.now() - start_time).total_seconds()\n", "print(f\"✅ Training completed in {training_time:.2f} seconds\")\n", "print(f\"  • Best iteration: {xgb_baseline.best_iteration}\")\n", "print(f\"  • Best score: {xgb_baseline.best_score:.4f}\")\n", "\n", "# Make predictions\n", "print(f\"\\n📊 Making predictions...\")\n", "y_train_pred = xgb_baseline.predict(X_train)\n", "y_val_pred = xgb_baseline.predict(X_val)\n", "y_test_pred = xgb_baseline.predict(X_test)\n", "\n", "# Get prediction probabilities\n", "y_train_proba = xgb_baseline.predict_proba(X_train)\n", "y_val_proba = xgb_baseline.predict_proba(X_val)\n", "y_test_proba = xgb_baseline.predict_proba(X_test)\n", "\n", "# For binary classification, extract positive class probabilities\n", "if len(unique_labels) == 2:\n", "    y_train_proba_pos = y_train_proba[:, 1]\n", "    y_val_proba_pos = y_val_proba[:, 1]\n", "    y_test_proba_pos = y_test_proba[:, 1]\n", "else:\n", "    y_train_proba_pos = y_train_proba\n", "    y_val_proba_pos = y_val_proba\n", "    y_test_proba_pos = y_test_proba\n", "\n", "# Evaluation function (reuse from Random Forest)\n", "def evaluate_model(y_true, y_pred, y_proba=None, set_name=\"\"):\n", "    \"\"\"Comprehensive model evaluation\"\"\"\n", "    results = {}\n", "    \n", "    # Basic metrics\n", "    results['accuracy'] = accuracy_score(y_true, y_pred)\n", "    results['precision'], results['recall'], results['f1'], _ = precision_recall_fscore_support(\n", "        y_true, y_pred, average='weighted'\n", "    )\n", "    \n", "    # Additional metrics\n", "    results['kappa'] = cohen_kappa_score(y_true, y_pred)\n", "    results['mcc'] = matthews_corrcoef(y_true, y_pred)\n", "    \n", "    # ROC AUC for binary classification\n", "    if len(np.unique(y_true)) == 2 and y_proba is not None:\n", "        if len(y_proba.shape) > 1 and y_proba.shape[1] > 1:\n", "            results['roc_auc'] = roc_auc_score(y_true, y_proba[:, 1])\n", "        else:\n", "            results['roc_auc'] = roc_auc_score(y_true, y_proba)\n", "    \n", "    return results\n", "\n", "# Evaluate baseline model\n", "train_results = evaluate_model(y_train, y_train_pred, y_train_proba, \"Train\")\n", "val_results = evaluate_model(y_val, y_val_pred, y_val_proba, \"Validation\")\n", "test_results = evaluate_model(y_test, y_test_pred, y_test_proba, \"Test\")\n", "\n", "print(f\"\\n📈 BASELINE XGBOOST PERFORMANCE:\")\n", "print(f\"{'Metric':<12} {'Train':<8} {'Val':<8} {'Test':<8}\")\n", "print(\"-\" * 40)\n", "\n", "for metric in ['accuracy', 'precision', 'recall', 'f1', 'kappa', 'mcc']:\n", "    train_val = train_results.get(metric, 0)\n", "    val_val = val_results.get(metric, 0)\n", "    test_val = test_results.get(metric, 0)\n", "    print(f\"{metric.capitalize():<12} {train_val:<8.3f} {val_val:<8.3f} {test_val:<8.3f}\")\n", "\n", "if 'roc_auc' in val_results:\n", "    print(f\"{'ROC AUC':<12} {train_results['roc_auc']:<8.3f} {val_results['roc_auc']:<8.3f} {test_results['roc_auc']:<8.3f}\")\n", "\n", "# Check for overfitting\n", "train_val_diff = train_results['accuracy'] - val_results['accuracy']\n", "print(f\"\\n🔍 Overfitting Analysis:\")\n", "print(f\"  • Train-Val accuracy difference: {train_val_diff:.3f}\")\n", "if train_val_diff > 0.1:\n", "    print(f\"  • ⚠️ Potential overfitting detected\")\n", "elif train_val_diff > 0.05:\n", "    print(f\"  • ⚠️ Mild overfitting\")\n", "else:\n", "    print(f\"  • ✅ Good generalization\")\n", "\n", "# Feature importance analysis\n", "print(f\"\\n🔍 Feature Importance Analysis:\")\n", "feature_names = vectorizer.get_feature_names_out()\n", "feature_importance = xgb_baseline.feature_importances_\n", "\n", "# Get top important features\n", "top_indices = np.argsort(feature_importance)[-20:][::-1]\n", "top_features = [(feature_names[i], feature_importance[i]) for i in top_indices]\n", "\n", "print(f\"  • Top 10 Most Important Features:\")\n", "for i, (feature, importance) in enumerate(top_features[:10]):\n", "    print(f\"    {i+1:2d}. {feature:<20} : {importance:.4f}\")\n", "\n", "# Store baseline results\n", "baseline_results = {\n", "    'model': xgb_baseline,\n", "    'params': baseline_params,\n", "    'training_time': training_time,\n", "    'best_iteration': xgb_baseline.best_iteration,\n", "    'best_score': xgb_baseline.best_score,\n", "    'train_metrics': train_results,\n", "    'val_metrics': val_results,\n", "    'test_metrics': test_results,\n", "    'feature_importance': dict(zip(feature_names, feature_importance)),\n", "    'top_features': top_features\n", "}\n", "\n", "print(f\"\\n✅ Baseline XGBoost evaluation completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Advanced Hyperparameter Tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced hyperparameter tuning for XGBoost\n", "print(\"🎯 ADVANCED HYPERPARAMETER TUNING\")\n", "print(\"=\" * 50)\n", "\n", "# Define parameter spaces for different optimization methods\n", "# Grid Search - focused search\n", "grid_params = {\n", "    'max_depth': [3, 4, 5, 6],\n", "    'learning_rate': [0.01, 0.1, 0.2],\n", "    'n_estimators': [100, 200, 300],\n", "    'subsample': [0.8, 0.9, 1.0],\n", "    'colsample_bytree': [0.8, 0.9, 1.0]\n", "}\n", "\n", "# Random Search - broader exploration\n", "random_params = {\n", "    'max_depth': stats.randint(3, 10),\n", "    'learning_rate': stats.uniform(0.01, 0.3),\n", "    'n_estimators': stats.randint(50, 500),\n", "    'subsample': stats.uniform(0.6, 0.4),\n", "    'colsample_bytree': stats.uniform(0.6, 0.4),\n", "    'reg_alpha': stats.uniform(0, 1),\n", "    'reg_lambda': stats.uniform(0, 1)\n", "}\n", "\n", "# Bayesian optimization space (if available)\n", "if BAYESIAN_AVAILABLE:\n", "    bayesian_space = {\n", "        'max_depth': <PERSON><PERSON><PERSON>(3, 10),\n", "        'learning_rate': Real(0.01, 0.3, prior='log-uniform'),\n", "        'n_estimators': <PERSON><PERSON><PERSON>(50, 500),\n", "        'subsample': Real(0.6, 1.0),\n", "        'colsample_bytree': Real(0.6, 1.0),\n", "        'reg_alpha': Real(0, 2),\n", "        'reg_lambda': Real(0, 2)\n", "    }\n", "\n", "# Setup cross-validation\n", "cv_folds = 5\n", "cv_strategy = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)\n", "\n", "print(f\"\\n🔧 Tuning Configuration:\")\n", "print(f\"  • CV folds: {cv_folds}\")\n", "print(f\"  • Grid search combinations: {np.prod([len(v) for v in grid_params.values()])}\")\n", "print(f\"  • Random search iterations: 50\")\n", "if BAYESIAN_AVAILABLE:\n", "    print(f\"  • Bayesian optimization iterations: 30\")\n", "\n", "# Base estimator for tuning\n", "base_params = {\n", "    'objective': baseline_params['objective'],\n", "    'eval_metric': baseline_params['eval_metric'],\n", "    'random_state': 42,\n", "    'n_jobs': -1\n", "}\n", "\n", "if 'num_class' in baseline_params:\n", "    base_params['num_class'] = baseline_params['num_class']\n", "if 'scale_pos_weight' in baseline_params:\n", "    base_params['scale_pos_weight'] = baseline_params['scale_pos_weight']\n", "\n", "base_xgb = xgb.XGBClassifier(**base_params)\n", "\n", "# Grid Search\n", "print(f\"\\n🔍 Starting Grid Search...\")\n", "grid_start = datetime.now()\n", "\n", "grid_search = GridSearchCV(\n", "    estimator=base_xgb,\n", "    param_grid=grid_params,\n", "    cv=cv_strategy,\n", "    scoring='f1_weighted',\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "\n", "grid_search.fit(X_train, y_train)\n", "grid_time = (datetime.now() - grid_start).total_seconds()\n", "\n", "print(f\"✅ Grid Search completed in {grid_time:.1f} seconds\")\n", "print(f\"  • Best CV score: {grid_search.best_score_:.4f}\")\n", "print(f\"  • Best parameters: {grid_search.best_params_}\")\n", "\n", "# Random Search\n", "print(f\"\\n🎲 Starting Random Search...\")\n", "random_start = datetime.now()\n", "\n", "random_search = RandomizedSearchCV(\n", "    estimator=base_xgb,\n", "    param_distributions=random_params,\n", "    n_iter=50,\n", "    cv=cv_strategy,\n", "    scoring='f1_weighted',\n", "    n_jobs=-1,\n", "    random_state=42,\n", "    verbose=1\n", ")\n", "\n", "random_search.fit(X_train, y_train)\n", "random_time = (datetime.now() - random_start).total_seconds()\n", "\n", "print(f\"✅ Random Search completed in {random_time:.1f} seconds\")\n", "print(f\"  • Best CV score: {random_search.best_score_:.4f}\")\n", "print(f\"  • Best parameters: {random_search.best_params_}\")\n", "\n", "# Bayesian Optimization (if available)\n", "bayesian_search = None\n", "bayesian_time = 0\n", "\n", "if BAYESIAN_AVAILABLE:\n", "    print(f\"\\n🧠 Starting Bayesian Optimization...\")\n", "    bayesian_start = datetime.now()\n", "    \n", "    bayesian_search = BayesSearchCV(\n", "        estimator=base_xgb,\n", "        search_spaces=bayesian_space,\n", "        n_iter=30,\n", "        cv=cv_strategy,\n", "        scoring='f1_weighted',\n", "        n_jobs=-1,\n", "        random_state=42,\n", "        verbose=1\n", "    )\n", "    \n", "    bayesian_search.fit(X_train, y_train)\n", "    bayesian_time = (datetime.now() - bayesian_start).total_seconds()\n", "    \n", "    print(f\"✅ Bayesian Optimization completed in {bayesian_time:.1f} seconds\")\n", "    print(f\"  • Best CV score: {bayesian_search.best_score_:.4f}\")\n", "    print(f\"  • Best parameters: {bayesian_search.best_params_}\")\n", "\n", "# Compare tuning results\n", "print(f\"\\n📊 TUNING RESULTS COMPARISON:\")\n", "print(f\"{'Method':<20} {'CV Score':<10} {'Time (s)':<10}\")\n", "print(\"-\" * 45)\n", "print(f\"{'Baseline':<20} {val_results['f1']:<10.4f} {training_time:<10.1f}\")\n", "print(f\"{'Grid Search':<20} {grid_search.best_score_:<10.4f} {grid_time:<10.1f}\")\n", "print(f\"{'Random Search':<20} {random_search.best_score_:<10.4f} {random_time:<10.1f}\")\n", "\n", "if bayesian_search:\n", "    print(f\"{'Bayesian Opt':<20} {bayesian_search.best_score_:<10.4f} {bayesian_time:<10.1f}\")\n", "\n", "# Select best model\n", "best_searches = [grid_search, random_search]\n", "if bayesian_search:\n", "    best_searches.append(bayesian_search)\n", "\n", "best_search = max(best_searches, key=lambda x: x.best_score_)\n", "best_model = best_search.best_estimator_\n", "best_params = best_search.best_params_\n", "best_score = best_search.best_score_\n", "\n", "if best_search == grid_search:\n", "    best_method = 'Grid Search'\n", "elif best_search == random_search:\n", "    best_method = 'Random Search'\n", "else:\n", "    best_method = 'Bayesian Optimization'\n", "\n", "print(f\"\\n🏆 BEST XGBOOST MODEL: {best_method}\")\n", "print(f\"  • CV Score: {best_score:.4f}\")\n", "print(f\"  • Parameters: {best_params}\")\n", "\n", "# Store tuning results\n", "tuning_results = {\n", "    'grid_search': {\n", "        'best_score': grid_search.best_score_,\n", "        'best_params': grid_search.best_params_,\n", "        'time': grid_time\n", "    },\n", "    'random_search': {\n", "        'best_score': random_search.best_score_,\n", "        'best_params': random_search.best_params_,\n", "        'time': random_time\n", "    },\n", "    'best_method': best_method,\n", "    'best_model': best_model,\n", "    'best_params': best_params,\n", "    'best_score': best_score\n", "}\n", "\n", "if bayesian_search:\n", "    tuning_results['bayesian_search'] = {\n", "        'best_score': bayesian_search.best_score_,\n", "        'best_params': bayesian_search.best_params_,\n", "        'time': bayesian_time\n", "    }\n", "\n", "print(f\"\\n✅ Advanced hyperparameter tuning completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Best XGBoost Model Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive evaluation of the best XGBoost model\n", "print(\"📊 BEST XGBOOST MODEL EVALUATION\")\n", "print(\"=\" * 50)\n", "\n", "# Retrain best model with early stopping for final evaluation\n", "print(f\"\\n🔄 Retraining best model with early stopping...\")\n", "final_params = {**base_params, **best_params}\n", "\n", "best_xgb_final = xgb.XGBClassifier(**final_params)\n", "best_xgb_final.fit(\n", "    X_train, y_train,\n", "    eval_set=[(X_train, y_train), (X_val, y_val)],\n", "    eval_names=['train', 'val'],\n", "    early_stopping_rounds=20,\n", "    verbose=False\n", ")\n", "\n", "print(f\"✅ Final model trained\")\n", "print(f\"  • Best iteration: {best_xgb_final.best_iteration}\")\n", "print(f\"  • Best score: {best_xgb_final.best_score:.4f}\")\n", "\n", "# Make predictions with best model\n", "print(f\"\\n🔄 Evaluating best XGBoost model...\")\n", "y_train_best = best_xgb_final.predict(X_train)\n", "y_val_best = best_xgb_final.predict(X_val)\n", "y_test_best = best_xgb_final.predict(X_test)\n", "\n", "# Get prediction probabilities\n", "y_train_proba_best = best_xgb_final.predict_proba(X_train)\n", "y_val_proba_best = best_xgb_final.predict_proba(X_val)\n", "y_test_proba_best = best_xgb_final.predict_proba(X_test)\n", "\n", "# Evaluate best model\n", "best_train_results = evaluate_model(y_train, y_train_best, y_train_proba_best, \"Train\")\n", "best_val_results = evaluate_model(y_val, y_val_best, y_val_proba_best, \"Validation\")\n", "best_test_results = evaluate_model(y_test, y_test_best, y_test_proba_best, \"Test\")\n", "\n", "# Compare with baseline\n", "print(f\"\\n📈 XGBOOST PERFORMANCE COMPARISON:\")\n", "print(f\"{'Metric':<12} {'Baseline':<10} {'Best Model':<12} {'Improvement':<12}\")\n", "print(\"-\" * 50)\n", "\n", "for metric in ['accuracy', 'precision', 'recall', 'f1', 'kappa', 'mcc']:\n", "    baseline_val = val_results.get(metric, 0)\n", "    best_val = best_val_results.get(metric, 0)\n", "    improvement = best_val - baseline_val\n", "    improvement_pct = (improvement / baseline_val * 100) if baseline_val > 0 else 0\n", "    \n", "    print(f\"{metric.capitalize():<12} {baseline_val:<10.3f} {best_val:<12.3f} {improvement:>+7.3f} ({improvement_pct:>+5.1f}%)\")\n", "\n", "# Detailed classification report\n", "print(f\"\\n📋 DETAILED CLASSIFICATION REPORT (Test Set):\")\n", "class_names = [str(label) for label in unique_labels]\n", "print(classification_report(y_test, y_test_best, target_names=class_names))\n", "\n", "# Confusion Matrix\n", "print(f\"\\n🔍 CONFUSION MATRIX (Test Set):\")\n", "cm = confusion_matrix(y_test, y_test_best)\n", "cm_df = pd.DataFrame(cm, index=class_names, columns=class_names)\n", "print(cm_df)\n", "\n", "# Feature importance analysis for best model\n", "print(f\"\\n🔍 FEATURE IMPORTANCE ANALYSIS (Best XGBoost):\")\n", "best_feature_importance = best_xgb_final.feature_importances_\n", "best_top_indices = np.argsort(best_feature_importance)[-20:][::-1]\n", "best_top_features = [(feature_names[i], best_feature_importance[i]) for i in best_top_indices]\n", "\n", "print(f\"  • Top 15 Most Important Features:\")\n", "for i, (feature, importance) in enumerate(best_top_features[:15]):\n", "    print(f\"    {i+1:2d}. {feature:<25} : {importance:.4f}\")\n", "\n", "# XGBoost specific analysis\n", "print(f\"\\n🚀 XGBOOST SPECIFIC ANALYSIS:\")\n", "print(f\"  • Number of boosting rounds: {best_xgb_final.n_estimators}\")\n", "print(f\"  • Best iteration: {best_xgb_final.best_iteration}\")\n", "print(f\"  • Early stopping: {best_xgb_final.n_estimators - best_xgb_final.best_iteration} rounds saved\")\n", "print(f\"  • Learning rate: {best_xgb_final.learning_rate}\")\n", "print(f\"  • Max depth: {best_xgb_final.max_depth}\")\n", "print(f\"  • Subsample: {best_xgb_final.subsample}\")\n", "print(f\"  • Column sample: {best_xgb_final.colsample_bytree}\")\n", "\n", "# Get training history if available\n", "if hasattr(best_xgb_final, 'evals_result_'):\n", "    evals_result = best_xgb_final.evals_result_\n", "    train_scores = evals_result['train'][best_xgb_final.eval_metric]\n", "    val_scores = evals_result['val'][best_xgb_final.eval_metric]\n", "    \n", "    print(f\"\\n📈 Training History:\")\n", "    print(f\"  • Final train score: {train_scores[-1]:.4f}\")\n", "    print(f\"  • Final val score: {val_scores[-1]:.4f}\")\n", "    print(f\"  • Best val score: {min(val_scores):.4f} at iteration {np.argmin(val_scores)}\")\n", "\n", "# Cross-validation analysis\n", "print(f\"\\n🔄 CROSS-VALIDATION ANALYSIS:\")\n", "cv_scores = cross_val_score(best_xgb_final, X_train, y_train, cv=cv_strategy, scoring='f1_weighted')\n", "print(f\"  • CV F1 scores: {cv_scores}\")\n", "print(f\"  • Mean CV F1: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}\")\n", "print(f\"  • CV stability: {'Good' if cv_scores.std() < 0.05 else 'Moderate' if cv_scores.std() < 0.1 else 'Poor'}\")\n", "\n", "# Store best model results\n", "best_model_results = {\n", "    'model': best_xgb_final,\n", "    'method': best_method,\n", "    'params': final_params,\n", "    'cv_score': best_score,\n", "    'best_iteration': best_xgb_final.best_iteration,\n", "    'train_metrics': best_train_results,\n", "    'val_metrics': best_val_results,\n", "    'test_metrics': best_test_results,\n", "    'feature_importance': dict(zip(feature_names, best_feature_importance)),\n", "    'top_features': best_top_features,\n", "    'cv_analysis': {\n", "        'scores': cv_scores.tolist(),\n", "        'mean': cv_scores.mean(),\n", "        'std': cv_scores.std()\n", "    }\n", "}\n", "\n", "if hasattr(best_xgb_final, 'evals_result_'):\n", "    best_model_results['training_history'] = best_xgb_final.evals_result_\n", "\n", "print(f\"\\n✅ Best XGBoost model evaluation completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⚖️ XGBoost vs Random Forest Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare XGBoost with Random Forest results\n", "print(\"⚖️ XGBOOST VS RANDOM FOREST COMPARISON\")\n", "print(\"=\" * 50)\n", "\n", "# Load Random Forest results for comparison\n", "rf_comparison_data = None\n", "try:\n", "    with open('data/results/random_forest_results.json', 'r') as f:\n", "        rf_results = json.load(f)\n", "    \n", "    rf_test_metrics = rf_results['best_model_results']['metrics']['test']\n", "    rf_val_metrics = rf_results['best_model_results']['metrics']['val']\n", "    rf_params = rf_results['best_model_results']['params']\n", "    rf_method = rf_results['best_model_results']['method']\n", "    \n", "    print(f\"✅ Random Forest results loaded for comparison\")\n", "    print(f\"  • RF best method: {rf_method}\")\n", "    rf_comparison_data = rf_results\n", "    \n", "except FileNotFoundError:\n", "    print(f\"⚠️ Random Forest results not found. Skipping comparison.\")\n", "    rf_test_metrics = None\n", "\n", "if rf_test_metrics:\n", "    print(f\"\\n📊 MODEL COMPARISON (Test Set Performance):\")\n", "    print(f\"{'Metric':<12} {'Random Forest':<15} {'XGBoost':<12} {'Winner':<10} {'Difference':<12}\")\n", "    print(\"-\" * 70)\n", "    \n", "    comparison_results = {}\n", "    \n", "    for metric in ['accuracy', 'precision', 'recall', 'f1', 'kappa', 'mcc']:\n", "        rf_val = rf_test_metrics.get(metric, 0)\n", "        xgb_val = best_test_results.get(metric, 0)\n", "        \n", "        winner = 'XGBoost' if xgb_val > rf_val else 'RF' if rf_val > xgb_val else 'Tie'\n", "        difference = xgb_val - rf_val\n", "        \n", "        comparison_results[metric] = {\n", "            'rf': rf_val,\n", "            'xgb': xgb_val,\n", "            'winner': winner,\n", "            'difference': difference\n", "        }\n", "        \n", "        print(f\"{metric.capitalize():<12} {rf_val:<15.3f} {xgb_val:<12.3f} {winner:<10} {difference:>+8.3f}\")\n", "    \n", "    # ROC AUC comparison for binary classification\n", "    if 'roc_auc' in rf_test_metrics and 'roc_auc' in best_test_results:\n", "        rf_auc = rf_test_metrics['roc_auc']\n", "        xgb_auc = best_test_results['roc_auc']\n", "        auc_winner = 'XGBoost' if xgb_auc > rf_auc else 'RF'\n", "        auc_diff = xgb_auc - rf_auc\n", "        \n", "        comparison_results['roc_auc'] = {\n", "            'rf': rf_auc,\n", "            'xgb': xgb_auc,\n", "            'winner': auc_winner,\n", "            'difference': auc_diff\n", "        }\n", "        \n", "        print(f\"{'ROC AUC':<12} {rf_auc:<15.3f} {xgb_auc:<12.3f} {auc_winner:<10} {auc_diff:>+8.3f}\")\n", "    \n", "    # Overall winner analysis\n", "    xgb_wins = sum(1 for result in comparison_results.values() if result['winner'] == 'XGBoost')\n", "    rf_wins = sum(1 for result in comparison_results.values() if result['winner'] == 'RF')\n", "    ties = sum(1 for result in comparison_results.values() if result['winner'] == 'Tie')\n", "    \n", "    print(f\"\\n🏆 OVERALL COMPARISON:\")\n", "    print(f\"  • XGBoost wins: {xgb_wins} metrics\")\n", "    print(f\"  • Random Forest wins: {rf_wins} metrics\")\n", "    print(f\"  • Ties: {ties} metrics\")\n", "    \n", "    overall_winner = 'XGBoost' if xgb_wins > rf_wins else 'Random Forest' if rf_wins > xgb_wins else 'Tie'\n", "    print(f\"  • Overall winner: {overall_winner}\")\n", "    \n", "    # Performance characteristics comparison\n", "    print(f\"\\n🔍 PERFORMANCE CHARACTERISTICS:\")\n", "    \n", "    # Training time comparison (if available)\n", "    rf_training_time = rf_comparison_data.get('baseline_results', {}).get('training_time', 0)\n", "    xgb_training_time = training_time\n", "    \n", "    print(f\"  • Training time:\")\n", "    print(f\"    - Random Forest: {rf_training_time:.2f} seconds\")\n", "    print(f\"    - XGBoost: {xgb_training_time:.2f} seconds\")\n", "    print(f\"    - Faster: {'RF' if rf_training_time < xgb_training_time else 'XGBoost'}\")\n", "    \n", "    # Model complexity comparison\n", "    rf_complexity = rf_comparison_data.get('best_model_results', {}).get('complexity', {})\n", "    rf_nodes = rf_complexity.get('total_nodes', 0)\n", "    xgb_estimators = best_xgb_final.n_estimators\n", "    \n", "    print(f\"  • Model complexity:\")\n", "    print(f\"    - Random Forest: {rf_nodes:,} total nodes\")\n", "    print(f\"    - XGBoost: {xgb_estimators} estimators\")\n", "    \n", "    # Overfitting analysis\n", "    rf_overfitting = rf_comparison_data.get('performance_summary', {}).get('overfitting_check', {}).get('train_val_diff', 0)\n", "    xgb_overfitting = best_train_results['accuracy'] - best_val_results['accuracy']\n", "    \n", "    print(f\"  • Overfitting (Train-Val diff):\")\n", "    print(f\"    - Random Forest: {rf_overfitting:.3f}\")\n", "    print(f\"    - XGBoost: {xgb_overfitting:.3f}\")\n", "    print(f\"    - Better generalization: {'RF' if rf_overfitting < xgb_overfitting else 'XGBoost'}\")\n", "    \n", "    # Feature importance comparison\n", "    print(f\"\\n🔍 FEATURE IMPORTANCE COMPARISON:\")\n", "    rf_top_features = rf_comparison_data.get('feature_analysis', {}).get('top_features', [])[:10]\n", "    xgb_top_features = best_top_features[:10]\n", "    \n", "    # Find common top features\n", "    rf_top_names = [f[0] for f in rf_top_features] if rf_top_features else []\n", "    xgb_top_names = [f[0] for f in xgb_top_features]\n", "    common_features = set(rf_top_names) & set(xgb_top_names)\n", "    \n", "    print(f\"  • RF top 10 features: {len(rf_top_names)}\")\n", "    print(f\"  • XGBoost top 10 features: {len(xgb_top_names)}\")\n", "    print(f\"  • Common top features: {len(common_features)} ({len(common_features)/10*100:.0f}%)\")\n", "    \n", "    if common_features:\n", "        print(f\"  • Common important features: {list(common_features)[:5]}\")\n", "    \n", "    # Store comparison results\n", "    model_comparison = {\n", "        'metrics_comparison': comparison_results,\n", "        'overall_winner': overall_winner,\n", "        'xgb_wins': xgb_wins,\n", "        'rf_wins': rf_wins,\n", "        'ties': ties,\n", "        'performance_characteristics': {\n", "            'training_time': {'rf': rf_training_time, 'xgb': xgb_training_time},\n", "            'overfitting': {'rf': rf_overfitting, 'xgb': xgb_overfitting},\n", "            'common_features': list(common_features)\n", "        }\n", "    }\n", "    \n", "else:\n", "    model_comparison = None\n", "    print(f\"\\n⚠️ Cannot perform model comparison without Random Forest results\")\n", "\n", "print(f\"\\n✅ Model comparison completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save XGBoost Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all XGBoost results\n", "print(\"💾 SAVING XGBOOST RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Create directories\n", "os.makedirs('models/xgboost', exist_ok=True)\n", "os.makedirs('data/results', exist_ok=True)\n", "os.makedirs('data/predictions', exist_ok=True)\n", "\n", "# Save best model\n", "best_model_file = 'models/xgboost/xgb_best_model.pkl'\n", "joblib.dump(best_xgb_final, best_model_file)\n", "print(f\"✅ Best XGBoost model saved: {best_model_file}\")\n", "\n", "# Save baseline model for comparison\n", "baseline_model_file = 'models/xgboost/xgb_baseline_model.pkl'\n", "joblib.dump(xgb_baseline, baseline_model_file)\n", "print(f\"✅ Baseline XGBoost model saved: {baseline_model_file}\")\n", "\n", "# Save predictions\n", "predictions_data = {\n", "    'y_train_true': y_train.tolist(),\n", "    'y_train_pred': y_train_best.tolist(),\n", "    'y_val_true': y_val.tolist(),\n", "    'y_val_pred': y_val_best.tolist(),\n", "    'y_test_true': y_test.tolist(),\n", "    'y_test_pred': y_test_best.tolist(),\n", "    'y_train_proba': y_train_proba_best.tolist(),\n", "    'y_val_proba': y_val_proba_best.tolist(),\n", "    'y_test_proba': y_test_proba_best.tolist()\n", "}\n", "\n", "predictions_file = 'data/predictions/xgb_predictions.json'\n", "with open(predictions_file, 'w') as f:\n", "    json.dump(predictions_data, f, indent=2)\n", "print(f\"✅ XGBoost predictions saved: {predictions_file}\")\n", "\n", "# Save comprehensive results\n", "xgb_results = {\n", "    'model_info': {\n", "        'algorithm': 'XGBoost',\n", "        'version': xgb.__version__,\n", "        'labeling_method': labeling_method,\n", "        'feature_config': feature_config,\n", "        'training_date': datetime.now().isoformat(),\n", "        'dataset_size': len(y_labels),\n", "        'feature_count': X_dense.shape[1]\n", "    },\n", "    'data_split': {\n", "        'total_samples': len(y_labels),\n", "        'train_size': len(X_train),\n", "        'val_size': len(X_val),\n", "        'test_size': len(X_test),\n", "        'class_distribution': class_distribution,\n", "        'imbalance_ratio': imbalance_ratio,\n", "        'scale_pos_weight': scale_pos_weight\n", "    },\n", "    'baseline_results': {\n", "        'params': baseline_params,\n", "        'training_time': training_time,\n", "        'best_iteration': baseline_results['best_iteration'],\n", "        'best_score': baseline_results['best_score'],\n", "        'metrics': {\n", "            'train': baseline_results['train_metrics'],\n", "            'val': baseline_results['val_metrics'],\n", "            'test': baseline_results['test_metrics']\n", "        }\n", "    },\n", "    'hyperparameter_tuning': tuning_results,\n", "    'best_model_results': {\n", "        'method': best_method,\n", "        'params': final_params,\n", "        'cv_score': best_score,\n", "        'best_iteration': best_model_results['best_iteration'],\n", "        'metrics': {\n", "            'train': best_model_results['train_metrics'],\n", "            'val': best_model_results['val_metrics'],\n", "            'test': best_model_results['test_metrics']\n", "        },\n", "        'cv_analysis': best_model_results['cv_analysis']\n", "    },\n", "    'feature_analysis': {\n", "        'top_features': best_top_features[:20],\n", "        'importance_stats': {\n", "            'mean': float(np.mean(best_feature_importance)),\n", "            'std': float(np.std(best_feature_importance)),\n", "            'max': float(np.max(best_feature_importance)),\n", "            'min': float(np.min(best_feature_importance))\n", "        }\n", "    },\n", "    'performance_summary': {\n", "        'best_test_accuracy': float(best_test_results['accuracy']),\n", "        'best_test_f1': float(best_test_results['f1']),\n", "        'improvement_over_baseline': {\n", "            'accuracy': float(best_val_results['accuracy'] - val_results['accuracy']),\n", "            'f1': float(best_val_results['f1'] - val_results['f1'])\n", "        },\n", "        'overfitting_check': {\n", "            'train_val_diff': float(best_train_results['accuracy'] - best_val_results['accuracy']),\n", "            'status': 'Good' if (best_train_results['accuracy'] - best_val_results['accuracy']) < 0.05 else 'Overfitting'\n", "        }\n", "    }\n", "}\n", "\n", "# Add training history if available\n", "if 'training_history' in best_model_results:\n", "    xgb_results['training_history'] = best_model_results['training_history']\n", "\n", "# Add model comparison if available\n", "if model_comparison:\n", "    xgb_results['model_comparison'] = model_comparison\n", "\n", "results_file = 'data/results/xgboost_results.json'\n", "with open(results_file, 'w') as f:\n", "    json.dump(xgb_results, f, indent=2, default=str)\n", "print(f\"✅ Comprehensive XGBoost results saved: {results_file}\")\n", "\n", "# Save feature importance as Excel\n", "feature_importance_df = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'importance': best_feature_importance,\n", "    'rank': range(1, len(feature_names) + 1)\n", "}).sort_values('importance', ascending=False).reset_index(drop=True)\n", "\n", "feature_importance_df['rank'] = range(1, len(feature_importance_df) + 1)\n", "\n", "feature_importance_file = 'data/results/xgb_feature_importance.xlsx'\n", "feature_importance_df.to_excel(feature_importance_file, index=False)\n", "print(f\"✅ XGBoost feature importance saved: {feature_importance_file}\")\n", "\n", "# Save model artifacts for production use\n", "model_artifacts = {\n", "    'model': best_xgb_final,\n", "    'vectorizer': vectorizer,\n", "    'label_encoder': LabelEncoder().fit(y_labels),\n", "    'feature_names': feature_names.tolist(),\n", "    'class_names': unique_labels.tolist(),\n", "    'model_params': final_params,\n", "    'preprocessing_info': {\n", "        'labeling_method': labeling_method,\n", "        'feature_config': feature_config,\n", "        'scale_pos_weight': scale_pos_weight\n", "    },\n", "    'performance_metrics': best_test_results,\n", "    'creation_date': datetime.now().isoformat()\n", "}\n", "\n", "artifacts_file = 'models/xgboost/xgb_production_artifacts.pkl'\n", "joblib.dump(model_artifacts, artifacts_file)\n", "print(f\"✅ XGBoost production artifacts saved: {artifacts_file}\")\n", "\n", "# Create utility functions for XGBoost model usage\n", "xgb_utils_code = '''\n", "import joblib\n", "import numpy as np\n", "import pandas as pd\n", "import xgboost as xgb\n", "\n", "def load_xgb_model(model_path='models/xgboost/xgb_best_model.pkl'):\n", "    \"\"\"Load the trained XGBoost model\"\"\"\n", "    return joblib.load(model_path)\n", "\n", "def load_xgb_artifacts(artifacts_path='models/xgboost/xgb_production_artifacts.pkl'):\n", "    \"\"\"Load all XGBoost production artifacts\"\"\"\n", "    return joblib.load(artifacts_path)\n", "\n", "def predict_sentiment_xgb(texts, model_path='models/xgboost/xgb_production_artifacts.pkl'):\n", "    \"\"\"Predict sentiment for new texts using XGBoost\"\"\"\n", "    artifacts = load_xgb_artifacts(model_path)\n", "    \n", "    model = artifacts['model']\n", "    vectorizer = artifacts['vectorizer']\n", "    class_names = artifacts['class_names']\n", "    \n", "    # Vectorize texts\n", "    if isinstance(texts, str):\n", "        texts = [texts]\n", "    \n", "    X = vectorizer.transform(texts)\n", "    \n", "    # Make predictions\n", "    predictions = model.predict(X)\n", "    probabilities = model.predict_proba(X)\n", "    \n", "    # Convert to class names\n", "    predicted_labels = [class_names[pred] for pred in predictions]\n", "    \n", "    results = []\n", "    for i, text in enumerate(texts):\n", "        result = {\n", "            'text': text,\n", "            'predicted_sentiment': predicted_labels[i],\n", "            'confidence': float(np.max(probabilities[i])),\n", "            'probabilities': {class_names[j]: float(probabilities[i][j]) \n", "                           for j in range(len(class_names))}\n", "        }\n", "        results.append(result)\n", "    \n", "    return results if len(results) > 1 else results[0]\n", "\n", "def get_feature_importance_xgb(top_n=20, artifacts_path='models/xgboost/xgb_production_artifacts.pkl'):\n", "    \"\"\"Get top N most important features from XGBoost\"\"\"\n", "    artifacts = load_xgb_artifacts(artifacts_path)\n", "    \n", "    model = artifacts['model']\n", "    feature_names = artifacts['feature_names']\n", "    \n", "    importance = model.feature_importances_\n", "    top_indices = np.argsort(importance)[-top_n:][::-1]\n", "    \n", "    return [(feature_names[i], importance[i]) for i in top_indices]\n", "\n", "def compare_models(text, rf_path='models/random_forest/rf_production_artifacts.pkl',\n", "                  xgb_path='models/xgboost/xgb_production_artifacts.pkl'):\n", "    \"\"\"Compare predictions from both Random Forest and XGBoost\"\"\"\n", "    try:\n", "        from utils.random_forest_utils import predict_sentiment_rf\n", "        rf_result = predict_sentiment_rf(text, rf_path)\n", "    except:\n", "        rf_result = None\n", "    \n", "    xgb_result = predict_sentiment_xgb(text, xgb_path)\n", "    \n", "    return {\n", "        'text': text,\n", "        'random_forest': rf_result,\n", "        'xgboost': xgb_result,\n", "        'agreement': rf_result['predicted_sentiment'] == xgb_result['predicted_sentiment'] if rf_result else None\n", "    }\n", "'''\n", "\n", "with open('utils/xgboost_utils.py', 'w', encoding='utf-8') as f:\n", "    f.write(xgb_utils_code)\n", "\n", "print(f\"\\n🔧 XGBoost utility functions saved: utils/xgboost_utils.py\")\n", "\n", "print(f\"\\n📊 FILES GENERATED SUMMARY:\")\n", "print(f\"  📁 models/xgboost/\")\n", "print(f\"     • xgb_best_model.pkl - Best trained XGBoost model\")\n", "print(f\"     • xgb_baseline_model.pkl - Baseline XGBoost model\")\n", "print(f\"     • xgb_production_artifacts.pkl - Complete artifacts\")\n", "print(f\"  📁 data/results/\")\n", "print(f\"     • xgboost_results.json - Comprehensive results\")\n", "print(f\"     • xgb_feature_importance.xlsx - Feature analysis\")\n", "print(f\"  📁 data/predictions/\")\n", "print(f\"     • xgb_predictions.json - All predictions\")\n", "print(f\"  🔧 utils/xgboost_utils.py - Utility functions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 XGBOOST TRAINING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n🚀 XGBOOST MODEL TRAINING COMPLETED:\")\n", "print(f\"  • Dataset: {len(y_labels)} samples\")\n", "print(f\"  • Features: {X_dense.shape[1]} TF-IDF features ({feature_config})\")\n", "print(f\"  • Labeling method: {labeling_method}\")\n", "print(f\"  • Classes: {len(unique_labels)} ({', '.join(map(str, unique_labels))})\")\n", "print(f\"  • XGBoost version: {xgb.__version__}\")\n", "\n", "print(f\"\\n🎯 HYPERPARAMETER TUNING:\")\n", "print(f\"  • Grid Search: {grid_search.best_score_:.4f} F1-score\")\n", "print(f\"  • Random Search: {random_search.best_score_:.4f} F1-score\")\n", "if bayesian_search:\n", "    print(f\"  • Bayesian Optimization: {bayesian_search.best_score_:.4f} F1-score\")\n", "print(f\"  • Best method: {best_method}\")\n", "print(f\"  • Best CV score: {best_score:.4f}\")\n", "\n", "print(f\"\\n🏆 BEST XGBOOST PERFORMANCE:\")\n", "print(f\"  • Test Accuracy: {best_test_results['accuracy']:.3f}\")\n", "print(f\"  • Test F1-Score: {best_test_results['f1']:.3f}\")\n", "print(f\"  • Test Precision: {best_test_results['precision']:.3f}\")\n", "print(f\"  • Test Recall: {best_test_results['recall']:.3f}\")\n", "print(f\"  • <PERSON>'s Kappa: {best_test_results['kappa']:.3f}\")\n", "print(f\"  • Matthews Correlation: {best_test_results['mcc']:.3f}\")\n", "\n", "if 'roc_auc' in best_test_results:\n", "    print(f\"  • ROC AUC: {best_test_results['roc_auc']:.3f}\")\n", "\n", "print(f\"\\n📈 IMPROVEMENT OVER BASELINE:\")\n", "acc_improvement = best_val_results['accuracy'] - val_results['accuracy']\n", "f1_improvement = best_val_results['f1'] - val_results['f1']\n", "print(f\"  • Accuracy: {acc_improvement:+.3f} ({acc_improvement/val_results['accuracy']*100:+.1f}%)\")\n", "print(f\"  • F1-Score: {f1_improvement:+.3f} ({f1_improvement/val_results['f1']*100:+.1f}%)\")\n", "\n", "print(f\"\\n🔧 BEST MODEL CONFIGURATION:\")\n", "print(f\"  • Estimators: {best_xgb_final.n_estimators}\")\n", "print(f\"  • Best iteration: {best_xgb_final.best_iteration}\")\n", "print(f\"  • Max depth: {best_xgb_final.max_depth}\")\n", "print(f\"  • Learning rate: {best_xgb_final.learning_rate}\")\n", "print(f\"  • Subsample: {best_xgb_final.subsample}\")\n", "print(f\"  • Column sample: {best_xgb_final.colsample_bytree}\")\n", "\n", "print(f\"\\n🔍 TOP 5 MOST IMPORTANT FEATURES:\")\n", "for i, (feature, importance) in enumerate(best_top_features[:5]):\n", "    print(f\"  {i+1}. {feature:<25} : {importance:.4f}\")\n", "\n", "if model_comparison:\n", "    print(f\"\\n⚖️ VS RANDOM FOREST COMPARISON:\")\n", "    print(f\"  • Overall winner: {model_comparison['overall_winner']}\")\n", "    print(f\"  • XGBoost wins: {model_comparison['xgb_wins']} metrics\")\n", "    print(f\"  • Random Forest wins: {model_comparison['rf_wins']} metrics\")\n", "    print(f\"  • Key advantages:\")\n", "    \n", "    # Highlight key differences\n", "    for metric, data in model_comparison['metrics_comparison'].items():\n", "        if data['winner'] == 'XGBoost' and data['difference'] > 0.01:\n", "            print(f\"    - Better {metric}: +{data['difference']:.3f}\")\n", "\n", "print(f\"\\n✅ QUALITY ASSESSMENT:\")\n", "overfitting_diff = best_train_results['accuracy'] - best_val_results['accuracy']\n", "print(f\"  • Overfitting check: {overfitting_diff:.3f} ({'Good' if overfitting_diff < 0.05 else 'Overfitting'})\")\n", "print(f\"  • CV stability: {cv_scores.std():.3f} ({'Good' if cv_scores.std() < 0.05 else 'Moderate'})\")\n", "print(f\"  • Early stopping: {best_xgb_final.n_estimators - best_xgb_final.best_iteration} rounds saved\")\n", "\n", "print(f\"\\n💾 OUTPUT FILES:\")\n", "print(f\"  • Best model: models/xgboost/xgb_best_model.pkl\")\n", "print(f\"  • Production artifacts: models/xgboost/xgb_production_artifacts.pkl\")\n", "print(f\"  • Results: data/results/xgboost_results.json\")\n", "print(f\"  • Feature importance: data/results/xgb_feature_importance.xlsx\")\n", "print(f\"  • Predictions: data/predictions/xgb_predictions.json\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. Model evaluation and ensemble (09_model_evaluation.ipynb)\")\n", "print(f\"  2. Advanced analysis and topic modeling (10-12)\")\n", "print(f\"  3. Production deployment preparation\")\n", "print(f\"  4. Performance monitoring setup\")\n", "\n", "print(f\"\\n✅ XGBOOST TRAINING COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for model evaluation and ensemble phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}