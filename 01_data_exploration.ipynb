{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 📊 01. Data Exploration & Statistical Analysis\n",
    "\n",
    "**Tujuan**: Melakukan eksplorasi data mendalam untuk memahami karakteristik dataset ulasan GoFood\n",
    "\n",
    "**Dataset**: `gofood_ulasan_preprocessed.xlsx`\n",
    "\n",
    "**Analisis yang akan dilakukan**:\n",
    "1. 📈 **Statistical Overview** - Statistik deskriptif\n",
    "2. 🔍 **Data Quality Assessment** - Missing values, duplicates\n",
    "3. 📊 **Distribution Analysis** - Rating dan text length distribution\n",
    "4. 🎯 **Sentiment Distribution** - Analisis sebaran sentimen\n",
    "5. 📝 **Text Characteristics** - Panjang teks, vocabulary analysis\n",
    "6. 🎨 **Initial Visualizations** - Charts dan graphs\n",
    "\n",
    "---"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🛠️ Setup & Import Libraries"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Core libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import plotly.express as px\n",
    "import plotly.graph_objects as go\n",
    "from plotly.subplots import make_subplots\n",
    "import warnings\n",
    "from datetime import datetime\n",
    "import re\n",
    "from collections import Counter\n",
    "from wordcloud import WordCloud\n",
    "\n",
    "# Configuration\n",
    "warnings.filterwarnings('ignore')\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "# Display settings\n",
    "pd.set_option('display.max_columns', None)\n",
    "pd.set_option('display.max_rows', 100)\n",
    "pd.set_option('display.width', None)\n",
    "\n",
    "print(\"📚 Libraries imported successfully!\")\n",
    "print(f\"🕐 Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📂 Load Dataset"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the preprocessed dataset\n",
    "file_path = 'gofood_ulasan_preprocessed.xlsx'\n",
    "\n",
    "try:\n",
    "    df = pd.read_excel(file_path)\n",
    "    print(f\"✅ Dataset loaded successfully from: {file_path}\")\n",
    "    print(f\"📊 Dataset shape: {df.shape}\")\n",
    "    print(f\"📋 Columns: {list(df.columns)}\")\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(f\"❌ Error: File '{file_path}' not found.\")\n",
    "    print(\"Please ensure the preprocessed data file exists.\")\n",
    "    raise\n",
    "except Exception as e:\n",
    "    print(f\"❌ Error loading dataset: {e}\")\n",
    "    raise"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔍 Basic Data Information"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Basic information about the dataset\n",
    "print(\"📊 DATASET OVERVIEW\")\n",
    "print(\"=\" * 50)\n",
    "print(f\"Total Records: {len(df):,}\")\n",
    "print(f\"Total Columns: {len(df.columns)}\")\n",
    "print(f\"Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n",
    "\n",
    "print(\"\\n📋 COLUMN INFORMATION\")\n",
    "print(\"=\" * 50)\n",
    "print(df.info())\n",
    "\n",
    "print(\"\\n📈 STATISTICAL SUMMARY\")\n",
    "print(\"=\" * 50)\n",
    "print(df.describe(include='all'))"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display first few rows\n",
    "print(\"👀 FIRST 5 ROWS\")\n",
    "print(\"=\" * 50)\n",
    "display(df.head())\n",
    "\n",
    "print(\"\\n👀 LAST 5 ROWS\")\n",
    "print(\"=\" * 50)\n",
    "display(df.tail())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🧹 Data Quality Assessment"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Check for missing values\n",
    "print(\"🔍 MISSING VALUES ANALYSIS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "missing_data = df.isnull().sum()\n",
    "missing_percent = (missing_data / len(df)) * 100\n",
    "\n",
    "missing_df = pd.DataFrame({\n",
    "    'Column': missing_data.index,\n",
    "    'Missing Count': missing_data.values,\n",
    "    'Missing Percentage': missing_percent.values\n",
    "})\n",
    "\n",
    "missing_df = missing_df[missing_df['Missing Count'] > 0].sort_values('Missing Count', ascending=False)\n",
    "\n",
    "if len(missing_df) > 0:\n",
    "    print(missing_df)\n",
    "else:\n",
    "    print(\"✅ No missing values found!\")\n",
    "\n",
    "# Check for duplicates\n",
    "print(\"\\n🔄 DUPLICATE ANALYSIS\")\n",
    "print(\"=\" * 50)\n",
    "duplicates = df.duplicated().sum()\n",
    "print(f\"Total duplicate rows: {duplicates}\")\n",
    "\n",
    "if 'ulasan_stemmed' in df.columns:\n",
    "    text_duplicates = df['ulasan_stemmed'].duplicated().sum()\n",
    "    print(f\"Duplicate text reviews: {text_duplicates}\")\n",
    "\n",
    "# Data types check\n",
    "print(\"\\n📊 DATA TYPES\")\n",
    "print(\"=\" * 50)\n",
    "print(df.dtypes)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Rating Distribution Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Rating distribution analysis\n",
    "if 'nilai' in df.columns:\n",
    "    print(\"⭐ RATING DISTRIBUTION\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    rating_counts = df['nilai'].value_counts().sort_index()\n",
    "    rating_percent = (rating_counts / len(df)) * 100\n",
    "    \n",
    "    rating_summary = pd.DataFrame({\n",
    "        'Rating': rating_counts.index,\n",
    "        'Count': rating_counts.values,\n",
    "        'Percentage': rating_percent.values\n",
    "    })\n",
    "    \n",
    "    print(rating_summary)\n",
    "    \n",
    "    # Statistical measures for ratings\n",
    "    print(f\"\\n📈 RATING STATISTICS\")\n",
    "    print(\"=\" * 30)\n",
    "    print(f\"Mean Rating: {df['nilai'].mean():.2f}\")\n",
    "    print(f\"Median Rating: {df['nilai'].median():.2f}\")\n",
    "    print(f\"Mode Rating: {df['nilai'].mode().iloc[0]}\")\n",
    "    print(f\"Standard Deviation: {df['nilai'].std():.2f}\")\n",
    "    print(f\"Min Rating: {df['nilai'].min()}\")\n",
    "    print(f\"Max Rating: {df['nilai'].max()}\")\n",
    "else:\n",
    "    print(\"⚠️ 'nilai' column not found in dataset\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📝 Text Characteristics Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Text length analysis\n",
    "text_columns = [col for col in df.columns if 'ulasan' in col.lower()]\n",
    "\n",
    "if text_columns:\n",
    "    print(\"📝 TEXT LENGTH ANALYSIS\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    for col in text_columns:\n",
    "        if df[col].dtype == 'object':\n",
    "            # Calculate text lengths\n",
    "            df[f'{col}_length'] = df[col].astype(str).apply(len)\n",
    "            df[f'{col}_word_count'] = df[col].astype(str).apply(lambda x: len(x.split()))\n",
    "            \n",
    "            print(f\"\\n📊 {col.upper()} STATISTICS:\")\n",
    "            print(f\"  Character Length - Mean: {df[f'{col}_length'].mean():.1f}, Median: {df[f'{col}_length'].median():.1f}\")\n",
    "            print(f\"  Word Count - Mean: {df[f'{col}_word_count'].mean():.1f}, Median: {df[f'{col}_word_count'].median():.1f}\")\n",
    "            print(f\"  Min Length: {df[f'{col}_length'].min()}, Max Length: {df[f'{col}_length'].max()}\")\n",
    "            print(f\"  Min Words: {df[f'{col}_word_count'].min()}, Max Words: {df[f'{col}_word_count'].max()}\")\n",
    "            \n",
    "            # Empty text analysis\n",
    "            empty_texts = df[col].astype(str).apply(lambda x: x.strip() == '').sum()\n",
    "            print(f\"  Empty texts: {empty_texts} ({empty_texts/len(df)*100:.1f}%)\")\n",
    "else:\n",
    "    print(\"⚠️ No text columns found for analysis\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎯 Sentiment Mapping & Distribution"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create sentiment labels based on ratings\n",
    "if 'nilai' in df.columns:\n",
    "    def create_sentiment_label(rating):\n",
    "        if rating <= 2:\n",
    "            return 'Negatif'\n",
    "        elif rating >= 4:\n",
    "            return 'Positif'\n",
    "        else:\n",
    "            return 'Netral'\n",
    "    \n",
    "    df['sentiment_label'] = df['nilai'].apply(create_sentiment_label)\n",
    "    \n",
    "    print(\"🎯 SENTIMENT DISTRIBUTION (Score-based)\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    sentiment_counts = df['sentiment_label'].value_counts()\n",
    "    sentiment_percent = (sentiment_counts / len(df)) * 100\n",
    "    \n",
    "    sentiment_summary = pd.DataFrame({\n",
    "        'Sentiment': sentiment_counts.index,\n",
    "        'Count': sentiment_counts.values,\n",
    "        'Percentage': sentiment_percent.values\n",
    "    })\n",
    "    \n",
    "    print(sentiment_summary)\n",
    "    \n",
    "    # Detailed rating-sentiment mapping\n",
    "    print(\"\\n📊 RATING-SENTIMENT MAPPING\")\n",
    "    print(\"=\" * 50)\n",
    "    rating_sentiment = pd.crosstab(df['nilai'], df['sentiment_label'], margins=True)\n",
    "    print(rating_sentiment)\n",
    "else:\n",
    "    print(\"⚠️ Cannot create sentiment labels without 'nilai' column\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Data Visualizations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create comprehensive visualizations\n",
    "fig = make_subplots(\n",
    "    rows=2, cols=2,\n",
    "    subplot_titles=('Rating Distribution', 'Sentiment Distribution', \n",
    "                   'Text Length Distribution', 'Word Count Distribution'),\n",
    "    specs=[[{'type': 'bar'}, {'type': 'pie'}],\n",
    "           [{'type': 'histogram'}, {'type': 'histogram'}]]\n",
    ")\n",
    "\n",
    "if 'nilai' in df.columns:\n",
    "    # Rating distribution\n",
    "    rating_counts = df['nilai'].value_counts().sort_index()\n",
    "    fig.add_trace(\n",
    "        go.Bar(x=rating_counts.index, y=rating_counts.values, \n",
    "               name='Rating Count', marker_color='skyblue'),\n",
    "        row=1, col=1\n",
    "    )\n",
    "    \n",
    "    # Sentiment distribution\n",
    "    if 'sentiment_label' in df.columns:\n",
    "        sentiment_counts = df['sentiment_label'].value_counts()\n",
    "        fig.add_trace(\n",
    "            go.Pie(labels=sentiment_counts.index, values=sentiment_counts.values,\n",
    "                   name='Sentiment'),\n",
    "            row=1, col=2\n",
    "        )\n",
    "\n",
    "# Text length distributions\n",
    "if any(col.endswith('_length') for col in df.columns):\n",
    "    length_col = [col for col in df.columns if col.endswith('_length')][0]\n",
    "    fig.add_trace(\n",
    "        go.Histogram(x=df[length_col], name='Text Length', \n",
    "                    marker_color='lightgreen', nbinsx=30),\n",
    "        row=2, col=1\n",
    "    )\n",
    "\n",
    "if any(col.endswith('_word_count') for col in df.columns):\n",
    "    word_col = [col for col in df.columns if col.endswith('_word_count')][0]\n",
    "    fig.add_trace(\n",
    "        go.Histogram(x=df[word_col], name='Word Count', \n",
    "                    marker_color='lightcoral', nbinsx=30),\n",
    "        row=2, col=2\n",
    "    )\n",
    "\n",
    "fig.update_layout(height=800, showlegend=False, \n",
    "                 title_text=\"📊 GoFood Reviews - Data Distribution Overview\")\n",
    "fig.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Rating vs Text Length Analysis\n",
    "if 'nilai' in df.columns and any(col.endswith('_length') for col in df.columns):\n",
    "    length_col = [col for col in df.columns if col.endswith('_length')][0]\n",
    "    \n",
    "    fig = px.box(df, x='nilai', y=length_col, \n",
    "                title='📏 Text Length Distribution by Rating',\n",
    "                labels={'nilai': 'Rating', length_col: 'Text Length (characters)'})\n",
    "    fig.update_layout(height=500)\n",
    "    fig.show()\n",
    "    \n",
    "    # Statistical correlation\n",
    "    correlation = df['nilai'].corr(df[length_col])\n",
    "    print(f\"\\n📊 Correlation between Rating and Text Length: {correlation:.3f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔤 Vocabulary Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Vocabulary analysis for processed text\n",
    "if 'ulasan_stemmed' in df.columns:\n",
    "    print(\"🔤 VOCABULARY ANALYSIS\")\n",
    "    print(\"=\" * 50)\n",
    "    \n",
    "    # Combine all text\n",
    "    all_text = ' '.join(df['ulasan_stemmed'].astype(str))\n",
    "    words = all_text.split()\n",
    "    \n",
    "    # Basic vocabulary statistics\n",
    "    unique_words = set(words)\n",
    "    word_freq = Counter(words)\n",
    "    \n",
    "    print(f\"Total words: {len(words):,}\")\n",
    "    print(f\"Unique words: {len(unique_words):,}\")\n",
    "    print(f\"Vocabulary richness: {len(unique_words)/len(words):.3f}\")\n",
    "    \n",
    "    # Most common words\n",
    "    print(\"\\n🔝 TOP 20 MOST COMMON WORDS:\")\n",
    "    print(\"-\" * 30)\n",
    "    for word, count in word_freq.most_common(20):\n",
    "        if word.strip():  # Skip empty words\n",
    "            print(f\"{word:<15} : {count:>5} times\")\n",
    "    \n",
    "    # Word length distribution\n",
    "    word_lengths = [len(word) for word in words if word.strip()]\n",
    "    print(f\"\\n📏 WORD LENGTH STATISTICS:\")\n",
    "    print(f\"Average word length: {np.mean(word_lengths):.1f} characters\")\n",
    "    print(f\"Median word length: {np.median(word_lengths):.1f} characters\")\n",
    "    print(f\"Min word length: {min(word_lengths)} characters\")\n",
    "    print(f\"Max word length: {max(word_lengths)} characters\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📈 Summary & Key Insights"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Generate comprehensive summary\n",
    "print(\"📋 DATA EXPLORATION SUMMARY\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "print(f\"\\n📊 DATASET OVERVIEW:\")\n",
    "print(f\"  • Total reviews: {len(df):,}\")\n",
    "print(f\"  • Columns: {len(df.columns)}\")\n",
    "print(f\"  • Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB\")\n",
    "\n",
    "if 'nilai' in df.columns:\n",
    "    print(f\"\\n⭐ RATING INSIGHTS:\")\n",
    "    print(f\"  • Average rating: {df['nilai'].mean():.2f}/5\")\n",
    "    print(f\"  • Most common rating: {df['nilai'].mode().iloc[0]}\")\n",
    "    print(f\"  • Rating distribution: {dict(df['nilai'].value_counts().sort_index())}\")\n",
    "\n",
    "if 'sentiment_label' in df.columns:\n",
    "    sentiment_dist = df['sentiment_label'].value_counts()\n",
    "    print(f\"\\n🎯 SENTIMENT INSIGHTS:\")\n",
    "    for sentiment, count in sentiment_dist.items():\n",
    "        percentage = (count / len(df)) * 100\n",
    "        print(f\"  • {sentiment}: {count:,} ({percentage:.1f}%)\")\n",
    "\n",
    "# Text insights\n",
    "text_cols = [col for col in df.columns if 'ulasan' in col.lower() and df[col].dtype == 'object']\n",
    "if text_cols:\n",
    "    main_text_col = text_cols[0]\n",
    "    if f'{main_text_col}_length' in df.columns:\n",
    "        print(f\"\\n📝 TEXT INSIGHTS:\")\n",
    "        print(f\"  • Average text length: {df[f'{main_text_col}_length'].mean():.0f} characters\")\n",
    "        print(f\"  • Average word count: {df[f'{main_text_col}_word_count'].mean():.0f} words\")\n",
    "        print(f\"  • Shortest review: {df[f'{main_text_col}_length'].min()} characters\")\n",
    "        print(f\"  • Longest review: {df[f'{main_text_col}_length'].max()} characters\")\n",
    "\n",
    "# Data quality insights\n",
    "missing_total = df.isnull().sum().sum()\n",
    "duplicates_total = df.duplicated().sum()\n",
    "\n",
    "print(f\"\\n🧹 DATA QUALITY:\")\n",
    "print(f\"  • Missing values: {missing_total} ({missing_total/df.size*100:.2f}% of all data points)\")\n",
    "print(f\"  • Duplicate rows: {duplicates_total} ({duplicates_total/len(df)*100:.2f}% of total rows)\")\n",
    "print(f\"  • Data completeness: {((df.size - missing_total)/df.size)*100:.1f}%\")\n",
    "\n",
    "print(f\"\\n✅ DATA EXPLORATION COMPLETED!\")\n",
    "print(f\"📅 Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n",
    "print(f\"\\n🎯 NEXT STEPS:\")\n",
    "print(f\"  1. Setup Indonesian lexicon resources (02_lexicon_setup.ipynb)\")\n",
    "print(f\"  2. Advanced text preprocessing (03_text_preprocessing.ipynb)\")\n",
    "print(f\"  3. Feature extraction and modeling\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
