# Sel 1: Impor Library dan <PERSON>pan <PERSON>n

# Instalasi library yang mungkin belum ada di Colab
!pip install gensim
!pip install openpyxl # TAMBAHAN: Diperlukan untuk menyimpan file .xlsx
!pip install sastrawi

import pandas as pd
import re
from Sastrawi.Stemmer.StemmerFactory import StemmerFactory
import nltk # Diimpor di sini untuk konsistensi
from nltk.corpus import stopwords
import string
import warnings
from google.colab import files
import csv

warnings.filterwarnings('ignore')

# Mengunduh NLTK stopwords
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    print("Mencoba mengunduh NLTK stopwords...")
    nltk.download('stopwords')
    print("NLTK stopwords berhasil diunduh.")

print("✅ Library berhasil diimpor dan lingkungan siap.")

# Sel 2: Pemuatan Data dari File Excel

# --- Tentukan path ke file Excel Anda ---
# Ganti 'gobiz_ulasan.xlsx' dengan nama file Anda.
# Jika file ada di Google Drive, path-nya akan seperti: '/content/drive/MyDrive/FolderAnda/gobiz_ulasan.xlsx'
file_path = 'reviews_gofood_Merchant.xlsx'

print(f"Mencoba memuat data dari: {file_path}...")

# --- Memuat Data ke DataFrame dari File Excel ---
try:
    # Menggunakan pd.read_excel untuk memuat file .xlsx
    df = pd.read_excel(file_path)
    print(f"✅ Data berhasil dimuat dari: {file_path}")

except FileNotFoundError:
    print(f"\n❌ ERROR: File '{file_path}' tidak ditemukan.")
    print("   Pastikan nama file sudah benar dan file berada di direktori yang sama,")
    print("   atau pastikan Google Drive Anda sudah terhubung (mounted) jika file ada di sana.")
    exit() # Hentikan eksekusi jika file tidak ada
except Exception as e:
    print(f"\n❌ GAGAL MEMUAT FILE EXCEL: {e}")
    exit()

# --- MENGHAPUS KOLOM YANG TIDAK DIPERLUKAN ---
kolom_untuk_dihapus = ['user_name', 'at', 'thumbsUpCount']
kolom_yang_ada = [kol for kol in kolom_untuk_dihapus if kol in df.columns]

if kolom_yang_ada:
    df.drop(columns=kolom_yang_ada, inplace=True)
    print(f"\nKolom berhasil dihapus: {kolom_yang_ada}")
else:
    print("\nTidak ada kolom ('user_name', 'at', 'thumbsUpCount') yang ditemukan untuk dihapus.")


print("\n--- Contoh 5 Baris Data Awal ---")
print(df.head())
print(f"\nJumlah ulasan awal: {len(df)}")
print(f"Nama kolom setelah penghapusan: {df.columns.tolist()}")


# --- Penanganan Kolom Ulasan dan Duplikat Awal ---
COLUMN_REVIEW_TEXT = 'ulasan'
if COLUMN_REVIEW_TEXT not in df.columns:
    print(f"\n❌ ERROR: Kolom '{COLUMN_REVIEW_TEXT}' tidak ditemukan.")
    exit()

df.drop_duplicates(subset=[COLUMN_REVIEW_TEXT], inplace=True)
print(f"\nJumlah ulasan setelah menghapus duplikat: {len(df)}")

df[COLUMN_REVIEW_TEXT] = df[COLUMN_REVIEW_TEXT].fillna('')

print("\nPemuatan data dan pembersihan kolom selesai.")

# sel 3
def preprocess_text(text):
    # 1. Case Folding (Mengubah menjadi huruf kecil semua)
    text = text.lower()

    # 2. Menghapus username (@username)
    text = re.sub(r'@[A-Za-z0-9_]+', '', text)

    # 3. Menghapus URL
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)

    # 4. Menghapus hashtag (#hashtag)
    text = re.sub(r'#\w+', '', text)

    # 5. Menghapus karakter non-alfabetik (selain huruf) dan angka
    # Ini juga akan menghapus tanda baca. Jika Anda ingin mempertahankan beberapa,
    # sesuaikan regex ini.
    text = re.sub(r'[^a-zA-Z\s]', '', text) # Hanya menyisakan huruf dan spasi

    # 6. Menghapus spasi berlebih
    text = re.sub(r'\s+', ' ', text).strip()

    return text

df['ulasan_bersih'] = df[COLUMN_REVIEW_TEXT].apply(preprocess_text)
print("\n--- Contoh Ulasan Setelah Pra-pemrosesan Awal ---")
print(df[['ulasan', 'ulasan_bersih']].head())
print("\nPra-pemrosesan awal selesai: 'ulasan_bersih' telah dibuat.")

#sel 4
# --- Kamus Normalisasi Kata (SANGAT PENTING UNTUK DIKEMBANGKAN) ---
# Tambahkan lebih banyak entri sesuai dengan data ulasan GoBiz Anda.
kamus_normalisasi = {
    'yg': 'yang', 'ga': 'tidak', 'gak': 'tidak', 'kalo': 'kalau', 'udah': 'sudah',
    'bgt': 'banget', 'banget': 'sekali', 'jg': 'juga', 'aja': 'saja', 'ok': 'oke',
    'bikin': 'membuat', 'gini': 'begini', 'gitu': 'begitu', 'byk': 'banyak',
    'udh': 'sudah', 'krn': 'karena', 'dlm': 'dalam', 'hrs': 'harus', 'jd': 'jadi',
    'bbrp': 'beberapa', 'sy': 'saya', 'brg': 'barang', 'hrsnya': 'seharusnya',
    'tks': 'terima kasih', 'mksh': 'terima kasih', 'trmksh': 'terima kasih',
    'dg': 'dengan', 'sdh': 'sudah', 'tdk': 'tidak', 'jgk': 'juga', 'mnrt': 'menurut',
    'tp': 'tapi', 'sm': 'sama', 'gk': 'tidak', 'gmn': 'bagaimana', 'gt': 'begitu',
    'bkn': 'bukan', 'srg': 'sering', 'pdhl': 'padahal', 'ato': 'atau', 'btw': 'omong-omong',
    'gpp': 'tidak apa-apa', 'btw': 'ngomong-ngomong', 'blg': 'bilang', 'mlh': 'malah',
    'sbnrnya': 'sebenarnya', 'tgl': 'tanggal', 'dgn': 'dengan', 'ktnya': 'katanya',
    'skrg': 'sekarang', 'bknnya': 'bukannya', 'klo': 'kalau', 'bgs': 'bagus', 'bnyk': 'banyak',
    'bgtu': 'begitu', 'tlt': 'telat', 'kl': 'kalau', 'd': 'di', 'dr': 'dari',
    'yg': 'yang', 'jd': 'jadi', 'utk': 'untuk', 'utk': 'untuk', 'hr': 'hari',
    'trs': 'terus', 'tdk': 'tidak', 'mntp': 'mantap', 'bgus': 'bagus', 'gk': 'tidak',
    'lgsg': 'langsung', 'dgn': 'dengan', 'pake': 'pakai', 'msh': 'masih',
    'klo': 'kalau', 'cpt': 'cepat', 'lbih': 'lebih', 'brmasalah': 'bermasalah',
    'aplikasinya': 'aplikasi', 'gobiznya': 'gobiz', # Contoh spesifik GoBiz
    'akunnya': 'akun', 'orderannya': 'order', 'fitur': 'fitur',
    # Tambahkan lebih banyak sesuai dengan data Anda
}

def normalisasi_teks(text):
    words = text.split()
    normalized_words = [kamus_normalisasi.get(word, word) for word in words]
    return ' '.join(normalized_words)

df['ulasan_normalisasi'] = df['ulasan_bersih'].apply(normalisasi_teks)
print("\n--- Contoh Ulasan Setelah Normalisasi ---")
print(df[['ulasan_bersih', 'ulasan_normalisasi']].head())
print("\nNormalisasi kata selesai: 'ulasan_normalisasi' telah dibuat.")

#sel 5
# --- Tokenisasi dan Penghapusan Stop Words ---
# Mengunduh stop words bahasa Indonesia (jika belum ada)
import nltk # Pastikan ini ada di sel ini atau di sel import awal
try:
    nltk.data.find('corpora/stopwords')
except Exception as e: # PERBAIKAN PENTING DI SINI! Gunakan Exception yang lebih umum
    print(f"Error saat mencari stopwords: {e}")
    print("Mencoba mengunduh NLTK stopwords...")
    nltk.download('stopwords')
    print("NLTK stopwords berhasil diunduh.") # Tambahkan pesan konfirmasi

from nltk.corpus import stopwords # Impor ini setelah dipastikan stopwords diunduh

list_stopwords = set(stopwords.words('indonesian'))

# Anda bisa menambahkan stop words kustom jika ada kata-kata umum di data Anda
# yang tidak relevan untuk sentimen.
# Contoh:
# list_stopwords.add("gobiz")
# list_stopwords.add("aplikasi")
# list_stopwords.add("sangat") # Hati-hati dengan kata penguat sentimen

def remove_stopwords(text):
    words = text.split()
    filtered_words = [word for word in words if word not in list_stopwords]
    return ' '.join(filtered_words)

df['ulasan_tanpa_stopwords'] = df['ulasan_normalisasi'].apply(remove_stopwords)
print("\n--- Contoh Ulasan Setelah Penghapusan Stop Words ---")
print(df[['ulasan_normalisasi', 'ulasan_tanpa_stopwords']].head())
print("\nPenghapusan stop words selesai: 'ulasan_tanpa_stopwords' telah dibuat.")


#sel 6
# --- Stemming (Mengubah kata ke bentuk dasar) ---
# Inisialisasi Stemmer Sastrawi
factory = StemmerFactory()
stemmer = factory.create_stemmer()

def perform_stemming(text):
    # Stemmer Sastrawi bisa mengembalikan string kosong jika inputnya kosong
    if not text:
        return ""
    return stemmer.stem(text)

df['ulasan_stemmed'] = df['ulasan_tanpa_stopwords'].apply(perform_stemming)
print("\n--- Contoh Ulasan Setelah Stemming ---")
print(df[['ulasan_tanpa_stopwords', 'ulasan_stemmed']].head())
print("\nStemming selesai: 'ulasan_stemmed' telah dibuat.")

# Sel 7: Menyimpan Hasil Akhir ke File Excel

print("\n--- Hasil Akhir Pra-pemrosesan untuk Beberapa Ulasan ---")
for i in range(min(5, len(df))):
    print(f"\nUlasan Asli [{i+1}]: {df['ulasan'].iloc[i]}")
    print(f"Ulasan Pra-proses Lengkap [{i+1}]: {df['ulasan_stemmed'].iloc[i]}")


# --- MENYIMPAN HASIL AKHIR KE FILE EXCEL (.xlsx) (PERUBAHAN) ---
# Memilih hanya kolom yang relevan: ulasan asli dan hasil stemmed.
# Kita juga akan menyertakan kolom 'nilai' (rating) jika ada, karena penting untuk analisis.
kolom_final_untuk_disimpan = ['app_name', 'ulasan', 'ulasan_stemmed']
if 'nilai' in df.columns:
    kolom_final_untuk_disimpan.append('nilai')

df_hasil_akhir = df[kolom_final_untuk_disimpan]

# Menentukan nama file output
output_xlsx_path = 'gobiz_ulasan_preprocessed.xlsx'
try:
    df_hasil_akhir.to_excel(output_xlsx_path, index=False, engine='openpyxl')
    print(f"\n✅ Data hasil pra-pemrosesan berhasil disimpan ke file Excel: '{output_xlsx_path}'")
    print(f"   File ini hanya berisi kolom: {df_hasil_akhir.columns.tolist()}")
    print("   Perhatian: File ini akan hilang saat sesi Colab berakhir. Pastikan untuk mengunduhnya jika perlu.")
except Exception as e:
    print(f"\n❌ Gagal menyimpan data ke {output_xlsx_path}: {e}")

print("\nSELURUH PRA-PEMROSESAN DATA TELAH SELESAI!")
print("Kolom **'ulasan_stemmed'** sekarang berisi data bersih dan siap untuk tahap selanjutnya.")