{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNQ02oeO4hp8HhkVKGeVXrj"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 930}, "id": "D2GviSIkln4z", "executionInfo": {"status": "error", "timestamp": 1753161997136, "user_tz": -420, "elapsed": 35052, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "6f482fe4-0472-4247-a68a-f8766ab14193"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting gensim\n", "  Downloading gensim-4.3.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.1 kB)\n", "Collecting numpy<2.0,>=1.18.5 (from gensim)\n", "  Downloading numpy-1.26.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (61 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.0/61.0 kB\u001b[0m \u001b[31m1.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting scipy<1.14.0,>=1.7.0 (from gensim)\n", "  Downloading scipy-1.13.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (60 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m60.6/60.6 kB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: smart-open>=1.8.1 in /usr/local/lib/python3.11/dist-packages (from gensim) (7.3.0.post1)\n", "Requirement already satisfied: wrapt in /usr/local/lib/python3.11/dist-packages (from smart-open>=1.8.1->gensim) (1.17.2)\n", "Downloading gensim-4.3.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (26.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m26.7/26.7 MB\u001b[0m \u001b[31m14.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading numpy-1.26.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (18.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m18.3/18.3 MB\u001b[0m \u001b[31m31.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading scipy-1.13.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (38.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m38.6/38.6 MB\u001b[0m \u001b[31m11.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: numpy, scipy, gensim\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 2.0.2\n", "    Uninstalling numpy-2.0.2:\n", "      Successfully uninstalled numpy-2.0.2\n", "  Attempting uninstall: scipy\n", "    Found existing installation: scipy 1.15.3\n", "    Uninstalling scipy-1.15.3:\n", "      Successfully uninstalled scipy-1.15.3\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "opencv-python-headless ********* requires numpy<2.3.0,>=2; python_version >= \"3.9\", but you have numpy 1.26.4 which is incompatible.\n", "tsfresh 0.21.0 requires scipy>=1.14.0; python_version >= \"3.10\", but you have scipy 1.13.1 which is incompatible.\n", "thinc 8.3.6 requires numpy<3.0.0,>=2.0.0, but you have numpy 1.26.4 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed gensim-4.3.3 numpy-1.26.4 scipy-1.13.1\n"]}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipython-input-1-*********.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;31m# Instalasi library yang mungkin belum ada di <PERSON>\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 4\u001b[0;31m \u001b[0mget_ipython\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msystem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'pip install gensim'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      5\u001b[0m \u001b[0mget_ipython\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msystem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'pip install openpyxl # TAMBAHAN: Diperlukan untuk menyimpan file .xlsx'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0mget_ipython\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msystem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'pip install sastrawi'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/google/colab/_shell.py\u001b[0m in \u001b[0;36msystem\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    150\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    151\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mpip_warn\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 152\u001b[0;31m       \u001b[0m_pip\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mprint_previous_import_warning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0moutput\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    153\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    154\u001b[0m   \u001b[0;32mdef\u001b[0m \u001b[0m_send_error\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexc_content\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/google/colab/_pip.py\u001b[0m in \u001b[0;36mprint_previous_import_warning\u001b[0;34m(output)\u001b[0m\n\u001b[1;32m     54\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0mprint_previous_import_warning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0moutput\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     55\u001b[0m   \u001b[0;34m\"\"\"Prints a warning about previously imported packages.\"\"\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 56\u001b[0;31m   \u001b[0mpackages\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0m_previously_imported_packages\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0moutput\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     57\u001b[0m   \u001b[0;32mif\u001b[0m \u001b[0mpackages\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     58\u001b[0m     \u001b[0;31m# display a list of packages using the colab-display-data mimetype, which\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/google/colab/_pip.py\u001b[0m in \u001b[0;36m_previously_imported_packages\u001b[0;34m(pip_output)\u001b[0m\n\u001b[1;32m     48\u001b[0m \u001b[0;32mdef\u001b[0m \u001b[0m_previously_imported_packages\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpip_output\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     49\u001b[0m   \u001b[0;34m\"\"\"List all previously imported packages from a pip install.\"\"\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 50\u001b[0;31m   \u001b[0minstalled\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_extract_toplevel_packages\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpip_output\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     51\u001b[0m   \u001b[0;32mreturn\u001b[0m \u001b[0msorted\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minstalled\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mintersection\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msys\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmodules\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     52\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/google/colab/_pip.py\u001b[0m in \u001b[0;36m_extract_toplevel_packages\u001b[0;34m(pip_output)\u001b[0m\n\u001b[1;32m     37\u001b[0m   \u001b[0;34m\"\"\"Extract the list of toplevel packages associated with a pip install.\"\"\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     38\u001b[0m   \u001b[0mtoplevel\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcollections\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdefaultdict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mset\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 39\u001b[0;31m   \u001b[0;32mfor\u001b[0m \u001b[0mm\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mps\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mimportlib\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpackages_distributions\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mitems\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     40\u001b[0m     \u001b[0;32mfor\u001b[0m \u001b[0mp\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mps\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     41\u001b[0m       \u001b[0mtoplevel\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0madd\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mm\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/lib/python3.11/importlib/metadata/__init__.py\u001b[0m in \u001b[0;36mpackages_distributions\u001b[0;34m()\u001b[0m\n\u001b[1;32m   1073\u001b[0m     \u001b[0mpkg_to_dist\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcollections\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdefaultdict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlist\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1074\u001b[0m     \u001b[0;32mfor\u001b[0m \u001b[0mdist\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mdistributions\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1075\u001b[0;31m         \u001b[0;32mfor\u001b[0m \u001b[0mpkg\u001b[0m \u001b[0;32min\u001b[0m \u001b[0m_top_level_declared\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdist\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0m_top_level_inferred\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdist\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1076\u001b[0m             \u001b[0mpkg_to_dist\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mpkg\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdist\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmetadata\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'Name'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1077\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0mdict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpkg_to_dist\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/lib/python3.11/importlib/metadata/__init__.py\u001b[0m in \u001b[0;36m_top_level_inferred\u001b[0;34m(dist)\u001b[0m\n\u001b[1;32m   1085\u001b[0m     return {\n\u001b[1;32m   1086\u001b[0m         \u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mparts\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mparts\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m \u001b[0;32melse\u001b[0m \u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwith_suffix\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m''\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1087\u001b[0;31m         \u001b[0;32mfor\u001b[0m \u001b[0mf\u001b[0m \u001b[0;32min\u001b[0m \u001b[0malways_iterable\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdist\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfiles\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1088\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msuffix\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m\".py\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1089\u001b[0m     }\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/importlib_metadata/__init__.py\u001b[0m in \u001b[0;36mfiles\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    602\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mlist\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilter\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mlambda\u001b[0m \u001b[0mpath\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlocate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpackage_paths\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    603\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 604\u001b[0;31m         return skip_missing_files(\n\u001b[0m\u001b[1;32m    605\u001b[0m             make_files(\n\u001b[1;32m    606\u001b[0m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_read_files_distinfo\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/importlib_metadata/_functools.py\u001b[0m in \u001b[0;36mwrapper\u001b[0;34m(param, *args, **kwargs)\u001b[0m\n\u001b[1;32m    100\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mparam\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    101\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mparam\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 102\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mparam\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    103\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    104\u001b[0m     \u001b[0;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/importlib_metadata/__init__.py\u001b[0m in \u001b[0;36mskip_missing_files\u001b[0;34m(package_paths)\u001b[0m\n\u001b[1;32m    600\u001b[0m         \u001b[0;34m@\u001b[0m\u001b[0mpass_none\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    601\u001b[0m         \u001b[0;32mdef\u001b[0m \u001b[0mskip_missing_files\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpackage_paths\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 602\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mlist\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilter\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mlambda\u001b[0m \u001b[0mpath\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlocate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpackage_paths\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    603\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    604\u001b[0m         return skip_missing_files(\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/importlib_metadata/__init__.py\u001b[0m in \u001b[0;36m<lambda>\u001b[0;34m(path)\u001b[0m\n\u001b[1;32m    600\u001b[0m         \u001b[0;34m@\u001b[0m\u001b[0mpass_none\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    601\u001b[0m         \u001b[0;32mdef\u001b[0m \u001b[0mskip_missing_files\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpackage_paths\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 602\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mlist\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilter\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mlambda\u001b[0m \u001b[0mpath\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlocate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpackage_paths\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    603\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    604\u001b[0m         return skip_missing_files(\n", "\u001b[0;32m/usr/lib/python3.11/pathlib.py\u001b[0m in \u001b[0;36mexists\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1233\u001b[0m         \"\"\"\n\u001b[1;32m   1234\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1235\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1236\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mOSError\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1237\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0m_ignore_error\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0me\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/lib/python3.11/pathlib.py\u001b[0m in \u001b[0;36mstat\u001b[0;34m(self, follow_symlinks)\u001b[0m\n\u001b[1;32m   1011\u001b[0m         \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0mdoes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1012\u001b[0m         \"\"\"\n\u001b[0;32m-> 1013\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfollow_symlinks\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfollow_symlinks\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1014\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1015\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mowner\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# Sel 1: Impor Library dan <PERSON>\n", "\n", "# Instalasi library yang mungkin belum ada di <PERSON>b\n", "!pip install gensim\n", "!pip install openpyxl # TAMBAHAN: <PERSON><PERSON>lukan untuk menyimpan file .xlsx\n", "!pip install sastrawi\n", "\n", "import pandas as pd\n", "import re\n", "from Sastrawi.Stemmer.StemmerFactory import StemmerFactory\n", "import nltk # Diimpor di sini untuk konsistensi\n", "from nltk.corpus import stopwords\n", "import string\n", "import warnings\n", "from google.colab import files\n", "import csv\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Mengunduh NLTK stopwords\n", "try:\n", "    nltk.data.find('corpora/stopwords')\n", "except LookupError:\n", "    print(\"Mencoba mengunduh NLTK stopwords...\")\n", "    nltk.download('stopwords')\n", "    print(\"NLTK stopwords berhasil diunduh.\")\n", "\n", "print(\"✅ Library berhasil diimpor dan lingkungan siap.\")"]}, {"cell_type": "code", "source": ["# Sel 2: Pemuatan Data dari File Excel\n", "\n", "# --- Tentukan path ke file Excel Anda ---\n", "# Ganti 'gobiz_ulasan.xlsx' dengan nama file Anda.\n", "# Jika file ada di <PERSON> Drive, path-nya akan seperti: '/content/drive/MyDrive/FolderAnda/gobiz_ulasan.xlsx'\n", "file_path = 'gobiz_ulasan.xlsx'\n", "\n", "print(f\"Mencoba memuat data dari: {file_path}...\")\n", "\n", "# --- Memuat Data ke DataFrame dari File Excel ---\n", "try:\n", "    # Menggunakan pd.read_excel untuk memuat file .xlsx\n", "    df = pd.read_excel(file_path)\n", "    print(f\"✅ Data berhasil dimuat dari: {file_path}\")\n", "\n", "except FileNotFoundError:\n", "    print(f\"\\n❌ ERROR: File '{file_path}' tidak ditemukan.\")\n", "    print(\"   Pastikan nama file sudah benar dan file berada di direktori yang sama,\")\n", "    print(\"   atau pastikan Google Drive Anda sudah terhubung (mounted) jika file ada di sana.\")\n", "    exit() # Hentikan eksekusi jika file tidak ada\n", "except Exception as e:\n", "    print(f\"\\n❌ GAGAL MEMUAT FILE EXCEL: {e}\")\n", "    exit()\n", "\n", "# --- MENGHAPUS KOLOM YANG TIDAK DIPERLUKAN ---\n", "kolom_untuk_dihapus = ['user_name', 'at', 'thumbsUpCount']\n", "kolom_yang_ada = [kol for kol in kolom_untuk_dihapus if kol in df.columns]\n", "\n", "if kolom_yang_ada:\n", "    df.drop(columns=kolom_yang_ada, inplace=True)\n", "    print(f\"\\nKolom berhasil dihapus: {kolom_yang_ada}\")\n", "else:\n", "    print(\"\\nTidak ada kolom ('user_name', 'at', 'thumbsUpCount') yang ditemukan untuk dihapus.\")\n", "\n", "\n", "print(\"\\n--- <PERSON>toh 5 Baris Data Awal ---\")\n", "print(df.head())\n", "print(f\"\\nJumlah ulasan awal: {len(df)}\")\n", "print(f\"Nama kolom setelah pengh<PERSON>usan: {df.columns.tolist()}\")\n", "\n", "\n", "# --- <PERSON><PERSON><PERSON> dan Du<PERSON> Awal ---\n", "COLUMN_REVIEW_TEXT = 'ulasan'\n", "if COLUMN_REVIEW_TEXT not in df.columns:\n", "    print(f\"\\n❌ ERROR: Kolom '{COLUMN_REVIEW_TEXT}' tidak ditemukan.\")\n", "    exit()\n", "\n", "df.drop_duplicates(subset=[COLUMN_REVIEW_TEXT], inplace=True)\n", "print(f\"\\nJumlah ulasan setelah menghapus duplikat: {len(df)}\")\n", "\n", "df[COLUMN_REVIEW_TEXT] = df[COLUMN_REVIEW_TEXT].fillna('')\n", "\n", "print(\"\\nPemuatan data dan pembersihan kolom selesai.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gPXUcRpilvvi", "executionInfo": {"status": "ok", "timestamp": 1752860455428, "user_tz": -420, "elapsed": 2204, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "5e34b77a-98d3-4470-f94f-5588d51fd391"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mencoba memuat data dari: gobiz_ulasan.xlsx...\n", "✅ Data berhasil dimuat dari: gobiz_ulasan.xlsx\n", "\n", "<PERSON><PERSON><PERSON> ber<PERSON> di<PERSON>: ['user_name', 'at', 'thumbsUpCount']\n", "\n", "--- Contoh 5 Baris Data Awal ---\n", "  app_name  ni<PERSON>                                             ul<PERSON>n\n", "0    Gobiz      2  Smntra bintang 2 aj msih pemula nanti kalau hs...\n", "1    Gobiz      1  Makin kesini makin ga jela<PERSON>,lama nunggu instru...\n", "2    Gobiz      2       St<PERSON><PERSON>,<PERSON><PERSON><PERSON> <PERSON><PERSON>,<PERSON> perubahan....jd Maless\n", "3    Gobiz      3  Tolong kalo buat promosi yang jelas konsep itu...\n", "4    Gobiz      1  Makin di update makin jelek, gada notif tau² d...\n", "\n", "<PERSON><PERSON><PERSON> awal: 20000\n", "<PERSON>a kolom setelah pengh<PERSON>n: ['app_name', 'nilai', 'ulasan']\n", "\n", "<PERSON><PERSON><PERSON> setelah menghapus duplikat: 15797\n", "\n", "Pemuatan data dan pembersihan kolom selesai.\n"]}]}, {"cell_type": "code", "source": ["# sel 3\n", "def preprocess_text(text):\n", "    # 1. <PERSON> Folding (Mengu<PERSON> menja<PERSON> huruf kecil semua)\n", "    text = text.lower()\n", "\n", "    # 2. <PERSON>gh<PERSON>us username (@username)\n", "    text = re.sub(r'@[A-Za-z0-9_]+', '', text)\n", "\n", "    # 3. Menghapus URL\n", "    text = re.sub(r'http\\S+|www\\S+|https\\S+', '', text, flags=re.MULTILINE)\n", "\n", "    # 4. <PERSON><PERSON><PERSON>us hashtag (#hashtag)\n", "    text = re.sub(r'#\\w+', '', text)\n", "\n", "    # 5. <PERSON><PERSON><PERSON><PERSON> ka<PERSON> non-al<PERSON><PERSON><PERSON> (se<PERSON> huruf) dan angka\n", "    # Ini juga akan menghapus tanda baca. <PERSON><PERSON>a ingin memper<PERSON> beber<PERSON>,\n", "    # sesuaikan regex ini.\n", "    text = re.sub(r'[^a-zA-Z\\s]', '', text) # <PERSON><PERSON> menyi<PERSON>kan huruf dan spasi\n", "\n", "    # 6. <PERSON><PERSON><PERSON><PERSON> spasi berle<PERSON>h\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "\n", "    return text\n", "\n", "df['ulasan_bersih'] = df[COLUMN_REVIEW_TEXT].apply(preprocess_text)\n", "print(\"\\n--- <PERSON><PERSON><PERSON>-p<PERSON><PERSON><PERSON> ---\")\n", "print(df[['ulasan', 'ulasan_bersih']].head())\n", "print(\"\\nPra-pem<PERSON>san awal selesai: 'ulasan_bersih' telah dibuat.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A_3CjaXSl3ll", "executionInfo": {"status": "ok", "timestamp": 1752860541811, "user_tz": -420, "elapsed": 425, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "65cee381-9280-4cd1-a92c-9d377063b3f8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> ---\n", "                                              ulasan  \\\n", "0  Smntra bintang 2 aj msih pemula nanti kalau hs...   \n", "1  Makin kesini makin ga jela<PERSON>,lama nunggu instru...   \n", "2       <PERSON><PERSON><PERSON>,beg<PERSON><PERSON> aja,<PERSON> per<PERSON><PERSON>....jd Maless   \n", "3  <PERSON><PERSON> kalo buat promosi yang jelas konsep itu...   \n", "4  Makin di update makin jelek, gada notif tau² d...   \n", "\n", "                                       ulasan_bersih  \n", "0  smntra bintang aj msih pemula nanti kalau hsil...  \n", "1  makin kesini makin ga jelaslama nunggu instruk...  \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless  \n", "3  tolong kalo buat promosi yang jelas konsep itu...  \n", "4  makin di update makin jelek gada notif tau dri...  \n", "\n", "Pra-<PERSON><PERSON><PERSON><PERSON> awal se<PERSON>ai: 'ulasan_bersih' telah dibuat.\n"]}]}, {"cell_type": "code", "source": ["#sel 4\n", "# --- <PERSON><PERSON> (SANGAT PENTING UNTUK DIKEMBANGKAN) ---\n", "# Tambahkan lebih banyak entri sesuai dengan data ulasan GoBiz Anda.\n", "kamus_normalisasi = {\n", "    'yg': 'yang', 'ga': 'tidak', 'gak': 'tidak', 'kalo': 'kalau', 'udah': 'sudah',\n", "    'bgt': 'banget', 'banget': 'sekali', 'jg': 'juga', 'aja': 'saja', 'ok': 'oke',\n", "    'bikin': 'membuat', 'gini': 'begini', 'gitu': 'begitu', 'byk': 'banyak',\n", "    'udh': 'sudah', 'krn': 'karena', 'dlm': 'dalam', 'hrs': 'harus', 'jd': 'jadi',\n", "    'bbrp': 'beber<PERSON>', 'sy': 'saya', 'brg': 'barang', 'hrsnya': 'seharusnya',\n", "    'tks': 'terima kasih', 'mksh': 'terima kasih', 'trmksh': 'terima kasih',\n", "    'dg': 'dengan', 'sdh': 'sudah', 'tdk': 'tidak', 'jgk': 'juga', 'mnrt': 'menurut',\n", "    'tp': 'tapi', 'sm': 'sama', 'gk': 'tidak', 'gmn': 'bagaimana', 'gt': 'begitu',\n", "    'bkn': 'bukan', 'srg': 'sering', 'pdhl': 'padahal', 'ato': 'atau', 'btw': 'omong-omong',\n", "    'gpp': 'tidak apa-apa', 'btw': 'ngomong-ngomong', 'blg': 'bilang', 'mlh': 'malah',\n", "    'sbnrnya': 'sebenarnya', 'tgl': 'tanggal', 'dgn': 'dengan', 'ktnya': 'katanya',\n", "    'skrg': 'sekarang', 'bknnya': 'bukannya', 'klo': 'kalau', 'bgs': 'bagus', 'bnyk': 'banyak',\n", "    'bgtu': 'begitu', 'tlt': 'telat', 'kl': 'kalau', 'd': 'di', 'dr': 'dari',\n", "    'yg': 'yang', 'jd': 'jadi', 'utk': 'untuk', 'utk': 'untuk', 'hr': 'hari',\n", "    'trs': 'terus', 'tdk': 'tidak', 'mntp': 'mantap', 'bgus': 'bagus', 'gk': 'tidak',\n", "    'lgsg': 'langsung', 'dgn': 'dengan', 'pake': 'pakai', 'msh': 'masih',\n", "    'klo': 'kalau', 'cpt': 'cepat', 'lbih': 'lebih', 'brmasalah': 'bermasalah',\n", "    'aplikasinya': 'aplikasi', 'gobiznya': 'gobiz', # Contoh spesifik GoBiz\n", "    'akunnya': 'akun', 'order<PERSON>ya': 'order', 'fitur': 'fitur',\n", "    # Tambahkan lebih banyak sesuai dengan data Anda\n", "}\n", "\n", "def normalisasi_teks(text):\n", "    words = text.split()\n", "    normalized_words = [kamus_normalisasi.get(word, word) for word in words]\n", "    return ' '.join(normalized_words)\n", "\n", "df['ulasan_normalisasi'] = df['ulasan_bersih'].apply(normalisasi_teks)\n", "print(\"\\n--- <PERSON><PERSON><PERSON> ---\")\n", "print(df[['ulasan_bersih', 'ulasan_normalisasi']].head())\n", "print(\"\\nNormalisasi kata selesai: 'ulasan_normalisasi' telah dibuat.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Rdrx0qP2mTWJ", "executionInfo": {"status": "ok", "timestamp": 1752860550911, "user_tz": -420, "elapsed": 82, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "17bec976-e311-4dd7-d1b8-2beb28c8029e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- <PERSON><PERSON><PERSON> ---\n", "                                       ulasan_bersih  \\\n", "0  smntra bintang aj msih pemula nanti kalau hsil...   \n", "1  makin kesini makin ga jelaslama nunggu instruk...   \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless   \n", "3  tolong kalo buat promosi yang jelas konsep itu...   \n", "4  makin di update makin jelek gada notif tau dri...   \n", "\n", "                                  ulasan_normalisasi  \n", "0  smntra bintang aj msih pemula nanti kalau hsil...  \n", "1  makin kesini makin tidak jelaslama nunggu inst...  \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless  \n", "3  tolong kalau buat promosi yang jelas konsep it...  \n", "4  makin di update makin jelek gada notif tau dri...  \n", "\n", "Normalisasi kata selesai: 'ulasan_normalisasi' telah dibuat.\n"]}]}, {"cell_type": "code", "source": ["#sel 5\n", "# --- <PERSON><PERSON><PERSON><PERSON> dan <PERSON> Stop Words ---\n", "# Mengunduh stop words bahasa Indonesia (jika belum ada)\n", "import nltk # Pastikan ini ada di sel ini atau di sel import awal\n", "try:\n", "    nltk.data.find('corpora/stopwords')\n", "except Exception as e: # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PENTING DI SINI! Gunakan Exception yang lebih umum\n", "    print(f\"Error saat mencari stopwords: {e}\")\n", "    print(\"Mencoba mengunduh NLTK stopwords...\")\n", "    nltk.download('stopwords')\n", "    print(\"NLTK stopwords berhasil diunduh.\") # Tambahkan pesan konfirmasi\n", "\n", "from nltk.corpus import stopwords # Impor ini setelah dipastikan stopwords diunduh\n", "\n", "list_stopwords = set(stopwords.words('indonesian'))\n", "\n", "# Anda bisa menambahkan stop words kustom jika ada kata-kata umum di data Anda\n", "# yang tidak relevan untuk sentimen.\n", "# Contoh:\n", "# list_stopwords.add(\"gobiz\")\n", "# list_stopwords.add(\"aplikasi\")\n", "# list_stopwords.add(\"sangat\") # Hati-hati dengan kata penguat sentimen\n", "\n", "def remove_stopwords(text):\n", "    words = text.split()\n", "    filtered_words = [word for word in words if word not in list_stopwords]\n", "    return ' '.join(filtered_words)\n", "\n", "df['ulasan_tanpa_stopwords'] = df['ulasan_normalisasi'].apply(remove_stopwords)\n", "print(\"\\n--- <PERSON><PERSON><PERSON> Stop Words ---\")\n", "print(df[['ulasan_normalisasi', 'ulasan_tanpa_stopwords']].head())\n", "print(\"\\nPenghapusan stop words selesai: 'ulasan_tanpa_stopwords' telah dibuat.\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UXXeIIXYmSVe", "executionInfo": {"status": "ok", "timestamp": 1752860555226, "user_tz": -420, "elapsed": 65, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "9bfd6e5f-9e95-4c63-a67b-d6c7cfb64081"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- <PERSON><PERSON><PERSON> Stop Words ---\n", "                                  ulasan_normalisasi  \\\n", "0  smntra bintang aj msih pemula nanti kalau hsil...   \n", "1  makin kesini makin tidak jelaslama nunggu inst...   \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless   \n", "3  tolong kalau buat promosi yang jelas konsep it...   \n", "4  makin di update makin jelek gada notif tau dri...   \n", "\n", "                              ulasan_tanpa_stopwords  \n", "0  smntra bintang aj msih pemula hsilnya puas tam...  \n", "1   kesini jelaslama nunggu instruksinyasistem kacau  \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless  \n", "3  tolong promosi konsep itunganya maasak dibeban...  \n", "4  update j<PERSON>k gada notif tau driver dtg ambil p...  \n", "\n", "Penghapusan stop words selesai: 'ulasan_tanpa_stopwords' telah dibuat.\n"]}]}, {"cell_type": "code", "source": ["#sel 6\n", "# --- <PERSON><PERSON><PERSON> (Mengubah kata ke bentuk dasar) ---\n", "# Inisialisasi Stemmer <PERSON>wi\n", "factory = StemmerFactory()\n", "stemmer = factory.create_stemmer()\n", "\n", "def perform_stemming(text):\n", "    # <PERSON><PERSON>mer Sastrawi bisa mengembalikan string kosong jika inputnya kosong\n", "    if not text:\n", "        return \"\"\n", "    return stemmer.stem(text)\n", "\n", "df['ulasan_stemmed'] = df['ulasan_tanpa_stopwords'].apply(perform_stemming)\n", "print(\"\\n--- <PERSON><PERSON><PERSON>emming ---\")\n", "print(df[['ulasan_tanpa_stopwords', 'ulasan_stemmed']].head())\n", "print(\"\\nStemming selesai: 'ulasan_stemmed' telah dibuat.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pzOLl1nWmSO8", "executionInfo": {"status": "ok", "timestamp": 1752862641744, "user_tz": -420, "elapsed": 2081410, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "d01365a7-aaf4-4702-ccbc-dd7d254e78f8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- <PERSON><PERSON><PERSON> ---\n", "                              ulasan_tanpa_stopwords  \\\n", "0  smntra bintang aj msih pemula hsilnya puas tam...   \n", "1   kesini jelaslama nunggu instruksinyasistem kacau   \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless   \n", "3  tolong promosi konsep itunganya maasak dibeban...   \n", "4  update j<PERSON>k gada notif tau driver dtg ambil p...   \n", "\n", "                                      ulasan_stemmed  \n", "0  smntra bintang aj msih mula hsilnya puas tamba...  \n", "1   kesini jelaslama nunggu instruksinyasistem kacau  \n", "2             stagnan<PERSON><PERSON>u a<PERSON> maless  \n", "3  tolong promosi konsep itung<PERSON>a maasak beban r...  \n", "4  update j<PERSON>k gada notif tau driver dtg ambil s...  \n", "\n", "<PERSON><PERSON><PERSON> selesai: 'ulasan_stemmed' telah dibuat.\n"]}]}, {"cell_type": "code", "source": ["# Sel 7: <PERSON><PERSON><PERSON><PERSON> ke File Excel\n", "\n", "print(\"\\n--- <PERSON><PERSON><PERSON> untuk Be<PERSON> ---\")\n", "for i in range(min(5, len(df))):\n", "    print(f\"\\n<PERSON><PERSON><PERSON> [{i+1}]: {df['ulasan'].iloc[i]}\")\n", "    print(f\"<PERSON><PERSON><PERSON>-<PERSON><PERSON> [{i+1}]: {df['ulasan_stemmed'].iloc[i]}\")\n", "\n", "\n", "# --- MENYIMPAN HASIL AKHIR KE FILE EXCEL (.xlsx) (PERUBAHAN) ---\n", "# Me<PERSON>lih hanya kolom yang relevan: ulasan asli dan hasil stemmed.\n", "# Kita juga akan men<PERSON>kan kolom 'nilai' (rating) jika ada, karena penting untuk analisis.\n", "kolom_final_untuk_disimpan = ['app_name', 'ulasan', 'ulasan_stemmed']\n", "if 'nilai' in df.columns:\n", "    kolom_final_untuk_disimpan.append('nilai')\n", "\n", "df_hasil_akhir = df[kolom_final_untuk_disimpan]\n", "\n", "# Menentukan nama file output\n", "output_xlsx_path = 'gobiz_ulasan_preprocessed.xlsx'\n", "try:\n", "    df_hasil_akhir.to_excel(output_xlsx_path, index=False, engine='openpyxl')\n", "    print(f\"\\n✅ Data hasil pra-pemrosesan berhasil disimpan ke file Excel: '{output_xlsx_path}'\")\n", "    print(f\"   File ini hanya berisi kolom: {df_hasil_akhir.columns.tolist()}\")\n", "    print(\"   Perhatian: File ini akan hilang saat sesi <PERSON>b berakhir. Pastikan untuk mengunduhnya jika perlu.\")\n", "except Exception as e:\n", "    print(f\"\\n❌ Gagal menyimpan data ke {output_xlsx_path}: {e}\")\n", "\n", "print(\"\\nSELURUH PRA-PEMROSESAN DATA TELAH SELESAI!\")\n", "print(\"Kolom **'ulasan_stemmed'** sekarang berisi data bersih dan siap untuk tahap selanjutnya.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CmigMkFvmSLZ", "executionInfo": {"status": "ok", "timestamp": 1752862867907, "user_tz": -420, "elapsed": 1506, "user": {"displayName": "<PERSON><PERSON>", "userId": "01379271228513537741"}}, "outputId": "fe355346-5202-4c3c-c71c-35cfef4fc8c3"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "--- <PERSON><PERSON><PERSON> untuk Be<PERSON> ---\n", "\n", "<PERSON><PERSON><PERSON> [1]: <PERSON><PERSON><PERSON>ra bintang 2 aj msih pemula nanti kalau hsilnya puas saya tambahin bintangnya\n", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> [1]: sm<PERSON><PERSON> bintang aj msih mula hsilnya puas tambahin bintang\n", "\n", "<PERSON><PERSON><PERSON> [2]: <PERSON><PERSON> kesini makin ga jela<PERSON>,lama nunggu instr<PERSON><PERSON>,sistem kacau\n", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> [2]: kesini j<PERSON> nunggu instruksinyasistem kacau\n", "\n", "<PERSON><PERSON><PERSON> [3]: <PERSON><PERSON><PERSON>,beg<PERSON><PERSON> aja,<PERSON> per<PERSON>han....jd Maless\n", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> [3]: stagnan<PERSON><PERSON><PERSON> a<PERSON>o <PERSON>s\n", "\n", "<PERSON><PERSON><PERSON> [4]: <PERSON><PERSON> kalo buat promosi yang jelas konsep itunganya maasak dibebankan ke resto semua..\n", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> [4]: tolong promosi konsep itunganya maasak beban resto\n", "\n", "<PERSON><PERSON><PERSON> [5]: <PERSON><PERSON> di update makin j<PERSON>, gada notif tau² driver dtg am<PERSON> p<PERSON>, kn kasian drivernya jd nungguin lamaaa ... trrnyaa aplikasi hrs stay baru ada notif pdhl dulu ga bgtu\n", "<PERSON><PERSON><PERSON>-<PERSON><PERSON> [5]: update j<PERSON>k gada notif tau driver dtg ambil sen kn kasi driver<PERSON> nungguin lamaaa trrnyaa aplikasi stay notif\n", "\n", "✅ Data hasil pra-pemrosesan berhasil disimpan ke file Excel: 'gobiz_ulasan_preprocessed.xlsx'\n", "   File ini hanya berisi kolom: ['app_name', 'ulasan', 'ulasan_stemmed', 'nilai']\n", "   Perhatian: File ini akan hilang saat sesi <PERSON>b berakhir. Pastikan untuk mengunduhnya jika perlu.\n", "\n", "SELURUH PRA-PEMROSESAN DATA TELAH SELESAI!\n", "Kolom **'ulasan_stemmed'** sekarang berisi data bersih dan siap untuk tahap selanjutnya.\n"]}]}]}