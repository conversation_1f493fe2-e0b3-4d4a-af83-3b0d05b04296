# Preprocessing Script - Converted from Jupyter Notebook
# Ana<PERSON><PERSON>n untuk Data GoBiz

import pandas as pd
import re
from Sastrawi.Stemmer.StemmerFactory import StemmerFactory
import nltk
from nltk.corpus import stopwords
import string
import warnings
import csv

warnings.filterwarnings('ignore')

print("Starting preprocessing script...")

# Download NLTK stopwords if not available
try:
    nltk.data.find('corpora/stopwords')
    print("NLTK stopwords already available.")
except LookupError:
    print("Downloading NLTK stopwords...")
    nltk.download('stopwords')
    print("NLTK stopwords downloaded successfully.")

print("✅ Libraries imported and environment ready.")

# Load data from Excel file
file_path = 'reviews_gofood_Merchant.xlsx'  # Updated to use the actual file in directory

print(f"Attempting to load data from: {file_path}...")

try:
    # Load Excel file
    df = pd.read_excel(file_path)
    print(f"✅ Data successfully loaded from: {file_path}")
    print(f"Data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")

except FileNotFoundError:
    print(f"\n❌ ERROR: File '{file_path}' not found.")
    print("   Make sure the file name is correct and the file is in the same directory.")
    exit()
except Exception as e:
    print(f"\n❌ FAILED TO LOAD EXCEL FILE: {e}")
    exit()

# Display first few rows
print("\n--- First 5 rows of data ---")
print(df.head())

# Check for required columns and clean up
print(f"\nInitial number of reviews: {len(df)}")

# Remove unnecessary columns if they exist
columns_to_remove = ['user_name', 'at', 'thumbsUpCount']
existing_columns = [col for col in columns_to_remove if col in df.columns]

if existing_columns:
    df.drop(columns=existing_columns, inplace=True)
    print(f"\nColumns removed: {existing_columns}")
else:
    print("\nNo unnecessary columns found to remove.")

print(f"Remaining columns: {df.columns.tolist()}")

# Identify the review text column
COLUMN_REVIEW_TEXT = None
possible_review_columns = ['ulasan', 'review', 'text', 'content', 'comment']

for col in possible_review_columns:
    if col in df.columns:
        COLUMN_REVIEW_TEXT = col
        break

if COLUMN_REVIEW_TEXT is None:
    print(f"\n❌ ERROR: No review text column found. Available columns: {df.columns.tolist()}")
    print("Please check your data file and ensure it has a column with review text.")
    exit()

print(f"\nUsing '{COLUMN_REVIEW_TEXT}' as the review text column.")

# Remove duplicates and handle missing values
df.drop_duplicates(subset=[COLUMN_REVIEW_TEXT], inplace=True)
print(f"\nNumber of reviews after removing duplicates: {len(df)}")

df[COLUMN_REVIEW_TEXT] = df[COLUMN_REVIEW_TEXT].fillna('')

print("\nData loading and initial cleanup completed.")

# Text preprocessing function
def preprocess_text(text):
    # 1. Case Folding (Convert to lowercase)
    text = text.lower()
    
    # 2. Remove usernames (@username)
    text = re.sub(r'@[A-Za-z0-9_]+', '', text)
    
    # 3. Remove URLs
    text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
    
    # 4. Remove hashtags (#hashtag)
    text = re.sub(r'#\w+', '', text)
    
    # 5. Remove non-alphabetic characters (except letters) and numbers
    text = re.sub(r'[^a-zA-Z\s]', '', text)  # Only keep letters and spaces
    
    # 6. Remove extra spaces
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

# Apply preprocessing
df['ulasan_bersih'] = df[COLUMN_REVIEW_TEXT].apply(preprocess_text)
print("\n--- Sample reviews after initial preprocessing ---")
print(df[[COLUMN_REVIEW_TEXT, 'ulasan_bersih']].head())
print("\nInitial preprocessing completed: 'ulasan_bersih' column created.")

# Text normalization dictionary
kamus_normalisasi = {
    'yg': 'yang', 'ga': 'tidak', 'gak': 'tidak', 'kalo': 'kalau', 'udah': 'sudah',
    'bgt': 'banget', 'banget': 'sekali', 'jg': 'juga', 'aja': 'saja', 'ok': 'oke',
    'bikin': 'membuat', 'gini': 'begini', 'gitu': 'begitu', 'byk': 'banyak',
    'udh': 'sudah', 'krn': 'karena', 'dlm': 'dalam', 'hrs': 'harus', 'jd': 'jadi',
    'bbrp': 'beberapa', 'sy': 'saya', 'brg': 'barang', 'hrsnya': 'seharusnya',
    'tks': 'terima kasih', 'mksh': 'terima kasih', 'trmksh': 'terima kasih',
    'dg': 'dengan', 'sdh': 'sudah', 'tdk': 'tidak', 'jgk': 'juga', 'mnrt': 'menurut',
    'tp': 'tapi', 'sm': 'sama', 'gk': 'tidak', 'gmn': 'bagaimana', 'gt': 'begitu',
    'bkn': 'bukan', 'srg': 'sering', 'pdhl': 'padahal', 'ato': 'atau', 'btw': 'omong-omong',
    'gpp': 'tidak apa-apa', 'blg': 'bilang', 'mlh': 'malah',
    'sbnrnya': 'sebenarnya', 'tgl': 'tanggal', 'dgn': 'dengan', 'ktnya': 'katanya',
    'skrg': 'sekarang', 'bknnya': 'bukannya', 'klo': 'kalau', 'bgs': 'bagus', 'bnyk': 'banyak',
    'bgtu': 'begitu', 'tlt': 'telat', 'kl': 'kalau', 'd': 'di', 'dr': 'dari',
    'trs': 'terus', 'mntp': 'mantap', 'bgus': 'bagus',
    'lgsg': 'langsung', 'pake': 'pakai', 'msh': 'masih',
    'cpt': 'cepat', 'lbih': 'lebih', 'brmasalah': 'bermasalah',
    'aplikasinya': 'aplikasi', 'gobiznya': 'gobiz',
    'akunnya': 'akun', 'orderannya': 'order', 'fitur': 'fitur',
    'utk': 'untuk', 'hr': 'hari'
}

def normalisasi_teks(text):
    words = text.split()
    normalized_words = [kamus_normalisasi.get(word, word) for word in words]
    return ' '.join(normalized_words)

# Apply normalization
df['ulasan_normalisasi'] = df['ulasan_bersih'].apply(normalisasi_teks)
print("\n--- Sample reviews after normalization ---")
print(df[['ulasan_bersih', 'ulasan_normalisasi']].head())
print("\nText normalization completed: 'ulasan_normalisasi' column created.")

# Tokenization and stopword removal
from nltk.corpus import stopwords

list_stopwords = set(stopwords.words('indonesian'))

def remove_stopwords(text):
    words = text.split()
    filtered_words = [word for word in words if word not in list_stopwords]
    return ' '.join(filtered_words)

# Apply stopword removal
df['ulasan_tanpa_stopwords'] = df['ulasan_normalisasi'].apply(remove_stopwords)
print("\n--- Sample reviews after stopword removal ---")
print(df[['ulasan_normalisasi', 'ulasan_tanpa_stopwords']].head())
print("\nStopword removal completed: 'ulasan_tanpa_stopwords' column created.")

# Stemming using Sastrawi
factory = StemmerFactory()
stemmer = factory.create_stemmer()

def perform_stemming(text):
    if not text:
        return ""
    return stemmer.stem(text)

print("\nPerforming stemming... This may take a while...")
df['ulasan_stemmed'] = df['ulasan_tanpa_stopwords'].apply(perform_stemming)
print("\n--- Sample reviews after stemming ---")
print(df[['ulasan_tanpa_stopwords', 'ulasan_stemmed']].head())
print("\nStemming completed: 'ulasan_stemmed' column created.")

# Display final results
print("\n--- Final preprocessing results for sample reviews ---")
for i in range(min(5, len(df))):
    print(f"\nOriginal Review [{i+1}]: {df[COLUMN_REVIEW_TEXT].iloc[i]}")
    print(f"Processed Review [{i+1}]: {df['ulasan_stemmed'].iloc[i]}")

# Save results to Excel file
columns_to_save = [COLUMN_REVIEW_TEXT, 'ulasan_stemmed']
if 'nilai' in df.columns:
    columns_to_save.append('nilai')
if 'app_name' in df.columns:
    columns_to_save.append('app_name')

df_final = df[columns_to_save]

output_file = 'gofood_ulasan_preprocessed.xlsx'
try:
    df_final.to_excel(output_file, index=False, engine='openpyxl')
    print(f"\n✅ Preprocessed data successfully saved to: '{output_file}'")
    print(f"   File contains columns: {df_final.columns.tolist()}")
    print(f"   Total processed reviews: {len(df_final)}")
except Exception as e:
    print(f"\n❌ Failed to save data to {output_file}: {e}")

print("\n🎉 ALL PREPROCESSING COMPLETED!")
print("The 'ulasan_stemmed' column now contains clean data ready for the next stage.")
