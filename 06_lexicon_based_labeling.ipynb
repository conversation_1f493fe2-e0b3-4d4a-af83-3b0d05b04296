{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📖 06. Lexicon-Based Sentiment Labeling\n", "\n", "**Tujuan**: Membuat label sentimen berdasarkan lexicon/kamus sentimen bahasa Indonesia\n", "\n", "**Key Features**:\n", "1. 📚 **Lexicon Loading** - Load Indonesian sentiment lexicon\n", "2. 🔍 **Sentiment Scoring** - <PERSON><PERSON><PERSON><PERSON> skor sentimen per teks\n", "3. 🎯 **Threshold Optimization** - Opti<PERSON><PERSON> <PERSON> klasifikasi\n", "4. ⚖️ **Label Comparison** - Bandingkan dengan score-based labels\n", "5. 📊 **Agreement Analysis** - <PERSON><PERSON><PERSON> k<PERSON>n antar metode\n", "6. 💾 **Results Integration** - Integrasi hasil untuk model training\n", "\n", "**Input**: Dataset terpreproses + Indonesian lexicon  \n", "**Output**: Dataset dengan label sentimen lexicon-based\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ Setup & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import json\n", "import pickle\n", "\n", "# Machine learning libraries\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "from sklearn.metrics import cohen_kappa_score, matthews_corrcoef\n", "import joblib\n", "\n", "# Configuration\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "pd.set_option('display.max_columns', None)\n", "\n", "print(\"📖 Lexicon-based labeling libraries imported successfully!\")\n", "print(f\"🕐 Lexicon labeling started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📂 Load Data & Lexicon Resources"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the preprocessed dataset\n", "try:\n", "    df = pd.read_excel('gofood_advanced_preprocessed.xlsx')\n", "    print(f\"✅ Advanced preprocessed dataset loaded: {len(df)} reviews\")\n", "    text_column = 'final_processed_text'\n", "    \n", "except FileNotFoundError:\n", "    print(\"⚠️ Advanced preprocessed file not found. Trying alternatives...\")\n", "    try:\n", "        df = pd.read_excel('gofood_ulasan_preprocessed.xlsx')\n", "        print(f\"✅ Basic preprocessed dataset loaded: {len(df)} reviews\")\n", "        text_column = 'ulasan_stemmed' if 'ulasan_stemmed' in df.columns else df.columns[0]\n", "    except FileNotFoundError:\n", "        print(\"❌ No preprocessed data found. Creating sample data...\")\n", "        sample_data = {\n", "            'final_processed_text': [\n", "                'makanan enak cepat sampai driver ramah',\n", "                'pelayanan buruk makanan dingin tidak enak',\n", "                'lumayan oke tapi agak lama tunggu',\n", "                'mantap gofood terbaik recommended sekali',\n", "                'aplikasi sering error susah order',\n", "                'makanan segar enak harga terjangkau',\n", "                'driver tidak sopan makanan tumpah',\n", "                'biasa saja tidak istimewa',\n", "                'sangat puas pelayanan excellent',\n", "                'mengecewakan sekali tidak recommended'\n", "            ],\n", "            'nilai': [5, 1, 3, 5, 2, 4, 1, 3, 5, 1]\n", "        }\n", "        df = pd.DataFrame(sample_data)\n", "        text_column = 'final_processed_text'\n", "        print(f\"📝 Sample dataset created with {len(df)} reviews\")\n", "\n", "print(f\"📝 Using text column: {text_column}\")\n", "\n", "# Load Indonesian lexicon resources\n", "try:\n", "    with open('lexicons/indonesian_lexicon_package.pkl', 'rb') as f:\n", "        lexicon_package = pickle.load(f)\n", "    \n", "    slang_dict = lexicon_package['slang_dictionary']\n", "    sentiment_lexicon = lexicon_package['sentiment_lexicon']\n", "    \n", "    print(f\"✅ Indonesian lexicon loaded:\")\n", "    print(f\"  • Slang dictionary: {len(slang_dict)} entries\")\n", "    print(f\"  • Sentiment lexicon: {len(sentiment_lexicon)} entries\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"⚠️ Lexicon package not found. Creating basic lexicon...\")\n", "    \n", "    # Basic Indonesian sentiment lexicon\n", "    sentiment_lexicon = {\n", "        # Positive words\n", "        'enak': 2, 'bagus': 2, 'mantap': 3, 'recommended': 2, 'puas': 2,\n", "        'ramah': 2, 'cepat': 1, 'segar': 1, 'terjangkau': 1, 'excellent': 3,\n", "        'terbaik': 3, 'oke': 1, 'lumayan': 1, 'sopan': 1,\n", "        \n", "        # Negative words\n", "        'buruk': -2, 'dingin': -1, 'lama': -1, 'error': -2, 'susah': -1,\n", "        'tumpah': -1, 'mengecewakan': -3, 'tidak': -1, 'sering': -1,\n", "        'agak': -1\n", "    }\n", "    \n", "    slang_dict = {\n", "        'bgt': 'banget', 'ga': 'tidak', 'gak': 'tidak', 'yg': 'yang',\n", "        'dg': 'dengan', 'utk': 'untuk', 'hrs': 'harus', 'jd': 'jadi'\n", "    }\n", "    \n", "    print(f\"📝 Basic lexicon created:\")\n", "    print(f\"  • Sentiment words: {len(sentiment_lexicon)}\")\n", "    print(f\"  • Slang mappings: {len(slang_dict)}\")\n", "\n", "# Load score-based labels for comparison if available\n", "score_based_labels = None\n", "try:\n", "    score_df = pd.read_excel('data/labeled/gofood_score_based_labeled.xlsx')\n", "    if len(score_df) == len(df):\n", "        score_based_labels = score_df['sentiment_label']\n", "        print(f\"✅ Score-based labels loaded for comparison: {len(score_based_labels)} labels\")\n", "    else:\n", "        print(f\"⚠️ Score-based labels size mismatch: {len(score_df)} vs {len(df)}\")\n", "except FileNotFoundError:\n", "    print(f\"⚠️ Score-based labels not found. Will proceed without comparison.\")\n", "\n", "print(f\"\\n📊 Dataset Overview:\")\n", "print(f\"  • Total reviews: {len(df)}\")\n", "print(f\"  • Text column: {text_column}\")\n", "print(f\"  • Lexicon words: {len(sentiment_lexicon)}\")\n", "print(f\"  • Score-based comparison: {'Available' if score_based_labels is not None else 'Not available'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Lexicon Coverage Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze lexicon coverage on the dataset\n", "print(\"🔍 LEXICON COVERAGE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Combine all texts and analyze vocabulary\n", "all_texts = df[text_column].astype(str).fillna('')\n", "all_words = ' '.join(all_texts).split()\n", "unique_words = set(all_words)\n", "word_freq = Counter(all_words)\n", "\n", "print(f\"\\n📊 Vocabulary Statistics:\")\n", "print(f\"  • Total words: {len(all_words):,}\")\n", "print(f\"  • Unique words: {len(unique_words):,}\")\n", "print(f\"  • Vocabulary richness: {len(unique_words)/len(all_words):.3f}\")\n", "\n", "# Check lexicon coverage\n", "lexicon_words = set(sentiment_lexicon.keys())\n", "covered_words = unique_words & lexicon_words\n", "coverage_percentage = len(covered_words) / len(unique_words) * 100\n", "\n", "print(f\"\\n📚 Lexicon Coverage:\")\n", "print(f\"  • Lexicon size: {len(lexicon_words)} words\")\n", "print(f\"  • Covered words: {len(covered_words)} / {len(unique_words)}\")\n", "print(f\"  • Coverage percentage: {coverage_percentage:.2f}%\")\n", "\n", "# Analyze covered words by frequency\n", "covered_word_freq = {word: word_freq[word] for word in covered_words}\n", "total_covered_occurrences = sum(covered_word_freq.values())\n", "frequency_coverage = total_covered_occurrences / len(all_words) * 100\n", "\n", "print(f\"  • Covered word occurrences: {total_covered_occurrences:,} / {len(all_words):,}\")\n", "print(f\"  • Frequency coverage: {frequency_coverage:.2f}%\")\n", "\n", "# Show most frequent covered words\n", "print(f\"\\n🔝 Top 15 Covered Words by Frequency:\")\n", "sorted_covered = sorted(covered_word_freq.items(), key=lambda x: x[1], reverse=True)\n", "for word, freq in sorted_covered[:15]:\n", "    sentiment_score = sentiment_lexicon[word]\n", "    sentiment_type = \"Positive\" if sentiment_score > 0 else \"Negative\" if sentiment_score < 0 else \"Neutral\"\n", "    print(f\"  {word:<15} : {freq:>4} times, score: {sentiment_score:>2} ({sentiment_type})\")\n", "\n", "# Analyze sentiment distribution in lexicon\n", "positive_words = {k: v for k, v in sentiment_lexicon.items() if v > 0}\n", "negative_words = {k: v for k, v in sentiment_lexicon.items() if v < 0}\n", "neutral_words = {k: v for k, v in sentiment_lexicon.items() if v == 0}\n", "\n", "print(f\"\\n🎯 Lexicon Sentiment Distribution:\")\n", "print(f\"  • Positive words: {len(positive_words)} ({len(positive_words)/len(sentiment_lexicon)*100:.1f}%)\")\n", "print(f\"  • Negative words: {len(negative_words)} ({len(negative_words)/len(sentiment_lexicon)*100:.1f}%)\")\n", "print(f\"  • Neutral words: {len(neutral_words)} ({len(neutral_words)/len(sentiment_lexicon)*100:.1f}%)\")\n", "\n", "# Check coverage by sentiment type\n", "covered_positive = set(positive_words.keys()) & covered_words\n", "covered_negative = set(negative_words.keys()) & covered_words\n", "\n", "print(f\"\\n📈 Coverage by Sentiment Type:\")\n", "print(f\"  • Positive words covered: {len(covered_positive)} / {len(positive_words)} ({len(covered_positive)/len(positive_words)*100:.1f}%)\")\n", "print(f\"  • Negative words covered: {len(covered_negative)} / {len(negative_words)} ({len(covered_negative)/len(negative_words)*100:.1f}%)\")\n", "\n", "# Find most common uncovered words (potential lexicon expansion)\n", "uncovered_words = unique_words - lexicon_words\n", "uncovered_freq = {word: word_freq[word] for word in uncovered_words}\n", "top_uncovered = sorted(uncovered_freq.items(), key=lambda x: x[1], reverse=True)[:20]\n", "\n", "print(f\"\\n❓ Top 20 Uncovered Words (potential lexicon expansion):\")\n", "for word, freq in top_uncovered:\n", "    if len(word) > 2:  # Skip very short words\n", "        print(f\"  {word:<15} : {freq:>4} times\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧮 Lexicon-Based Sentiment Scoring"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Implement lexicon-based sentiment scoring\n", "print(\"🧮 LEXICON-BASED SENTIMENT SCORING\")\n", "print(\"=\" * 50)\n", "\n", "def calculate_lexicon_sentiment(text, slang_dict, sentiment_dict, normalize_slang=True):\n", "    \"\"\"\n", "    Calculate sentiment score using lexicon approach\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        slang_dict (dict): Slang normalization dictionary\n", "        sentiment_dict (dict): Sentiment lexicon dictionary\n", "        normalize_slang (bool): Whether to apply slang normalization\n", "    \n", "    Returns:\n", "        dict: Detailed sentiment analysis results\n", "    \"\"\"\n", "    if not text or pd.isna(text):\n", "        return {\n", "            'sentiment_score': 0.0,\n", "            'positive_score': 0.0,\n", "            'negative_score': 0.0,\n", "            'matched_words': [],\n", "            'word_count': 0,\n", "            'coverage': 0.0,\n", "            'normalized_text': ''\n", "        }\n", "    \n", "    # Normalize slang if requested\n", "    words = str(text).lower().split()\n", "    if normalize_slang:\n", "        normalized_words = [slang_dict.get(word, word) for word in words]\n", "    else:\n", "        normalized_words = words\n", "    \n", "    # Calculate sentiment scores\n", "    positive_score = 0\n", "    negative_score = 0\n", "    matched_words = []\n", "    \n", "    for word in normalized_words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            matched_words.append((word, score))\n", "            \n", "            if score > 0:\n", "                positive_score += score\n", "            elif score < 0:\n", "                negative_score += abs(score)\n", "    \n", "    # Calculate final metrics\n", "    total_sentiment_score = positive_score - negative_score\n", "    word_count = len(normalized_words)\n", "    coverage = len(matched_words) / word_count if word_count > 0 else 0\n", "    \n", "    return {\n", "        'sentiment_score': total_sentiment_score,\n", "        'positive_score': positive_score,\n", "        'negative_score': negative_score,\n", "        'matched_words': matched_words,\n", "        'word_count': word_count,\n", "        'coverage': coverage,\n", "        'normalized_text': ' '.join(normalized_words)\n", "    }\n", "\n", "# Apply lexicon-based scoring to all texts\n", "print(f\"\\n🔄 Calculating sentiment scores for {len(df)} reviews...\")\n", "\n", "sentiment_results = []\n", "for text in df[text_column]:\n", "    result = calculate_lexicon_sentiment(text, slang_dict, sentiment_lexicon)\n", "    sentiment_results.append(result)\n", "\n", "# Extract scores and metrics\n", "df['lexicon_sentiment_score'] = [r['sentiment_score'] for r in sentiment_results]\n", "df['lexicon_positive_score'] = [r['positive_score'] for r in sentiment_results]\n", "df['lexicon_negative_score'] = [r['negative_score'] for r in sentiment_results]\n", "df['lexicon_coverage'] = [r['coverage'] for r in sentiment_results]\n", "df['lexicon_matched_words'] = [len(r['matched_words']) for r in sentiment_results]\n", "\n", "print(f\"✅ Sentiment scoring completed!\")\n", "\n", "# Analyze score distribution\n", "scores = df['lexicon_sentiment_score']\n", "print(f\"\\n📊 Sentiment Score Statistics:\")\n", "print(f\"  • Mean score: {scores.mean():.3f}\")\n", "print(f\"  • Median score: {scores.median():.3f}\")\n", "print(f\"  • Std deviation: {scores.std():.3f}\")\n", "print(f\"  • Min score: {scores.min():.3f}\")\n", "print(f\"  • Max score: {scores.max():.3f}\")\n", "print(f\"  • Zero scores: {(scores == 0).sum()} ({(scores == 0).sum()/len(scores)*100:.1f}%)\")\n", "\n", "# Coverage analysis\n", "coverage = df['lexicon_coverage']\n", "print(f\"\\n📈 Lexicon Coverage per Review:\")\n", "print(f\"  • Mean coverage: {coverage.mean():.3f} ({coverage.mean()*100:.1f}%)\")\n", "print(f\"  • Median coverage: {coverage.median():.3f} ({coverage.median()*100:.1f}%)\")\n", "print(f\"  • Zero coverage: {(coverage == 0).sum()} reviews ({(coverage == 0).sum()/len(coverage)*100:.1f}%)\")\n", "print(f\"  • Full coverage (100%): {(coverage == 1.0).sum()} reviews\")\n", "\n", "# Show examples of scoring\n", "print(f\"\\n📝 SENTIMENT SCORING EXAMPLES:\")\n", "print(\"-\" * 80)\n", "\n", "# Sort by sentiment score for diverse examples\n", "sorted_indices = df['lexicon_sentiment_score'].argsort()\n", "example_indices = [\n", "    sorted_indices.iloc[0],  # Most negative\n", "    sorted_indices.iloc[len(sorted_indices)//4],  # Lower quartile\n", "    sorted_indices.iloc[len(sorted_indices)//2],  # Median\n", "    sorted_indices.iloc[3*len(sorted_indices)//4],  # Upper quartile\n", "    sorted_indices.iloc[-1]  # Most positive\n", "]\n", "\n", "for i, idx in enumerate(example_indices):\n", "    text = df[text_column].iloc[idx]\n", "    score = df['lexicon_sentiment_score'].iloc[idx]\n", "    pos_score = df['lexicon_positive_score'].iloc[idx]\n", "    neg_score = df['lexicon_negative_score'].iloc[idx]\n", "    coverage = df['lexicon_coverage'].iloc[idx]\n", "    matched = sentiment_results[idx]['matched_words']\n", "    \n", "    print(f\"\\n{i+1}. Text: '{text}'\")\n", "    print(f\"   Score: {score:.2f} (Pos: {pos_score:.1f}, Neg: {neg_score:.1f})\")\n", "    print(f\"   Coverage: {coverage:.2f} ({coverage*100:.0f}%)\")\n", "    print(f\"   Matched: {matched}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Threshold Optimization & Classification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optimize thresholds for sentiment classification\n", "print(\"🎯 THRESHOLD OPTIMIZATION & CLASSIFICATION\")\n", "print(\"=\" * 50)\n", "\n", "# Define multiple threshold strategies\n", "def apply_threshold_strategy(scores, strategy='balanced'):\n", "    \"\"\"\n", "    Apply different threshold strategies for classification\n", "    \n", "    Args:\n", "        scores: Array of sentiment scores\n", "        strategy: Threshold strategy name\n", "    \n", "    Returns:\n", "        tuple: (labels, thresholds_used)\n", "    \"\"\"\n", "    scores = np.array(scores)\n", "    \n", "    if strategy == 'zero_centered':\n", "        # Simple: negative < 0, positive > 0, neutral = 0\n", "        labels = np.where(scores > 0, 'Positive', \n", "                         np.where(scores < 0, 'Negative', 'Neutral'))\n", "        thresholds = {'positive': 0, 'negative': 0}\n", "        \n", "    elif strategy == 'balanced':\n", "        # Use percentiles for balanced classes\n", "        pos_threshold = np.percentile(scores, 66.7)  # Top 33%\n", "        neg_threshold = np.percentile(scores, 33.3)  # Bottom 33%\n", "        labels = np.where(scores >= pos_threshold, 'Positive',\n", "                         np.where(scores <= neg_threshold, 'Negative', 'Neutral'))\n", "        thresholds = {'positive': pos_threshold, 'negative': neg_threshold}\n", "        \n", "    elif strategy == 'conservative':\n", "        # Conservative: require stronger signals\n", "        pos_threshold = max(1.0, np.percentile(scores, 75))\n", "        neg_threshold = min(-1.0, np.percentile(scores, 25))\n", "        labels = np.where(scores >= pos_threshold, 'Positive',\n", "                         np.where(scores <= neg_threshold, 'Negative', 'Neutral'))\n", "        thresholds = {'positive': pos_threshold, 'negative': neg_threshold}\n", "        \n", "    elif strategy == 'aggressive':\n", "        # Aggressive: lower thresholds for more classification\n", "        pos_threshold = max(0.5, np.percentile(scores, 60))\n", "        neg_threshold = min(-0.5, np.percentile(scores, 40))\n", "        labels = np.where(scores >= pos_threshold, 'Positive',\n", "                         np.where(scores <= neg_threshold, 'Negative', 'Neutral'))\n", "        thresholds = {'positive': pos_threshold, 'negative': neg_threshold}\n", "        \n", "    elif strategy == 'binary_median':\n", "        # Binary classification using median\n", "        median_score = np.median(scores)\n", "        labels = np.where(scores >= median_score, 'Positive', 'Negative')\n", "        thresholds = {'threshold': median_score}\n", "        \n", "    elif strategy == 'binary_mean':\n", "        # Binary classification using mean\n", "        mean_score = np.mean(scores)\n", "        labels = np.where(scores >= mean_score, 'Positive', 'Negative')\n", "        thresholds = {'threshold': mean_score}\n", "        \n", "    else:\n", "        raise ValueError(f\"Unknown strategy: {strategy}\")\n", "    \n", "    return labels, thresholds\n", "\n", "# Apply all threshold strategies\n", "threshold_strategies = [\n", "    'zero_centered', 'balanced', 'conservative', \n", "    'aggressive', 'binary_median', 'binary_mean'\n", "]\n", "\n", "strategy_results = {}\n", "scores = df['lexicon_sentiment_score'].values\n", "\n", "print(f\"\\n🔄 Testing {len(threshold_strategies)} threshold strategies...\")\n", "\n", "for strategy in threshold_strategies:\n", "    labels, thresholds = apply_threshold_strategy(scores, strategy)\n", "    \n", "    # Calculate statistics\n", "    label_counts = pd.Series(labels).value_counts()\n", "    label_percentages = (label_counts / len(labels) * 100).round(1)\n", "    \n", "    strategy_results[strategy] = {\n", "        'labels': labels,\n", "        'thresholds': thresholds,\n", "        'label_counts': label_counts.to_dict(),\n", "        'label_percentages': label_percentages.to_dict(),\n", "        'num_classes': len(label_counts)\n", "    }\n", "    \n", "    print(f\"\\n📊 {strategy.upper().replace('_', ' ')}:\")\n", "    print(f\"  • Thresholds: {thresholds}\")\n", "    print(f\"  • Classes: {len(label_counts)}\")\n", "    print(f\"  • Distribution:\")\n", "    for label, count in label_counts.items():\n", "        percentage = label_percentages[label]\n", "        print(f\"    - {label}: {count} ({percentage}%)\")\n", "\n", "# Analyze strategy performance\n", "print(f\"\\n📈 STRATEGY COMPARISON:\")\n", "print(f\"{'Strategy':<15} {'Classes':<8} {'Largest %':<10} {'Smallest %':<11} {'Balance':<8}\")\n", "print(\"-\" * 60)\n", "\n", "for strategy, results in strategy_results.items():\n", "    percentages = list(results['label_percentages'].values())\n", "    largest_pct = max(percentages)\n", "    smallest_pct = min(percentages)\n", "    balance_ratio = smallest_pct / largest_pct\n", "    \n", "    print(f\"{strategy:<15} {results['num_classes']:<8} {largest_pct:<10.1f} {smallest_pct:<11.1f} {balance_ratio:<8.3f}\")\n", "\n", "# Select recommended strategy based on balance and interpretability\n", "def score_threshold_strategy(results):\n", "    \"\"\"Score threshold strategy based on balance and interpretability\"\"\"\n", "    percentages = list(results['label_percentages'].values())\n", "    balance_ratio = min(percentages) / max(percentages)\n", "    \n", "    # Prefer balanced distributions and interpretable thresholds\n", "    score = balance_ratio * 100  # Balance score (0-100)\n", "    \n", "    # Bonus for having neutral class (more informative)\n", "    if results['num_classes'] == 3:\n", "        score += 10\n", "    \n", "    # Penalty for extreme imbalance\n", "    if max(percentages) > 80:\n", "        score -= 20\n", "    \n", "    return score\n", "\n", "# Score and rank strategies\n", "strategy_scores = {}\n", "for strategy, results in strategy_results.items():\n", "    score = score_threshold_strategy(results)\n", "    strategy_scores[strategy] = score\n", "\n", "best_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]\n", "best_results = strategy_results[best_strategy]\n", "\n", "print(f\"\\n🏆 RECOMMENDED STRATEGY: {best_strategy.upper().replace('_', ' ')}\")\n", "print(f\"  • Score: {strategy_scores[best_strategy]:.1f}\")\n", "print(f\"  • Thresholds: {best_results['thresholds']}\")\n", "print(f\"  • Distribution: {best_results['label_percentages']}\")\n", "\n", "# Apply recommended strategy to dataset\n", "df['lexicon_sentiment_label'] = best_results['labels']\n", "df['lexicon_threshold_strategy'] = best_strategy\n", "\n", "print(f\"\\n✅ Lexicon-based labels applied using {best_strategy} strategy\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⚖️ Comparison with Score-Based Labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare lexicon-based labels with score-based labels if available\n", "print(\"⚖️ COMPARISON WITH SCORE-BASED LABELS\")\n", "print(\"=\" * 50)\n", "\n", "if score_based_labels is not None:\n", "    # Align datasets for comparison\n", "    lexicon_labels = df['lexicon_sentiment_label']\n", "    \n", "    # Find common labels between both methods\n", "    lexicon_set = set(lexicon_labels.unique())\n", "    score_set = set(score_based_labels.unique())\n", "    common_labels = lexicon_set & score_set\n", "    \n", "    print(f\"\\n📊 Label Set Comparison:\")\n", "    print(f\"  • Lexicon labels: {sorted(lexicon_set)}\")\n", "    print(f\"  • Score-based labels: {sorted(score_set)}\")\n", "    print(f\"  • Common labels: {sorted(common_labels)}\")\n", "    \n", "    # Create comparison for common labels only\n", "    if len(common_labels) >= 2:\n", "        # Filter to common labels\n", "        mask = (lexicon_labels.isin(common_labels)) & (score_based_labels.isin(common_labels))\n", "        lex_filtered = lexicon_labels[mask]\n", "        score_filtered = score_based_labels[mask]\n", "        \n", "        print(f\"\\n🔍 Agreement Analysis ({len(lex_filtered)} comparable samples):\")\n", "        \n", "        # Calculate agreement metrics\n", "        exact_agreement = (lex_filtered == score_filtered).sum()\n", "        agreement_rate = exact_agreement / len(lex_filtered) * 100\n", "        \n", "        print(f\"  • Exact agreement: {exact_agreement} / {len(lex_filtered)} ({agreement_rate:.1f}%)\")\n", "        \n", "        # <PERSON>'s Kappa\n", "        if len(lex_filtered.unique()) > 1 and len(score_filtered.unique()) > 1:\n", "            kappa = cohen_kappa_score(lex_filtered, score_filtered)\n", "            print(f\"  • <PERSON>'s Kappa: {kappa:.3f}\")\n", "            \n", "            if kappa >= 0.8:\n", "                agreement_quality = \"Excellent\"\n", "            elif ka<PERSON> >= 0.6:\n", "                agreement_quality = \"Good\"\n", "            elif ka<PERSON> >= 0.4:\n", "                agreement_quality = \"Moderate\"\n", "            elif ka<PERSON> >= 0.2:\n", "                agreement_quality = \"Fair\"\n", "            else:\n", "                agreement_quality = \"Poor\"\n", "            \n", "            print(f\"  • Agreement quality: {agreement_quality}\")\n", "        \n", "        # Confusion matrix\n", "        print(f\"\\n📋 Confusion Matrix (Lexicon vs Score-based):\")\n", "        conf_matrix = pd.crosstab(lex_filtered, score_filtered, margins=True)\n", "        print(conf_matrix)\n", "        \n", "        # Disagreement analysis\n", "        disagreements = lex_filtered != score_filtered\n", "        if disagreements.sum() > 0:\n", "            print(f\"\\n❌ Disagreement Analysis ({disagreements.sum()} cases):\")\n", "            \n", "            # Show disagreement patterns\n", "            disagree_df = pd.DataFrame({\n", "                'lexicon': lex_filtered[disagreements],\n", "                'score_based': score_filtered[disagreements]\n", "            })\n", "            \n", "            disagree_patterns = disagree_df.groupby(['lexicon', 'score_based']).size().sort_values(ascending=False)\n", "            print(f\"  • Top disagreement patterns:\")\n", "            for (lex, score), count in disagree_patterns.head(5).items():\n", "                print(f\"    - Lexicon: {lex} → Score: {score} ({count} cases)\")\n", "            \n", "            # Show example disagreements\n", "            print(f\"\\n📝 Example Disagreements:\")\n", "            disagree_indices = df[mask][disagreements].index[:5]\n", "            for idx in disagree_indices:\n", "                text = df.loc[idx, text_column]\n", "                lex_label = df.loc[idx, 'lexicon_sentiment_label']\n", "                score_label = score_based_labels.loc[idx] if idx in score_based_labels.index else 'N/A'\n", "                lex_score = df.loc[idx, 'lexicon_sentiment_score']\n", "                \n", "                print(f\"  Text: '{text[:60]}...'\")\n", "                print(f\"  Lexicon: {lex_label} (score: {lex_score:.2f}) | Score-based: {score_label}\")\n", "                print()\n", "    \n", "    else:\n", "        print(f\"\\n⚠️ Insufficient common labels for meaningful comparison\")\n", "        print(f\"   Consider using compatible labeling strategies\")\n", "\n", "else:\n", "    print(f\"\\n⚠️ Score-based labels not available for comparison\")\n", "    print(f\"   Run score-based labeling first for comparative analysis\")\n", "\n", "# Method-specific analysis\n", "print(f\"\\n📊 LEXICON-BASED METHOD ANALYSIS:\")\n", "lex_labels = df['lexicon_sentiment_label']\n", "lex_scores = df['lexicon_sentiment_score']\n", "lex_coverage = df['lexicon_coverage']\n", "\n", "print(f\"\\n🎯 Label Distribution:\")\n", "label_dist = lex_labels.value_counts()\n", "for label, count in label_dist.items():\n", "    percentage = count / len(lex_labels) * 100\n", "    print(f\"  • {label}: {count} ({percentage:.1f}%)\")\n", "\n", "print(f\"\\n📈 Score vs Coverage Correlation:\")\n", "score_coverage_corr = np.corrcoef(lex_scores, lex_coverage)[0, 1]\n", "print(f\"  • Correlation: {score_coverage_corr:.3f}\")\n", "\n", "# Analyze confidence by coverage\n", "high_coverage = lex_coverage >= 0.3  # 30% or more words matched\n", "low_coverage = lex_coverage < 0.1   # Less than 10% words matched\n", "\n", "print(f\"\\n🔍 Confidence Analysis by Coverage:\")\n", "print(f\"  • High coverage (≥30%): {high_coverage.sum()} samples ({high_coverage.sum()/len(df)*100:.1f}%)\")\n", "print(f\"  • Low coverage (<10%): {low_coverage.sum()} samples ({low_coverage.sum()/len(df)*100:.1f}%)\")\n", "\n", "if high_coverage.sum() > 0:\n", "    high_cov_labels = lex_labels[high_coverage].value_counts()\n", "    print(f\"  • High coverage distribution: {dict(high_cov_labels)}\")\n", "\n", "if low_coverage.sum() > 0:\n", "    low_cov_labels = lex_labels[low_coverage].value_counts()\n", "    print(f\"  • Low coverage distribution: {dict(low_cov_labels)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 Save Lexicon-Based Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save all lexicon-based results\n", "print(\"💾 SAVING LEXICON-BASED RESULTS\")\n", "print(\"=\" * 50)\n", "\n", "# Create directories\n", "import os\n", "os.makedirs('data/labeled', exist_ok=True)\n", "os.makedirs('data/analysis', exist_ok=True)\n", "\n", "# Prepare final dataset\n", "df_lexicon = df.copy()\n", "\n", "# Add numeric labels for ML\n", "le_lexicon = LabelEncoder()\n", "df_lexicon['lexicon_sentiment_numeric'] = le_lexicon.fit_transform(df_lexicon['lexicon_sentiment_label'])\n", "lexicon_label_mapping = dict(zip(le_lexicon.classes_, le_lexicon.transform(le_lexicon.classes_)))\n", "\n", "# Save main lexicon-based dataset\n", "lexicon_output = 'data/labeled/gofood_lexicon_based_labeled.xlsx'\n", "df_lexicon.to_excel(lexicon_output, index=False, engine='openpyxl')\n", "print(f\"✅ Lexicon-based dataset: {lexicon_output}\")\n", "print(f\"   • Samples: {len(df_lexicon)}\")\n", "print(f\"   • Strategy: {best_strategy}\")\n", "print(f\"   • Labels: {list(lexicon_label_mapping.keys())}\")\n", "\n", "# Save threshold strategy analysis\n", "threshold_analysis_data = []\n", "for strategy, results in strategy_results.items():\n", "    row = {\n", "        'strategy': strategy,\n", "        'num_classes': results['num_classes'],\n", "        'thresholds': str(results['thresholds']),\n", "        'score': strategy_scores.get(strategy, 0)\n", "    }\n", "    \n", "    # Add class distribution\n", "    for label, count in results['label_counts'].items():\n", "        row[f'{label.lower()}_count'] = count\n", "        row[f'{label.lower()}_percentage'] = results['label_percentages'][label]\n", "    \n", "    threshold_analysis_data.append(row)\n", "\n", "threshold_df = pd.DataFrame(threshold_analysis_data)\n", "threshold_analysis_file = 'data/analysis/lexicon_threshold_analysis.xlsx'\n", "threshold_df.to_excel(threshold_analysis_file, index=False, engine='openpyxl')\n", "print(f\"\\n✅ Threshold analysis: {threshold_analysis_file}\")\n", "\n", "# Save lexicon artifacts\n", "lexicon_artifacts = {\n", "    'label_encoder': le_lexicon,\n", "    'label_mapping': lexicon_label_mapping,\n", "    'sentiment_lexicon': sentiment_lexicon,\n", "    'slang_dictionary': slang_dict,\n", "    'threshold_strategy': best_strategy,\n", "    'thresholds': best_results['thresholds'],\n", "    'strategy_results': strategy_results,\n", "    'creation_date': datetime.now().isoformat(),\n", "    'dataset_info': {\n", "        'total_samples': len(df_lexicon),\n", "        'text_column': text_column,\n", "        'lexicon_size': len(sentiment_lexicon),\n", "        'coverage_stats': {\n", "            'mean_coverage': df_lexicon['lexicon_coverage'].mean(),\n", "            'median_coverage': df_lexicon['lexicon_coverage'].median(),\n", "            'zero_coverage_count': (df_lexicon['lexicon_coverage'] == 0).sum()\n", "        }\n", "    }\n", "}\n", "\n", "lexicon_artifacts_file = 'data/labeled/lexicon_based_artifacts.pkl'\n", "joblib.dump(lexicon_artifacts, lexicon_artifacts_file)\n", "print(f\"\\n✅ Lexicon artifacts: {lexicon_artifacts_file}\")\n", "\n", "# Save comprehensive summary\n", "lexicon_summary = {\n", "    'labeling_method': 'lexicon_based',\n", "    'creation_date': datetime.now().isoformat(),\n", "    'dataset_summary': {\n", "        'total_samples': len(df_lexicon),\n", "        'text_column': text_column,\n", "        'lexicon_size': len(sentiment_lexicon),\n", "        'slang_dictionary_size': len(slang_dict)\n", "    },\n", "    'lexicon_coverage': {\n", "        'vocabulary_coverage_pct': coverage_percentage,\n", "        'frequency_coverage_pct': frequency_coverage,\n", "        'mean_review_coverage': df_lexicon['lexicon_coverage'].mean(),\n", "        'zero_coverage_reviews': (df_lexicon['lexicon_coverage'] == 0).sum()\n", "    },\n", "    'sentiment_scoring': {\n", "        'score_range': [float(df_lexicon['lexicon_sentiment_score'].min()), \n", "                       float(df_lexicon['lexicon_sentiment_score'].max())],\n", "        'mean_score': float(df_lexicon['lexicon_sentiment_score'].mean()),\n", "        'median_score': float(df_lexicon['lexicon_sentiment_score'].median()),\n", "        'zero_scores': int((df_lexicon['lexicon_sentiment_score'] == 0).sum())\n", "    },\n", "    'recommended_strategy': {\n", "        'name': best_strategy,\n", "        'score': strategy_scores[best_strategy],\n", "        'thresholds': best_results['thresholds'],\n", "        'class_distribution': best_results['label_percentages']\n", "    },\n", "    'comparison_with_score_based': {\n", "        'available': score_based_labels is not None,\n", "        'agreement_rate': float(agreement_rate) if 'agreement_rate' in locals() else None,\n", "        'kappa_score': float(kappa) if 'kappa' in locals() else None\n", "    },\n", "    'files_generated': {\n", "        'labeled_dataset': lexicon_output,\n", "        'threshold_analysis': threshold_analysis_file,\n", "        'artifacts': lexicon_artifacts_file\n", "    }\n", "}\n", "\n", "lexicon_summary_file = 'data/analysis/lexicon_based_summary.json'\n", "with open(lexicon_summary_file, 'w', encoding='utf-8') as f:\n", "    json.dump(lexicon_summary, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"\\n✅ Summary report: {lexicon_summary_file}\")\n", "\n", "# Create utility functions\n", "lexicon_utils_code = '''\n", "import pandas as pd\n", "import joblib\n", "import json\n", "import numpy as np\n", "\n", "def load_lexicon_based_labels(file_path='data/labeled/gofood_lexicon_based_labeled.xlsx'):\n", "    \"\"\"Load the lexicon-based labeled dataset\"\"\"\n", "    return pd.read_excel(file_path)\n", "\n", "def load_lexicon_artifacts(file_path='data/labeled/lexicon_based_artifacts.pkl'):\n", "    \"\"\"Load lexicon and related artifacts\"\"\"\n", "    return joblib.load(file_path)\n", "\n", "def calculate_sentiment_score(text, sentiment_lexicon, slang_dict=None):\n", "    \"\"\"Calculate sentiment score for new text using lexicon\"\"\"\n", "    if not text:\n", "        return 0.0\n", "    \n", "    words = str(text).lower().split()\n", "    if slang_dict:\n", "        words = [slang_dict.get(word, word) for word in words]\n", "    \n", "    total_score = 0\n", "    for word in words:\n", "        if word in sentiment_lexicon:\n", "            total_score += sentiment_lexicon[word]\n", "    \n", "    return total_score\n", "\n", "def apply_lexicon_threshold(scores, strategy='balanced'):\n", "    \"\"\"Apply threshold strategy to sentiment scores\"\"\"\n", "    scores = np.array(scores)\n", "    \n", "    if strategy == 'zero_centered':\n", "        return np.where(scores > 0, 'Positive', \n", "                       np.where(scores < 0, 'Negative', 'Neutral'))\n", "    elif strategy == 'balanced':\n", "        pos_threshold = np.percentile(scores, 66.7)\n", "        neg_threshold = np.percentile(scores, 33.3)\n", "        return np.where(scores >= pos_threshold, 'Positive',\n", "                       np.where(scores <= neg_threshold, 'Negative', 'Neutral'))\n", "    else:\n", "        # Default to zero-centered\n", "        return np.where(scores > 0, 'Positive', \n", "                       np.where(scores < 0, 'Negative', 'Neutral'))\n", "\n", "def get_lexicon_info():\n", "    \"\"\"Get information about the lexicon-based labeling\"\"\"\n", "    with open('data/analysis/lexicon_based_summary.json', 'r') as f:\n", "        return json.load(f)\n", "'''\n", "\n", "with open('utils/lexicon_based_labeling.py', 'w', encoding='utf-8') as f:\n", "    f.write(lexicon_utils_code)\n", "\n", "print(f\"\\n🔧 Utility functions: utils/lexicon_based_labeling.py\")\n", "\n", "print(f\"\\n📊 FILES GENERATED SUMMARY:\")\n", "print(f\"  📁 data/labeled/\")\n", "print(f\"     • gofood_lexicon_based_labeled.xlsx - Main labeled dataset\")\n", "print(f\"     • lexicon_based_artifacts.pkl - Lexicon & artifacts\")\n", "print(f\"  📁 data/analysis/\")\n", "print(f\"     • lexicon_threshold_analysis.xlsx - Threshold comparison\")\n", "print(f\"     • lexicon_based_summary.json - Complete summary\")\n", "print(f\"  🔧 utils/lexicon_based_labeling.py - Utility functions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📋 Summary & Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "print(\"📋 LEXICON-BASED LABELING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📖 LEXICON-BASED LABELING COMPLETED:\")\n", "print(f\"  • Dataset size: {len(df_lexicon)} reviews\")\n", "print(f\"  • Text column: {text_column}\")\n", "print(f\"  • Lexicon size: {len(sentiment_lexicon)} words\")\n", "print(f\"  • Slang dictionary: {len(slang_dict)} mappings\")\n", "\n", "print(f\"\\n📊 LEXICON COVERAGE:\")\n", "print(f\"  • Vocabulary coverage: {coverage_percentage:.1f}%\")\n", "print(f\"  • Frequency coverage: {frequency_coverage:.1f}%\")\n", "print(f\"  • Mean review coverage: {df_lexicon['lexicon_coverage'].mean()*100:.1f}%\")\n", "print(f\"  • Zero coverage reviews: {(df_lexicon['lexicon_coverage'] == 0).sum()}\")\n", "\n", "print(f\"\\n🎯 SENTIMENT SCORING:\")\n", "print(f\"  • Score range: {df_lexicon['lexicon_sentiment_score'].min():.1f} to {df_lexicon['lexicon_sentiment_score'].max():.1f}\")\n", "print(f\"  • Mean score: {df_lexicon['lexicon_sentiment_score'].mean():.3f}\")\n", "print(f\"  • Median score: {df_lexicon['lexicon_sentiment_score'].median():.3f}\")\n", "print(f\"  • Zero scores: {(df_lexicon['lexicon_sentiment_score'] == 0).sum()} ({(df_lexicon['lexicon_sentiment_score'] == 0).sum()/len(df_lexicon)*100:.1f}%)\")\n", "\n", "print(f\"\\n🏆 RECOMMENDED THRESHOLD STRATEGY: {best_strategy.upper().replace('_', ' ')}\")\n", "print(f\"  • Strategy score: {strategy_scores[best_strategy]:.1f}\")\n", "print(f\"  • Thresholds: {best_results['thresholds']}\")\n", "print(f\"  • Class distribution:\")\n", "for label, percentage in best_results['label_percentages'].items():\n", "    count = best_results['label_counts'][label]\n", "    print(f\"    - {label}: {count} ({percentage}%)\")\n", "\n", "if 'agreement_rate' in locals():\n", "    print(f\"\\n⚖️ COMPARISON WITH SCORE-BASED:\")\n", "    print(f\"  • Agreement rate: {agreement_rate:.1f}%\")\n", "    if 'kappa' in locals():\n", "        print(f\"  • <PERSON>'s Kappa: {kappa:.3f} ({agreement_quality})\")\n", "    print(f\"  • Disagreements: {disagreements.sum() if 'disagreements' in locals() else 'N/A'} cases\")\n", "else:\n", "    print(f\"\\n⚠️ No score-based comparison available\")\n", "\n", "print(f\"\\n📈 QUALITY METRICS:\")\n", "high_confidence = (df_lexicon['lexicon_coverage'] >= 0.3).sum()\n", "low_confidence = (df_lexicon['lexicon_coverage'] < 0.1).sum()\n", "print(f\"  • High confidence samples (≥30% coverage): {high_confidence} ({high_confidence/len(df_lexicon)*100:.1f}%)\")\n", "print(f\"  • Low confidence samples (<10% coverage): {low_confidence} ({low_confidence/len(df_lexicon)*100:.1f}%)\")\n", "print(f\"  • Lexicon utilization: {len(covered_words)}/{len(sentiment_lexicon)} words ({len(covered_words)/len(sentiment_lexicon)*100:.1f}%)\")\n", "\n", "print(f\"\\n💾 OUTPUT FILES:\")\n", "print(f\"  • Main dataset: data/labeled/gofood_lexicon_based_labeled.xlsx\")\n", "print(f\"  • Threshold analysis: data/analysis/lexicon_threshold_analysis.xlsx\")\n", "print(f\"  • Summary: data/analysis/lexicon_based_summary.json\")\n", "print(f\"  • Artifacts: data/labeled/lexicon_based_artifacts.pkl\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"  1. Combine score-based and lexicon-based labels for ensemble\")\n", "print(f\"  2. Random Forest model training (07_random_forest_models.ipynb)\")\n", "print(f\"  3. XGBoost model training (08_xgboost_models.ipynb)\")\n", "print(f\"  4. Model comparison and evaluation\")\n", "\n", "print(f\"\\n✅ LEXICON-BASED LABELING COMPLETED!\")\n", "print(f\"📅 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"\\n🚀 Ready for machine learning model training phase!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}